# =============================================================================
# VESLINT Backend .dockerignore
# =============================================================================
# Exclude files and directories from Docker build context to improve build performance

# =============================================================================
# Python Cache and Virtual Environments
# =============================================================================
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# =============================================================================
# Virtual Environments
# =============================================================================
venv/
env/
ENV/
.venv/
.env/

# =============================================================================
# Environment Files
# =============================================================================
.env
.env.local
.env.development
.env.test
.env.production

# =============================================================================
# IDE and Editor Files
# =============================================================================
.vscode/
.idea/
*.swp
*.swo
*~
.spyderproject
.spyproject

# =============================================================================
# OS Generated Files
# =============================================================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# =============================================================================
# Git Files
# =============================================================================
.git
.gitignore
.gitattributes

# =============================================================================
# Testing and Coverage
# =============================================================================
.coverage
.pytest_cache/
.tox/
.nox/
coverage.xml
*.cover
.hypothesis/
htmlcov/

# =============================================================================
# Jupyter Notebook
# =============================================================================
.ipynb_checkpoints

# =============================================================================
# Documentation
# =============================================================================
README.md
CHANGELOG.md
docs/
*.md

# =============================================================================
# Docker Files (avoid recursive Docker builds)
# =============================================================================
Dockerfile*
docker-compose*.yml
.dockerignore

# =============================================================================
# Logs
# =============================================================================
*.log
logs/

# =============================================================================
# Database Files
# =============================================================================
*.db
*.sqlite
*.sqlite3

# =============================================================================
# Temporary Files
# =============================================================================
.tmp/
temp/
tmp/

# =============================================================================
# MyPy
# =============================================================================
.mypy_cache/
.dmypy.json
dmypy.json

# =============================================================================
# Pyre
# =============================================================================
.pyre/

# =============================================================================
# Celery
# =============================================================================
celerybeat-schedule
celerybeat.pid

# =============================================================================
# SageMath
# =============================================================================
*.sage.py

# =============================================================================
# Rope
# =============================================================================
.ropeproject

# =============================================================================
# mkdocs
# =============================================================================
/site

# =============================================================================
# Miscellaneous
# =============================================================================
.cache/
.sass-cache/
