"""
Logging configuration for the VESLINT API backend.

This module provides structured logging with JSON formatting, request tracking,
and performance monitoring. Optimized for cloud deployment on Render.
"""

import json
import logging
import logging.config
import os
import sys
import time
import uuid
from contextvars import ContextVar
from datetime import datetime, timezone
from typing import Any, Dict, Optional

from ..utils.constants import LOG_LEVEL, ENVIRONMENT

# Context variables for request tracking
request_id_ctx: ContextVar[Optional[str]] = ContextVar('request_id', default=None)
user_id_ctx: ContextVar[Optional[str]] = ContextVar('user_id', default=None)


class JSONFormatter(logging.Formatter):
    """
    JSON formatter for structured logging.
    
    Outputs logs in JSON format suitable for cloud logging platforms
    and log aggregation services.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.hostname = os.getenv('RENDER_INSTANCE_ID', 'local')
        self.service_name = 'veslint-api'
        self.version = os.getenv('RENDER_GIT_COMMIT', 'dev')[:8]

    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON."""
        # Basic log entry structure
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created, tz=timezone.utc).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'service': self.service_name,
            'version': self.version,
            'hostname': self.hostname,
        }

        # Add request context if available
        request_id = request_id_ctx.get()
        user_id = user_id_ctx.get()
        
        if request_id:
            log_entry['request_id'] = request_id
        if user_id:
            log_entry['user_id'] = user_id

        # Add exception information if present
        if record.exc_info:
            log_entry['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': self.formatException(record.exc_info)
            }

        # Add extra fields from log record
        extra_fields = {}
        for key, value in record.__dict__.items():
            if key not in {
                'name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                'filename', 'module', 'lineno', 'funcName', 'created',
                'msecs', 'relativeCreated', 'thread', 'threadName',
                'processName', 'process', 'stack_info', 'exc_info', 'exc_text'
            }:
                # Ensure the value is JSON serializable
                try:
                    json.dumps(value)
                    extra_fields[key] = value
                except (TypeError, ValueError):
                    extra_fields[key] = str(value)

        if extra_fields:
            log_entry['extra'] = extra_fields

        return json.dumps(log_entry)


class ColoredFormatter(logging.Formatter):
    """
    Colored formatter for local development.
    
    Provides colored output for better readability during development.
    """
    
    # Color codes
    COLORS = {
        'DEBUG': '\033[36m',      # Cyan
        'INFO': '\033[32m',       # Green
        'WARNING': '\033[33m',    # Yellow
        'ERROR': '\033[31m',      # Red
        'CRITICAL': '\033[1;31m', # Bold Red
        'RESET': '\033[0m'        # Reset
    }

    def format(self, record: logging.LogRecord) -> str:
        """Format log record with colors."""
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        reset = self.COLORS['RESET']
        
        # Format timestamp
        timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S')
        
        # Format level with color
        level = f"{color}{record.levelname:8}{reset}"
        
        # Format logger name (abbreviated)
        logger_name = record.name.split('.')[-1] if '.' in record.name else record.name
        logger_name = f"{logger_name:15}"
        
        # Format location
        location = f"{record.filename}:{record.lineno}"
        
        # Get request ID if available
        request_id = request_id_ctx.get()
        request_info = f" [{request_id[:8]}]" if request_id else ""
        
        # Format message
        message = record.getMessage()
        
        # Combine all parts
        formatted = f"{timestamp} {level} {logger_name} {location:25}{request_info} {message}"
        
        # Add exception info if present
        if record.exc_info:
            formatted += f"\n{self.formatException(record.exc_info)}"
            
        return formatted


class PerformanceFilter(logging.Filter):
    """
    Filter to add performance metrics to log records.
    
    Tracks request duration and other performance metrics.
    """
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Add performance metrics to the log record."""
        # Add process information
        record.pid = os.getpid()
        
        # Add memory usage if available
        try:
            import psutil
            process = psutil.Process()
            record.memory_mb = round(process.memory_info().rss / 1024 / 1024, 2)
        except ImportError:
            record.memory_mb = None
            
        return True


class HealthCheckFilter(logging.Filter):
    """
    Filter to reduce noise from health check requests.
    
    Prevents health check endpoints from cluttering logs.
    """
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Filter out health check logs."""
        message = record.getMessage().lower()
        
        # Skip health check logs in production
        if ENVIRONMENT == 'production':
            health_check_patterns = [
                '/health',
                '/ping',
                'health check',
                'uvicorn.access'
            ]
            
            for pattern in health_check_patterns:
                if pattern in message:
                    return False
                    
        return True


def get_logger(name: str) -> logging.Logger:
    """
    Get a configured logger instance.
    
    Args:
        name: Logger name (typically __name__)
        
    Returns:
        Configured logger instance
    """
    return logging.getLogger(name)


def setup_logging() -> None:
    """
    Setup application logging configuration.
    
    Configures different logging formats based on environment:
    - Development: Colored console output
    - Production: JSON structured logging
    """
    
    # Determine log level
    log_level = getattr(logging, LOG_LEVEL.upper(), logging.INFO)
    
    # Configure root logger
    logging.root.setLevel(log_level)
    
    # Remove existing handlers
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    
    # Choose formatter based on environment
    if ENVIRONMENT == 'production':
        formatter = JSONFormatter()
    else:
        formatter = ColoredFormatter()
    
    console_handler.setFormatter(formatter)
    
    # Add filters
    console_handler.addFilter(PerformanceFilter())
    console_handler.addFilter(HealthCheckFilter())
    
    # Add handler to root logger
    logging.root.addHandler(console_handler)
    
    # Configure specific loggers
    configure_external_loggers()
    
    # Log startup message
    logger = get_logger(__name__)
    logger.info(
        "Logging configured",
        extra={
            'environment': ENVIRONMENT,
            'log_level': LOG_LEVEL,
            'formatter': formatter.__class__.__name__
        }
    )


def configure_external_loggers() -> None:
    """Configure external library loggers."""
    
    # Reduce noise from external libraries
    external_loggers = {
        'urllib3.connectionpool': logging.WARNING,
        'requests.packages.urllib3': logging.WARNING,
        'httpx': logging.WARNING,
        'uvicorn.access': logging.INFO if ENVIRONMENT == 'development' else logging.WARNING,
        'uvicorn.error': logging.INFO,
        'fastapi': logging.INFO,
        'supabase': logging.WARNING,
        'httpcore': logging.WARNING,
    }
    
    for logger_name, level in external_loggers.items():
        logger = logging.getLogger(logger_name)
        logger.setLevel(level)


def set_request_context(request_id: Optional[str] = None, user_id: Optional[str] = None) -> str:
    """
    Set request context for logging.
    
    Args:
        request_id: Unique request identifier
        user_id: User identifier
        
    Returns:
        The request ID (generated if not provided)
    """
    if request_id is None:
        request_id = str(uuid.uuid4())
    
    request_id_ctx.set(request_id)
    if user_id:
        user_id_ctx.set(user_id)
        
    return request_id


def clear_request_context() -> None:
    """Clear request context."""
    request_id_ctx.set(None)
    user_id_ctx.set(None)


def log_function_call(func_name: str, **kwargs) -> None:
    """
    Log function call with parameters.
    
    Args:
        func_name: Name of the function being called
        **kwargs: Function parameters to log
    """
    logger = get_logger('function_calls')
    logger.debug(f"Calling {func_name}", extra={'function': func_name, 'parameters': kwargs})


def log_performance(operation: str, duration_ms: float, **metrics) -> None:
    """
    Log performance metrics.
    
    Args:
        operation: Name of the operation
        duration_ms: Duration in milliseconds
        **metrics: Additional performance metrics
    """
    logger = get_logger('performance')
    logger.info(
        f"Performance: {operation}",
        extra={
            'operation': operation,
            'duration_ms': duration_ms,
            'metrics': metrics
        }
    )


def log_api_request(method: str, path: str, status_code: int, duration_ms: float, **extra) -> None:
    """
    Log API request.
    
    Args:
        method: HTTP method
        path: Request path
        status_code: HTTP status code
        duration_ms: Request duration in milliseconds
        **extra: Additional request information
    """
    logger = get_logger('api')
    
    # Determine log level based on status code
    if status_code >= 500:
        log_level = logging.ERROR
    elif status_code >= 400:
        log_level = logging.WARNING
    else:
        log_level = logging.INFO
    
    logger.log(
        log_level,
        f"{method} {path} {status_code}",
        extra={
            'http_method': method,
            'http_path': path,
            'http_status': status_code,
            'duration_ms': duration_ms,
            **extra
        }
    )


def log_job_event(job_id: str, event: str, **details) -> None:
    """
    Log job processing event.
    
    Args:
        job_id: Job identifier
        event: Event type (created, started, completed, failed, etc.)
        **details: Additional event details
    """
    logger = get_logger('jobs')
    logger.info(
        f"Job {event}: {job_id}",
        extra={
            'job_id': job_id,
            'event': event,
            'details': details
        }
    )


def log_ml_event(operation: str, vessel_count: int, duration_ms: float, **metrics) -> None:
    """
    Log machine learning operation.
    
    Args:
        operation: ML operation (preprocessing, prediction, etc.)
        vessel_count: Number of vessels processed
        duration_ms: Operation duration in milliseconds
        **metrics: Additional ML metrics
    """
    logger = get_logger('ml')
    logger.info(
        f"ML {operation}: {vessel_count} vessels",
        extra={
            'ml_operation': operation,
            'vessel_count': vessel_count,
            'duration_ms': duration_ms,
            'metrics': metrics
        }
    )


class TimerContext:
    """
    Context manager for timing operations.
    
    Usage:
        with TimerContext('my_operation') as timer:
            # do something
            pass
        # Automatically logs the duration
    """
    
    def __init__(self, operation: str, logger_name: str = 'timer'):
        self.operation = operation
        self.logger = get_logger(logger_name)
        self.start_time = None
        self.end_time = None
    
    def __enter__(self) -> 'TimerContext':
        self.start_time = time.perf_counter()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        self.end_time = time.perf_counter()
        duration_ms = (self.end_time - self.start_time) * 1000
        
        if exc_type is None:
            self.logger.info(
                f"Operation completed: {self.operation}",
                extra={
                    'operation': self.operation,
                    'duration_ms': round(duration_ms, 2),
                    'success': True
                }
            )
        else:
            self.logger.error(
                f"Operation failed: {self.operation}",
                extra={
                    'operation': self.operation,
                    'duration_ms': round(duration_ms, 2),
                    'success': False,
                    'error_type': exc_type.__name__,
                    'error_message': str(exc_val)
                }
            )
    
    @property
    def duration_ms(self) -> Optional[float]:
        """Get operation duration in milliseconds."""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time) * 1000
        return None


def timer(operation: str, logger_name: str = 'timer'):
    """
    Decorator for timing function calls.
    
    Args:
        operation: Operation name for logging
        logger_name: Logger name to use
        
    Usage:
        @timer('my_function')
        def my_function():
            pass
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            with TimerContext(operation, logger_name):
                return func(*args, **kwargs)
        return wrapper
    return decorator


# Initialize logging when module is imported
if not logging.root.handlers:
    setup_logging()
