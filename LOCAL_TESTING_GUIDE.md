# 🧪 VESLINT Local Testing Guide

## 📋 Project Status Overview

✅ **Project Structure**: Complete and well-organized  
✅ **Backend (FastAPI)**: Ready with all dependencies  
✅ **Frontend (Next.js 14)**: Modern App Router structure  
✅ **Database (Supabase)**: Complete schema and migrations  
✅ **ML Model**: Feature schema and inference setup  
✅ **Docker**: Full containerized environment  
✅ **Scripts**: Comprehensive automation  

## 🔧 Prerequisites

### Required Software
- **Node.js**: 16+ (recommended: 18.18.0)
- **Python**: 3.8+ 
- **Docker & Docker Compose**: Latest version
- **Git**: Latest version

### Optional Tools
- **VS Code**: For development
- **Supabase CLI**: For database management

## 🚀 Quick Start (Recommended)

### Option 1: Docker Compose (Easiest)

```bash
# 1. Clone and navigate to project
cd VESLINT

# 2. Install missing frontend dependencies
cd frontend
npm install @mui/material @mui/icons-material framer-motion
cd ..

# 3. Start all services with Docker
docker-compose up -d

# 4. Wait for services to start (2-3 minutes)
docker-compose logs -f

# 5. Access the application
# Frontend: http://localhost:3000
# Backend API: http://localhost:8000
# API Docs: http://localhost:8000/docs
# Supabase Studio: http://localhost:54323
```

### Option 2: Manual Setup (Full Control)

```bash
# 1. Install frontend dependencies
cd frontend
npm install
cd ..

# 2. Set up Python environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r backend/requirements.txt

# 3. Set up database (Supabase local)
npm install -g supabase
supabase start

# 4. Run development servers
# Terminal 1 - Backend
cd backend
source ../venv/bin/activate
uvicorn src.main:app --reload --host 0.0.0.0 --port 8000

# Terminal 2 - Frontend  
cd frontend
npm run dev

# Terminal 3 - Database
supabase start
```

## 🧪 Testing Checklist

### 1. Health Checks

```bash
# Check backend health
curl http://localhost:8000/health

# Check frontend
curl http://localhost:3000

# Check database
curl http://localhost:3001/rest/v1/
```

### 2. Frontend Testing

```bash
cd frontend

# Install dependencies (if not done)
npm install

# Type checking
npm run type-check

# Linting
npm run lint

# Build test
npm run build

# Start production build
npm start
```

### 3. Backend Testing

```bash
cd backend
source ../venv/bin/activate  # On Windows: ..\venv\Scripts\activate

# Run tests
python -m pytest tests/ -v

# Code formatting check
python -m black --check src/

# Linting
python -m flake8 src/

# Type checking
python -m mypy src/
```

### 4. Database Testing

```bash
# Check Supabase status
supabase status

# Run migrations
supabase db reset

# Seed database
psql -h localhost -p 5432 -U postgres -d postgres -f database/seed.sql
```

### 5. Integration Testing

```bash
# Test file upload (replace with actual CSV file)
curl -X POST "http://localhost:8000/api/v1/jobs" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@sample_ais_data.csv"

# Test job status
curl "http://localhost:8000/api/v1/jobs/{job_id}"

# Test vessel classification
curl "http://localhost:8000/api/v1/vessels/classify" \
  -H "Content-Type: application/json" \
  -d '{"features": {"speed": 10, "course": 180}}'
```

## 🐛 Common Issues & Solutions

### Issue: Frontend Dependencies Missing
```bash
cd frontend
npm install @mui/material @mui/icons-material framer-motion
```

### Issue: Python Dependencies
```bash
cd backend
pip install -r requirements.txt
```

### Issue: Docker Services Not Starting
```bash
# Clean up and restart
docker-compose down -v
docker-compose build --no-cache
docker-compose up -d
```

### Issue: Port Conflicts
```bash
# Check what's using ports
netstat -tulpn | grep :3000
netstat -tulpn | grep :8000
netstat -tulpn | grep :5432

# Kill processes if needed
sudo kill -9 <PID>
```

### Issue: Database Connection
```bash
# Reset Supabase
supabase stop
supabase start
```

## 📊 Performance Testing

### Load Testing Backend
```bash
# Install Apache Bench
sudo apt-get install apache2-utils  # Ubuntu
brew install httpie  # macOS

# Test health endpoint
ab -n 100 -c 10 http://localhost:8000/health

# Test API endpoints
ab -n 50 -c 5 http://localhost:8000/api/v1/jobs
```

### Frontend Performance
```bash
cd frontend

# Build and analyze bundle
npm run build
npx @next/bundle-analyzer
```

## 🔍 Monitoring & Logs

### Docker Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f veslint-backend
docker-compose logs -f veslint-frontend
docker-compose logs -f supabase-db
```

### Application Logs
```bash
# Backend logs
tail -f backend/logs/app.log

# Frontend logs (in browser console)
# Open browser dev tools -> Console tab
```

## ✅ Success Criteria

Your local environment is working correctly when:

1. ✅ All health checks return 200 OK
2. ✅ Frontend loads at http://localhost:3000
3. ✅ Backend API docs accessible at http://localhost:8000/docs
4. ✅ Database accessible via Supabase Studio
5. ✅ File upload works through frontend
6. ✅ Job processing completes successfully
7. ✅ Results display correctly in dashboard

## 🚀 Next Steps

Once local testing is complete:

1. **Run Full Test Suite**: `npm run test`
2. **Deploy to Staging**: Use deployment scripts
3. **Performance Testing**: Load test with realistic data
4. **Security Testing**: Check authentication flows
5. **User Acceptance Testing**: Test with real users

## 📞 Support

If you encounter issues:

1. Check the logs first
2. Verify all dependencies are installed
3. Ensure ports are not in use
4. Try the Docker approach if manual setup fails
5. Check environment variables are set correctly

Happy testing! 🎉
