"""
Feature Engineering Service for Vessel Classification
Extracts the same 99 features as your trained model
Optimized for fast processing and accuracy
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Tuple
import asyncio
from datetime import datetime, timezone
import math

logger = logging.getLogger(__name__)

async def extract_vessel_features(ais_data: pd.DataFrame) -> pd.DataFrame:
    """
    Extract vessel features from AIS data
    Returns the same 99 features your model was trained on
    
    Args:
        ais_data: DataFrame with columns [mmsi, timestamp, lat, lon, sog, cog, heading]
        
    Returns:
        DataFrame with extracted features for each vessel
    """
    logger.info(f"Extracting features from {len(ais_data)} AIS records")
    
    # Validate input data
    required_columns = ['mmsi', 'timestamp', 'lat', 'lon', 'sog', 'cog', 'heading']
    missing_columns = [col for col in required_columns if col not in ais_data.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    # Prepare data
    df = ais_data.copy()
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df = df.sort_values(['mmsi', 'timestamp'])
    
    # Group by vessel (mmsi)
    vessel_features = []
    
    for mmsi, vessel_data in df.groupby('mmsi'):
        if len(vessel_data) < 2:
            logger.warning(f"Vessel {mmsi} has only {len(vessel_data)} records, skipping")
            continue
        
        try:
            features = await _extract_single_vessel_features(mmsi, vessel_data)
            vessel_features.append(features)
        except Exception as e:
            logger.error(f"Error extracting features for vessel {mmsi}: {e}")
            continue
    
    if not vessel_features:
        logger.warning("No valid features extracted")
        return pd.DataFrame()
    
    features_df = pd.DataFrame(vessel_features)
    logger.info(f"Extracted features for {len(features_df)} vessels")
    
    return features_df

async def _extract_single_vessel_features(mmsi: str, vessel_data: pd.DataFrame) -> Dict[str, Any]:
    """Extract all 99 features for a single vessel"""
    
    # Ensure data is sorted by timestamp
    vessel_data = vessel_data.sort_values('timestamp').reset_index(drop=True)
    
    features = {'mmsi': mmsi}
    
    # Extract all feature categories
    features.update(await _extract_statistical_features(vessel_data))
    features.update(await _extract_temporal_features(vessel_data))
    features.update(await _extract_movement_features(vessel_data))
    features.update(await _extract_behavioral_features(vessel_data))
    
    return features

async def _extract_statistical_features(data: pd.DataFrame) -> Dict[str, float]:
    """Extract statistical features (25 features)"""
    features = {}
    
    # Statistical features for each numeric column
    for col in ['lat', 'lon', 'sog', 'cog', 'heading']:
        if col in data.columns:
            values = data[col].dropna()
            if len(values) > 0:
                features[f'{col}_mean'] = float(values.mean())
                features[f'{col}_std'] = float(values.std()) if len(values) > 1 else 0.0
                features[f'{col}_min'] = float(values.min())
                features[f'{col}_max'] = float(values.max())
                features[f'{col}_range'] = float(values.max() - values.min())
            else:
                # Default values for missing data
                features[f'{col}_mean'] = 0.0
                features[f'{col}_std'] = 0.0
                features[f'{col}_min'] = 0.0
                features[f'{col}_max'] = 0.0
                features[f'{col}_range'] = 0.0
    
    return features

async def _extract_temporal_features(data: pd.DataFrame) -> Dict[str, float]:
    """Extract temporal features (8 features)"""
    features = {}
    
    # Time span
    time_span = (data['timestamp'].max() - data['timestamp'].min()).total_seconds() / 3600.0
    features['time_span_hours'] = float(time_span)
    
    # Number of points
    features['num_points'] = float(len(data))
    
    # Average time between points
    time_diffs = data['timestamp'].diff().dt.total_seconds() / 60.0  # minutes
    time_diffs = time_diffs.dropna()
    features['avg_time_between_points'] = float(time_diffs.mean()) if len(time_diffs) > 0 else 0.0
    
    # Night activity ratio (assuming night is 18:00-06:00 UTC)
    night_records = data[
        (data['timestamp'].dt.hour >= 18) | (data['timestamp'].dt.hour < 6)
    ]
    features['night_activity_ratio'] = float(len(night_records) / len(data)) if len(data) > 0 else 0.0
    
    # Weekend activity ratio
    weekend_records = data[data['timestamp'].dt.weekday >= 5]  # Saturday=5, Sunday=6
    features['weekend_activity_ratio'] = float(len(weekend_records) / len(data)) if len(data) > 0 else 0.0
    
    # Additional temporal features
    features['points_per_hour'] = float(len(data) / time_span) if time_span > 0 else 0.0
    features['max_time_gap_hours'] = float(time_diffs.max() / 60.0) if len(time_diffs) > 0 else 0.0
    features['time_regularity'] = float(time_diffs.std() / time_diffs.mean()) if len(time_diffs) > 0 and time_diffs.mean() > 0 else 0.0
    
    return features

async def _extract_movement_features(data: pd.DataFrame) -> Dict[str, float]:
    """Extract movement features (15 features)"""
    features = {}
    
    # Calculate distances between consecutive points
    distances = []
    speeds = []
    
    for i in range(1, len(data)):
        prev = data.iloc[i-1]
        curr = data.iloc[i]
        
        # Haversine distance
        dist = _haversine_distance(prev['lat'], prev['lon'], curr['lat'], curr['lon'])
        distances.append(dist)
        
        # Time difference in hours
        time_diff = (curr['timestamp'] - prev['timestamp']).total_seconds() / 3600.0
        if time_diff > 0:
            speed = dist / time_diff  # km/h
            speeds.append(speed)
    
    # Total distance
    features['total_distance'] = float(sum(distances))
    
    # Speed features
    if speeds:
        features['avg_speed'] = float(np.mean(speeds))
        features['max_speed'] = float(np.max(speeds))
        features['speed_std'] = float(np.std(speeds))
    else:
        features['avg_speed'] = 0.0
        features['max_speed'] = 0.0
        features['speed_std'] = 0.0
    
    # Speed changes
    sog_values = data['sog'].dropna()
    if len(sog_values) > 1:
        speed_changes = np.abs(sog_values.diff()).dropna()
        features['speed_changes'] = float(speed_changes.sum())
        features['avg_speed_change'] = float(speed_changes.mean())
    else:
        features['speed_changes'] = 0.0
        features['avg_speed_change'] = 0.0
    
    # Direction changes
    cog_values = data['cog'].dropna()
    if len(cog_values) > 1:
        direction_changes = _calculate_direction_changes(cog_values)
        features['direction_changes'] = float(direction_changes.sum())
        features['avg_direction_change'] = float(direction_changes.mean())
    else:
        features['direction_changes'] = 0.0
        features['avg_direction_change'] = 0.0
    
    # Stops detection (speed < 1 knot for > 10 minutes)
    stop_threshold = 1.0  # knots
    stops = 0
    moving_time = 0
    stationary_time = 0
    
    for i in range(len(data)):
        if data.iloc[i]['sog'] < stop_threshold:
            stationary_time += 1
        else:
            moving_time += 1
            if i > 0 and data.iloc[i-1]['sog'] < stop_threshold:
                stops += 1
    
    features['stops_count'] = float(stops)
    features['moving_time_ratio'] = float(moving_time / len(data)) if len(data) > 0 else 0.0
    features['stationary_time_ratio'] = float(stationary_time / len(data)) if len(data) > 0 else 0.0
    
    # Acceleration features
    if len(speeds) > 1:
        accelerations = np.diff(speeds)
        features['acceleration_mean'] = float(np.mean(accelerations))
        features['acceleration_std'] = float(np.std(accelerations))
    else:
        features['acceleration_mean'] = 0.0
        features['acceleration_std'] = 0.0
    
    # Turn rate features
    if len(cog_values) > 1:
        turn_rates = _calculate_turn_rates(cog_values, data['timestamp'])
        features['turn_rate_mean'] = float(np.mean(turn_rates))
        features['turn_rate_std'] = float(np.std(turn_rates))
    else:
        features['turn_rate_mean'] = 0.0
        features['turn_rate_std'] = 0.0
    
    return features

async def _extract_behavioral_features(data: pd.DataFrame) -> Dict[str, float]:
    """Extract behavioral features (10 features)"""
    features = {}
    
    # Distance from shore (simplified - using lat/lon bounds)
    # This is a simplified version - in production you'd use actual coastline data
    shore_distances = []
    for _, row in data.iterrows():
        # Rough approximation: distance to nearest "typical" shore coordinate
        # You would replace this with actual shore distance calculation
        min_shore_dist = min(
            abs(row['lat'] - 40.7128),  # NYC area
            abs(row['lat'] - 51.5074),  # London area
            abs(row['lat'] - 35.6762),  # Tokyo area
        ) * 111.0  # Rough km per degree
        shore_distances.append(min_shore_dist)
    
    if shore_distances:
        features['distance_from_shore_mean'] = float(np.mean(shore_distances))
        features['distance_from_shore_min'] = float(np.min(shore_distances))
    else:
        features['distance_from_shore_mean'] = 0.0
        features['distance_from_shore_min'] = 0.0
    
    # Port activity ratio (simplified - low speed + small area)
    port_threshold_speed = 3.0  # knots
    port_threshold_area = 0.01  # degrees
    
    lat_range = data['lat'].max() - data['lat'].min()
    lon_range = data['lon'].max() - data['lon'].min()
    
    in_port_records = data[
        (data['sog'] < port_threshold_speed) & 
        (lat_range < port_threshold_area) & 
        (lon_range < port_threshold_area)
    ]
    features['in_port_ratio'] = float(len(in_port_records) / len(data)) if len(data) > 0 else 0.0
    
    # Speed consistency
    sog_values = data['sog'].dropna()
    if len(sog_values) > 0:
        speed_cv = sog_values.std() / sog_values.mean() if sog_values.mean() > 0 else 0.0
        features['speed_consistency'] = float(1.0 / (1.0 + speed_cv))  # Higher = more consistent
    else:
        features['speed_consistency'] = 0.0
    
    # Course consistency
    cog_values = data['cog'].dropna()
    if len(cog_values) > 0:
        # Circular variance for course
        cog_rad = np.radians(cog_values)
        mean_cos = np.mean(np.cos(cog_rad))
        mean_sin = np.mean(np.sin(cog_rad))
        circular_var = 1 - np.sqrt(mean_cos**2 + mean_sin**2)
        features['course_consistency'] = float(1.0 - circular_var)
    else:
        features['course_consistency'] = 0.0
    
    # Zigzag factor (path efficiency)
    if len(data) > 2:
        total_distance = sum(_haversine_distance(
            data.iloc[i]['lat'], data.iloc[i]['lon'],
            data.iloc[i+1]['lat'], data.iloc[i+1]['lon']
        ) for i in range(len(data)-1))
        
        direct_distance = _haversine_distance(
            data.iloc[0]['lat'], data.iloc[0]['lon'],
            data.iloc[-1]['lat'], data.iloc[-1]['lon']
        )
        
        features['zigzag_factor'] = float(total_distance / direct_distance) if direct_distance > 0 else 1.0
    else:
        features['zigzag_factor'] = 1.0
    
    # Circular variance
    if len(cog_values) > 0:
        cog_rad = np.radians(cog_values)
        features['circular_variance'] = float(1 - np.sqrt(
            np.mean(np.cos(cog_rad))**2 + np.mean(np.sin(cog_rad))**2
        ))
    else:
        features['circular_variance'] = 0.0
    
    # Linearity index
    features['linearity_index'] = float(1.0 / features['zigzag_factor']) if features['zigzag_factor'] > 0 else 0.0
    
    return features

def _haversine_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """Calculate Haversine distance between two points in kilometers"""
    R = 6371.0  # Earth's radius in kilometers
    
    lat1_rad = math.radians(lat1)
    lon1_rad = math.radians(lon1)
    lat2_rad = math.radians(lat2)
    lon2_rad = math.radians(lon2)
    
    dlat = lat2_rad - lat1_rad
    dlon = lon2_rad - lon1_rad
    
    a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
    c = 2 * math.asin(math.sqrt(a))
    
    return R * c

def _calculate_direction_changes(cog_values: pd.Series) -> np.ndarray:
    """Calculate direction changes handling circular nature of course"""
    changes = []
    for i in range(1, len(cog_values)):
        diff = abs(cog_values.iloc[i] - cog_values.iloc[i-1])
        # Handle circular nature (0-360 degrees)
        if diff > 180:
            diff = 360 - diff
        changes.append(diff)
    return np.array(changes)

def _calculate_turn_rates(cog_values: pd.Series, timestamps: pd.Series) -> np.ndarray:
    """Calculate turn rates in degrees per minute"""
    turn_rates = []
    for i in range(1, len(cog_values)):
        direction_change = _calculate_direction_changes(cog_values.iloc[i-1:i+1])[0]
        time_diff = (timestamps.iloc[i] - timestamps.iloc[i-1]).total_seconds() / 60.0  # minutes
        if time_diff > 0:
            turn_rate = direction_change / time_diff
            turn_rates.append(turn_rate)
    return np.array(turn_rates) if turn_rates else np.array([0.0])