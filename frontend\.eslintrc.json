{"extends": ["next/core-web-vitals", "eslint:recommended", "@typescript-eslint/recommended", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "plugins": ["@typescript-eslint", "prettier"], "rules": {"prettier/prettier": "error", "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-non-null-assertion": "warn", "react-hooks/exhaustive-deps": "warn", "react/prop-types": "off", "react/react-in-jsx-scope": "off", "no-console": ["warn", {"allow": ["warn", "error"]}], "prefer-const": "error", "no-var": "error"}, "env": {"browser": true, "es2022": true, "node": true}, "settings": {"react": {"version": "detect"}}, "ignorePatterns": ["node_modules/", ".next/", "out/", "build/", "dist/", "coverage/"]}