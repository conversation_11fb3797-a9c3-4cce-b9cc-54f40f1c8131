"""
Services Initialization for VESLINT Backend.

This module provides centralized initialization and management of all
business logic services in the VESLINT maritime vessel classification system.
"""

import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime

from .ml_service import get_classifier, validate_ml_configuration
from .csv_processor import process_uploaded_csv, validate_ais_data
from .feature_engineering import extract_vessel_features
from .background_tasks import start_vessel_classification
from ..utils.logging import setup_logging, get_logger
import os

logger = get_logger(__name__)

# Global service instances
_services: Dict[str, Any] = {}
_initialization_status: Dict[str, bool] = {}
_last_health_check: Optional[datetime] = None
_health_check_results: Dict[str, bool] = {}

# Service configuration
SERVICE_CONFIG = {
    "ml_service": {
        "class": MLService,
        "singleton": True,
        "auto_init": True,
        "health_check": True,
        "dependencies": [],
        "config": {
            "model_path": ML_MODEL_PATH,
            "enable_dummy_mode": os.getenv('ENVIRONMENT', 'production') == "development"
        }
    },
    "csv_processor": {
        "class": CSVProcessor,
        "singleton": True,
        "auto_init": True,
        "health_check": True,
        "dependencies": [],
        "config": {
            "max_file_size_mb": 50,
            "chunk_size": 1000,
            "validation_enabled": True
        }
    },
    "feature_engineer": {
        "class": FeatureEngineer,
        "singleton": True,
        "auto_init": True,
        "health_check": True,
        "dependencies": [],
        "config": {
            "feature_count": 99,
            "enable_caching": True,
            "parallel_processing": True
        }
    },
    "background_tasks": {
        "class": BackgroundTaskManager,
        "singleton": True,
        "auto_init": True,
        "health_check": False,  # Background tasks don't need health checks
        "dependencies": ["ml_service", "csv_processor", "feature_engineer"],
        "config": {
            "max_concurrent_jobs": 5,
            "job_timeout_minutes": 30,
            "retry_attempts": 3
        }
    }
}


class ServiceManager:
    """
    Centralized service manager for VESLINT backend services.
    
    Manages initialization, health checking, and lifecycle of all
    business logic services.
    """
    
    def __init__(self):
        self.services = {}
        self.initialized = False
        self.startup_time: Optional[datetime] = None
        
    async def initialize_all(self) -> bool:
        """
        Initialize all services in dependency order.
        
        Returns:
            True if all services initialized successfully
        """
        logger.info("Initializing VESLINT services...")
        
        try:
            with TimerContext("service_initialization") as timer:
                # Sort services by dependencies
                ordered_services = self._resolve_dependencies()
                
                for service_name in ordered_services:
                    await self._initialize_service(service_name)
                
                self.initialized = True
                self.startup_time = datetime.utcnow()
                
                logger.success(
                    f"All services initialized successfully in {timer.duration_ms:.2f}ms"
                )
                
                # Run initial health check
                await self.health_check_all()
                
                return True
                
        except Exception as e:
            logger.error(f"Service initialization failed: {e}")
            await self.cleanup_all()
            return False
    
    async def _initialize_service(self, service_name: str) -> bool:
        """Initialize a specific service."""
        config = SERVICE_CONFIG.get(service_name)
        if not config:
            raise ValueError(f"Unknown service: {service_name}")
        
        logger.info(f"Initializing service: {service_name}")
        
        try:
            # Check if already initialized
            if service_name in self.services:
                logger.warning(f"Service {service_name} already initialized")
                return True
            
            # Create service instance
            service_class = config["class"]
            service_config = config.get("config", {})
            
            if config.get("singleton", True):
                service_instance = service_class(**service_config)
            else:
                # For non-singleton services, store the class for later instantiation
                service_instance = service_class
            
            # Initialize if needed
            if hasattr(service_instance, 'initialize') and config.get("auto_init", True):
                if asyncio.iscoroutinefunction(service_instance.initialize):
                    await service_instance.initialize()
                else:
                    service_instance.initialize()
            
            self.services[service_name] = service_instance
            _services[service_name] = service_instance
            _initialization_status[service_name] = True
            
            logger.success(f"Service {service_name} initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize service {service_name}: {e}")
            _initialization_status[service_name] = False
            raise
    
    def _resolve_dependencies(self) -> List[str]:
        """Resolve service dependencies and return initialization order."""
        ordered = []
        remaining = set(SERVICE_CONFIG.keys())
        
        while remaining:
            # Find services with no unresolved dependencies
            ready = []
            for service_name in remaining:
                dependencies = SERVICE_CONFIG[service_name].get("dependencies", [])
                if all(dep in ordered for dep in dependencies):
                    ready.append(service_name)
            
            if not ready:
                raise ValueError(f"Circular dependency detected in services: {remaining}")
            
            # Add ready services to ordered list
            for service_name in ready:
                ordered.append(service_name)
                remaining.remove(service_name)
        
        return ordered
    
    async def health_check_all(self) -> Dict[str, bool]:
        """
        Perform health check on all services.
        
        Returns:
            Dictionary mapping service names to health status
        """
        global _last_health_check, _health_check_results
        
        logger.info("Performing health check on all services...")
        results = {}
        
        try:
            for service_name, service_instance in self.services.items():
                config = SERVICE_CONFIG.get(service_name, {})
                
                if not config.get("health_check", False):
                    results[service_name] = True  # Skip health check
                    continue
                
                try:
                    if hasattr(service_instance, 'health_check'):
                        if asyncio.iscoroutinefunction(service_instance.health_check):
                            health_status = await service_instance.health_check()
                        else:
                            health_status = service_instance.health_check()
                        results[service_name] = bool(health_status)
                    else:
                        # If no health check method, assume healthy if initialized
                        results[service_name] = service_name in _initialization_status
                        
                except Exception as e:
                    logger.warning(f"Health check failed for {service_name}: {e}")
                    results[service_name] = False
            
            _last_health_check = datetime.utcnow()
            _health_check_results = results
            
            healthy_count = sum(results.values())
            total_count = len(results)
            
            logger.info(f"Health check completed: {healthy_count}/{total_count} services healthy")
            
            return results
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {}
    
    async def cleanup_all(self) -> None:
        """Cleanup all services."""
        logger.info("Cleaning up all services...")
        
        for service_name, service_instance in self.services.items():
            try:
                if hasattr(service_instance, 'cleanup'):
                    if asyncio.iscoroutinefunction(service_instance.cleanup):
                        await service_instance.cleanup()
                    else:
                        service_instance.cleanup()
                logger.info(f"Cleaned up service: {service_name}")
            except Exception as e:
                logger.warning(f"Error cleaning up service {service_name}: {e}")
        
        self.services.clear()
        _services.clear()
        _initialization_status.clear()
        self.initialized = False
        
        logger.info("Service cleanup completed")
    
    def get_service(self, service_name: str) -> Any:
        """Get a service instance by name."""
        if service_name not in self.services:
            raise ValueError(f"Service {service_name} not found or not initialized")
        return self.services[service_name]
    
    def is_healthy(self) -> bool:
        """Check if all services are healthy."""
        if not _health_check_results:
            return False
        return all(_health_check_results.values())
    
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive status of all services."""
        return {
            "initialized": self.initialized,
            "startup_time": self.startup_time,
            "service_count": len(self.services),
            "services": {
                name: {
                    "initialized": name in _initialization_status,
                    "healthy": _health_check_results.get(name, False),
                    "class": service.__class__.__name__
                }
                for name, service in self.services.items()
            },
            "last_health_check": _last_health_check,
            "overall_health": self.is_healthy()
        }


# Global service manager instance
service_manager = ServiceManager()


# Convenience functions for accessing services
def get_ml_service() -> MLService:
    """Get the ML service instance."""
    return service_manager.get_service("ml_service")


def get_csv_processor() -> CSVProcessor:
    """Get the CSV processor service instance."""
    return service_manager.get_service("csv_processor")


def get_feature_engineer() -> FeatureEngineer:
    """Get the feature engineer service instance."""
    return service_manager.get_service("feature_engineer")


def get_background_tasks() -> BackgroundTaskManager:
    """Get the background task manager service instance."""
    return service_manager.get_service("background_tasks")


async def initialize_services() -> bool:
    """
    Initialize all VESLINT services.
    
    This is the main entry point for service initialization,
    typically called during application startup.
    
    Returns:
        True if initialization successful
    """
    return await service_manager.initialize_all()


async def cleanup_services() -> None:
    """
    Cleanup all VESLINT services.
    
    This should be called during application shutdown.
    """
    await service_manager.cleanup_all()


async def health_check_services() -> Dict[str, bool]:
    """
    Perform health check on all services.
    
    Returns:
        Dictionary mapping service names to health status
    """
    return await service_manager.health_check_all()


def get_service_status() -> Dict[str, Any]:
    """
    Get comprehensive status of all services.
    
    Returns:
        Dictionary containing service status information
    """
    return service_manager.get_status()


def is_services_healthy() -> bool:
    """
    Check if all services are healthy.
    
    Returns:
        True if all services are healthy
    """
    return service_manager.is_healthy()


# Service availability checks
def is_ml_service_available() -> bool:
    """Check if ML service is available and healthy."""
    try:
        service = get_ml_service()
        return service is not None and _health_check_results.get("ml_service", False)
    except:
        return False


def is_csv_processor_available() -> bool:
    """Check if CSV processor service is available and healthy."""
    try:
        service = get_csv_processor()
        return service is not None and _health_check_results.get("csv_processor", False)
    except:
        return False


def is_feature_engineer_available() -> bool:
    """Check if feature engineer service is available and healthy."""
    try:
        service = get_feature_engineer()
        return service is not None and _health_check_results.get("feature_engineer", False)
    except:
        return False


# Export commonly used functions from service modules
# This allows importing from services directly
__all__ = [
    # Service manager
    "service_manager",
    "initialize_services", 
    "cleanup_services",
    "health_check_services",
    "get_service_status",
    "is_services_healthy",
    
    # Service getters
    "get_ml_service",
    "get_csv_processor", 
    "get_feature_engineer",
    "get_background_tasks",
    
    # Availability checks
    "is_ml_service_available",
    "is_csv_processor_available", 
    "is_feature_engineer_available",
    
    # Re-export key functions from service modules
    "get_model_info",
    "validate_ais_data",
    "process_csv_file", 
    "extract_vessel_features",
    "create_classification_job",
    
    # Configuration
    "SERVICE_CONFIG"
]
