-- VESLINT Database Schema for Supabase
-- Initial migration: Jobs, vessels, and user management

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Jobs table - tracks classification jobs
CREATE TABLE IF NOT EXISTS jobs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL, -- References auth.users(id) via RLS
    
    -- Job metadata
    filename TEXT NOT NULL,
    file_size BIGINT,
    file_path TEXT, -- Path in Supabase storage
    
    -- Job status tracking
    status TEXT NOT NULL DEFAULT 'created' CHECK (
        status IN ('created', 'uploading', 'processing', 'extracting_features', 
                  'classifying', 'completed', 'failed', 'cancelled')
    ),
    
    -- Progress tracking
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    total_vessels INTEGER DEFAULT 0,
    processed_vessels INTEGER DEFAULT 0,
    
    -- Results summary
    results_summary JSONB DEFAULT '{}',
    error_message TEXT,
    
    -- Classification results
    tug_count INTEGER DEFAULT 0,
    fishing_count INTEGER DEFAULT 0,
    pleasure_count INTEGER DEFAULT 0,
    cargo_count INTEGER DEFAULT 0,
    
    -- Performance metrics
    processing_time_seconds INTEGER,
    features_extracted_count INTEGER,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for performance
CREATE INDEX idx_jobs_user_id ON jobs(user_id);
CREATE INDEX idx_jobs_status ON jobs(status);
CREATE INDEX idx_jobs_created_at ON jobs(created_at DESC);
CREATE INDEX idx_jobs_updated_at ON jobs(updated_at DESC);

-- Vessel classifications table - detailed results
CREATE TABLE IF NOT EXISTS vessel_classifications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    
    -- Vessel identification
    mmsi TEXT NOT NULL,
    
    -- Classification results
    predicted_class INTEGER NOT NULL CHECK (predicted_class IN (0, 1, 2, 3)),
    class_name TEXT NOT NULL CHECK (class_name IN ('TUG', 'FISHING', 'PLEASURE', 'CARGO')),
    confidence DECIMAL(5,4) DEFAULT 0.0000 CHECK (confidence >= 0 AND confidence <= 1),
    
    -- Class probabilities
    tug_probability DECIMAL(5,4) DEFAULT 0.0000,
    fishing_probability DECIMAL(5,4) DEFAULT 0.0000,
    pleasure_probability DECIMAL(5,4) DEFAULT 0.0000,
    cargo_probability DECIMAL(5,4) DEFAULT 0.0000,
    
    -- Input data summary
    ais_points_count INTEGER,
    time_span_hours DECIMAL(10,2),
    total_distance_km DECIMAL(10,2),
    avg_speed_knots DECIMAL(6,2),
    
    -- Processing metadata
    features_json JSONB, -- Store extracted features if needed
    processing_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(job_id, mmsi)
);

-- Create indexes for vessel classifications
CREATE INDEX idx_vessel_classifications_job_id ON vessel_classifications(job_id);
CREATE INDEX idx_vessel_classifications_mmsi ON vessel_classifications(mmsi);
CREATE INDEX idx_vessel_classifications_class ON vessel_classifications(predicted_class);
CREATE INDEX idx_vessel_classifications_confidence ON vessel_classifications(confidence DESC);

-- Job statistics view for quick analytics
CREATE OR REPLACE VIEW job_statistics AS
SELECT 
    j.id,
    j.user_id,
    j.filename,
    j.status,
    j.progress,
    j.total_vessels,
    j.processed_vessels,
    j.tug_count,
    j.fishing_count,
    j.pleasure_count,
    j.cargo_count,
    j.processing_time_seconds,
    j.created_at,
    j.completed_at,
    
    -- Calculated fields
    CASE 
        WHEN j.total_vessels > 0 THEN 
            ROUND((j.processed_vessels::DECIMAL / j.total_vessels) * 100, 2)
        ELSE 0 
    END as completion_percentage,
    
    CASE 
        WHEN j.completed_at IS NOT NULL AND j.started_at IS NOT NULL THEN
            EXTRACT(EPOCH FROM (j.completed_at - j.started_at))
        ELSE NULL
    END as actual_processing_time_seconds
    
FROM jobs j;

-- User analytics view (for dashboard)
CREATE OR REPLACE VIEW user_analytics AS
SELECT 
    user_id,
    COUNT(*) as total_jobs,
    COUNT(*) FILTER (WHERE status = 'completed') as completed_jobs,
    COUNT(*) FILTER (WHERE status = 'failed') as failed_jobs,
    SUM(total_vessels) as total_vessels_processed,
    SUM(tug_count) as total_tug_classified,
    SUM(fishing_count) as total_fishing_classified,
    SUM(pleasure_count) as total_pleasure_classified,
    SUM(cargo_count) as total_cargo_classified,
    AVG(processing_time_seconds) as avg_processing_time,
    MIN(created_at) as first_job_date,
    MAX(created_at) as last_job_date
FROM jobs
GROUP BY user_id;

-- Function to update job updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at
CREATE TRIGGER update_jobs_updated_at 
    BEFORE UPDATE ON jobs 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Function to update job progress when vessel classifications are added
CREATE OR REPLACE FUNCTION update_job_progress()
RETURNS TRIGGER AS $$
BEGIN
    -- Update processed_vessels count and class counts
    UPDATE jobs SET 
        processed_vessels = (
            SELECT COUNT(*) 
            FROM vessel_classifications 
            WHERE job_id = NEW.job_id
        ),
        tug_count = (
            SELECT COUNT(*) 
            FROM vessel_classifications 
            WHERE job_id = NEW.job_id AND predicted_class = 0
        ),
        fishing_count = (
            SELECT COUNT(*) 
            FROM vessel_classifications 
            WHERE job_id = NEW.job_id AND predicted_class = 1
        ),
        pleasure_count = (
            SELECT COUNT(*) 
            FROM vessel_classifications 
            WHERE job_id = NEW.job_id AND predicted_class = 2
        ),
        cargo_count = (
            SELECT COUNT(*) 
            FROM vessel_classifications 
            WHERE job_id = NEW.job_id AND predicted_class = 3
        ),
        progress = LEAST(100, ROUND(
            (SELECT COUNT(*) FROM vessel_classifications WHERE job_id = NEW.job_id)::DECIMAL 
            / GREATEST(1, (SELECT total_vessels FROM jobs WHERE id = NEW.job_id)) * 100
        ))
    WHERE id = NEW.job_id;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to update job progress
CREATE TRIGGER update_job_progress_trigger
    AFTER INSERT ON vessel_classifications
    FOR EACH ROW
    EXECUTE FUNCTION update_job_progress();

-- Add comments for documentation
COMMENT ON TABLE jobs IS 'Tracks vessel classification jobs and their progress';
COMMENT ON TABLE vessel_classifications IS 'Stores individual vessel classification results';
COMMENT ON VIEW job_statistics IS 'Provides quick access to job statistics and metrics';
COMMENT ON VIEW user_analytics IS 'Aggregated analytics per user for dashboard';

-- Sample data for testing (optional)
-- INSERT INTO jobs (user_id, filename, status, total_vessels) VALUES 
-- ('123e4567-e89b-12d3-a456-426614174000', 'sample_ais_data.csv', 'completed', 100);

-- Success message
SELECT 'VESLINT database schema created successfully!' as message;