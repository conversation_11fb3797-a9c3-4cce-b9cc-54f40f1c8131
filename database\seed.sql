-- VESLINT Database Seed Data
-- Sample data for local development

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Sample users (these would normally come from Firebase Auth)
INSERT INTO auth.users (id, email, created_at)
VALUES 
  ('00000000-0000-0000-0000-000000000001', '<EMAIL>', NOW()),
  ('00000000-0000-0000-0000-000000000002', '<EMAIL>', NOW())
ON CONFLICT (id) DO NOTHING;

-- Sample jobs
INSERT INTO public.jobs (
  id, user_id, status, filename, file_size, total_vessels, processed_vessels,
  progress, created_at, updated_at
)
VALUES
  (
    'job-00001', '00000000-0000-0000-0000-000000000001', 'completed', 
    'sample_ais_data.csv', 1024000, 50, 50, 100, 
    NOW() - INTERVAL '2 days', NOW() - INTERVAL '1 day'
  ),
  (
    'job-00002', '00000000-0000-0000-0000-000000000001', 'processing', 
    'large_dataset.csv', 5120000, 200, 75, 37, 
    NOW() - INTERVAL '1 hour', NOW() - INTERVAL '10 minutes'
  ),
  (
    'job-00003', '00000000-0000-0000-0000-000000000001', 'failed', 
    'invalid_data.csv', 512000, 0, 0, 0, 
    NOW() - INTERVAL '3 days', NOW() - INTERVAL '3 days'
  ),
  (
    'job-00004', '00000000-0000-0000-0000-000000000002', 'completed', 
    'test_data.csv', 768000, 25, 25, 100, 
    NOW() - INTERVAL '5 days', NOW() - INTERVAL '5 days'
  )
ON CONFLICT (id) DO NOTHING;

-- Sample vessel classifications
INSERT INTO public.vessel_classifications (
  id, job_id, mmsi, vessel_type, confidence, created_at
)
VALUES
  (
    uuid_generate_v4(), 'job-00001', '123456789', 'cargo', 0.92, 
    NOW() - INTERVAL '1 day'
  ),
  (
    uuid_generate_v4(), 'job-00001', '234567890', 'fishing', 0.87, 
    NOW() - INTERVAL '1 day'
  ),
  (
    uuid_generate_v4(), 'job-00001', '345678901', 'pleasure', 0.94, 
    NOW() - INTERVAL '1 day'
  ),
  (
    uuid_generate_v4(), 'job-00001', '456789012', 'tug', 0.91, 
    NOW() - INTERVAL '1 day'
  ),
  (
    uuid_generate_v4(), 'job-00004', '567890123', 'cargo', 0.89, 
    NOW() - INTERVAL '5 days'
  ),
  (
    uuid_generate_v4(), 'job-00004', '678901234', 'tanker', 0.95, 
    NOW() - INTERVAL '5 days'
  )
ON CONFLICT (id) DO NOTHING;

-- Sample vessels
INSERT INTO public.vessels (
  id, mmsi, name, vessel_type, vessel_type_code, length, width, flag,
  classification_confidence, created_at, updated_at
)
VALUES
  (
    uuid_generate_v4(), '123456789', 'OCEAN VOYAGER', 'cargo', 70, 120, 20, 
    'US', 0.92, NOW() - INTERVAL '30 days', NOW() - INTERVAL '1 day'
  ),
  (
    uuid_generate_v4(), '234567890', 'SEAFARER II', 'fishing', 30, 24, 8, 
    'CA', 0.87, NOW() - INTERVAL '45 days', NOW() - INTERVAL '1 day'
  ),
  (
    uuid_generate_v4(), '345678901', 'SUMMER BREEZE', 'pleasure', 37, 15, 4, 
    'UK', 0.94, NOW() - INTERVAL '60 days', NOW() - INTERVAL '1 day'
  ),
  (
    uuid_generate_v4(), '456789012', 'HARBOR MASTER', 'tug', 52, 30, 10, 
    'US', 0.91, NOW() - INTERVAL '90 days', NOW() - INTERVAL '1 day'
  ),
  (
    uuid_generate_v4(), '567890123', 'PACIFIC TRADER', 'cargo', 70, 150, 25, 
    'JP', 0.89, NOW() - INTERVAL '120 days', NOW() - INTERVAL '5 days'
  ),
  (
    uuid_generate_v4(), '678901234', 'BLACK GOLD', 'tanker', 80, 200, 30, 
    'GR', 0.95, NOW() - INTERVAL '150 days', NOW() - INTERVAL '5 days'
  )
ON CONFLICT (mmsi) DO NOTHING;

-- Sample storage objects (references to files that would exist in Supabase Storage)
INSERT INTO storage.objects (
  id, bucket_id, name, owner, created_at, updated_at, metadata
)
VALUES
  (
    uuid_generate_v4(), 'ais-data', '00000000-0000-0000-0000-000000000001/job-00001/sample_ais_data.csv',
    '00000000-0000-0000-0000-000000000001', NOW() - INTERVAL '2 days', NOW() - INTERVAL '2 days',
    '{"contentType": "text/csv", "size": 1024000}'
  ),
  (
    uuid_generate_v4(), 'ais-data', '00000000-0000-0000-0000-000000000001/job-00002/large_dataset.csv',
    '00000000-0000-0000-0000-000000000001', NOW() - INTERVAL '1 hour', NOW() - INTERVAL '1 hour',
    '{"contentType": "text/csv", "size": 5120000}'
  ),
  (
    uuid_generate_v4(), 'results', '00000000-0000-0000-0000-000000000001/job-00001/results.json',
    '00000000-0000-0000-0000-000000000001', NOW() - INTERVAL '1 day', NOW() - INTERVAL '1 day',
    '{"contentType": "application/json", "size": 256000}'
  ),
  (
    uuid_generate_v4(), 'results', '00000000-0000-0000-0000-000000000002/job-00004/results.json',
    '00000000-0000-0000-0000-000000000002', NOW() - INTERVAL '5 days', NOW() - INTERVAL '5 days',
    '{"contentType": "application/json", "size": 128000}'
  )
ON CONFLICT (id) DO NOTHING;

-- Add job statistics
UPDATE public.jobs
SET 
  cargo_count = 25,
  fishing_count = 10,
  pleasure_count = 8,
  tug_count = 7,
  processing_time_seconds = 3600
WHERE id = 'job-00001';

UPDATE public.jobs
SET 
  cargo_count = 15,
  tanker_count = 10,
  processing_time_seconds = 1800
WHERE id = 'job-00004';

UPDATE public.jobs
SET 
  error_message = 'Invalid CSV format: missing required columns'
WHERE id = 'job-00003';

-- Set file paths
UPDATE public.jobs
SET file_path = '00000000-0000-0000-0000-000000000001/job-00001/sample_ais_data.csv'
WHERE id = 'job-00001';

UPDATE public.jobs
SET file_path = '00000000-0000-0000-0000-000000000001/job-00002/large_dataset.csv'
WHERE id = 'job-00002';

UPDATE public.jobs
SET file_path = '00000000-0000-0000-0000-000000000002/job-00004/test_data.csv'
WHERE id = 'job-00004';

-- Set result URLs for completed jobs
UPDATE public.jobs
SET result_url = '00000000-0000-0000-0000-000000000001/job-00001/results.json'
WHERE id = 'job-00001';

UPDATE public.jobs
SET result_url = '00000000-0000-0000-0000-000000000002/job-00004/results.json'
WHERE id = 'job-00004'; 