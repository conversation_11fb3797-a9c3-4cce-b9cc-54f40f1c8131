import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { Box } from '@mui/material'; // Only Box is needed here directly
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import ThemeRegistry from '@/lib/ThemeRegistry'; // Note the corrected path
import { AuthProvider } from '@/hooks/useAuth';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'VESLINT - AI-Powered Maritime Intelligence',
  description: 'The Ultimate Maritime Intelligence Platform for Defense Operations. AI-powered vessel classification and real-time threat detection.',
  keywords: ['maritime', 'surveillance', 'ai', 'vessel classification', 'defense', 'intelligence', 'ais data'],
};

// This is the function that defines your root layout in Next.js
export default function RootLayout({
  children, // The 'children' prop is where Next.js will render the current page
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        {/* ThemeRegistry provides the MUI theme and CSS baseline */}
        <ThemeRegistry>
            {/* AuthProvider gives auth context to all pages */}
            <AuthProvider>
              {/* This Box is your main page container from the old App.tsx */}
              <Box
                sx={{
                  minHeight: '100vh',
                  display: 'flex',
                  flexDirection: 'column',
                  background: 'linear-gradient(135deg, #0A192F 0%, #112240 100%)',
                  position: 'relative',
                  '&::before': {
                    content: '""',
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundImage: `
                      radial-gradient(circle at 25% 25%, rgba(100, 255, 218, 0.03) 0%, transparent 50%),
                      radial-gradient(circle at 75% 75%, rgba(100, 255, 218, 0.03) 0%, transparent 50%),
                      radial-gradient(circle at 50% 50%, rgba(100, 255, 218, 0.01) 0%, transparent 50%)
                    `,
                    pointerEvents: 'none',
                    zIndex: 0,
                  },
                }}
              >
                <Navbar />
                {/* The main content area where pages will be rendered */}
                <Box component="main" sx={{ flex: 1, position: 'relative', zIndex: 1 }}>
                  {children}
                </Box>
                <Footer />
              </Box>
            </AuthProvider>
        </ThemeRegistry>
      </body>
    </html>
  );
}