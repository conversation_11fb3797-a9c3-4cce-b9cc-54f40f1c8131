"""
Constants for VESLINT API
Defines features, classes, and configuration matching your trained model
"""

# API Configuration
API_VERSION = "v1"
APP_NAME = "VESLINT Maritime Intelligence API"
MAX_FILE_SIZE_MB = 50
MAX_VESSELS_PER_JOB = 10000

# Your trained model's expected features (99 features total)
EXPECTED_FEATURES = [
    # Statistical Features (25) - lat, lon, sog, cog, heading
    'lat_mean', 'lat_std', 'lat_min', 'lat_max', 'lat_range',
    'lon_mean', 'lon_std', 'lon_min', 'lon_max', 'lon_range',
    'sog_mean', 'sog_std', 'sog_min', 'sog_max', 'sog_range',
    'cog_mean', 'cog_std', 'cog_min', 'cog_max', 'cog_range',
    'heading_mean', 'heading_std', 'heading_min', 'heading_max', 'heading_range',
    
    # Temporal Features (8)
    'time_span_hours',
    'num_points',
    'avg_time_between_points',
    'night_activity_ratio',
    'weekend_activity_ratio',
    'points_per_hour',
    'max_time_gap_hours',
    'time_regularity',
    
    # Movement Features (15)
    'total_distance',
    'avg_speed',
    'max_speed',
    'speed_std',
    'speed_changes',
    'avg_speed_change',
    'direction_changes',
    'avg_direction_change',
    'stops_count',
    'moving_time_ratio',
    'stationary_time_ratio',
    'acceleration_mean',
    'acceleration_std',
    'turn_rate_mean',
    'turn_rate_std',
    
    # Behavioral Features (10)
    'distance_from_shore_mean',
    'distance_from_shore_min',
    'in_port_ratio',
    'speed_consistency',
    'course_consistency',
    'zigzag_factor',
    'circular_variance',
    'linearity_index'
]

# Class mapping from your trained model
CLASS_NAMES = {
    0: 'TUG',
    1: 'FISHING', 
    2: 'PLEASURE',
    3: 'CARGO'
}

# Class descriptions for API documentation
CLASS_DESCRIPTIONS = {
    'TUG': 'Tugboat - Low speed vessel used for towing/pushing',
    'FISHING': 'Fishing vessel - Variable speed patterns with fishing behavior',
    'PLEASURE': 'Pleasure craft - Recreational vessel with leisure patterns',
    'CARGO': 'Cargo vessel - High speed commercial transport vessel'
}

# Class distribution from your training data (for reference)
CLASS_DISTRIBUTION = {
    'TUG': 0.614,      # 61.4%
    'FISHING': 0.183,  # 18.3%
    'PLEASURE': 0.140, # 14.0%
    'CARGO': 0.062     # 6.2%
}

# Validation constraints for AIS data
AIS_VALIDATION = {
    'lat': {'min': -90.0, 'max': 90.0},
    'lon': {'min': -180.0, 'max': 180.0},
    'sog': {'min': 0.0, 'max': 60.0},      # Speed over ground in knots
    'cog': {'min': 0.0, 'max': 360.0},     # Course over ground in degrees
    'heading': {'min': 0.0, 'max': 360.0}, # Heading in degrees
    'mmsi': {'min_length': 7, 'max_length': 15}
}

# Required CSV columns
REQUIRED_CSV_COLUMNS = [
    'mmsi',      # Maritime Mobile Service Identity
    'timestamp', # ISO 8601 timestamp
    'lat',       # Latitude
    'lon',       # Longitude
    'sog',       # Speed over ground
    'cog',       # Course over ground
    'heading'    # Vessel heading
]

# Processing configuration
PROCESSING_CONFIG = {
    'chunk_size': 100,              # Vessels per processing chunk
    'max_concurrent_jobs': 5,       # Maximum concurrent classification jobs
    'job_timeout_minutes': 30,      # Maximum job processing time
    'cleanup_after_hours': 24,      # Clean up job data after hours
    'min_points_per_vessel': 2      # Minimum AIS points required per vessel
}

# Model performance metrics (from your training)
MODEL_METRICS = {
    'accuracy': 0.8815,             # 88.15% accuracy
    'model_type': 'Meta-ensemble',  # Model architecture
    'training_samples': None,       # You can add this
    'feature_count': len(EXPECTED_FEATURES),
    'class_count': len(CLASS_NAMES)
}

# API Response templates
RESPONSE_TEMPLATES = {
    'success': {
        'status': 'success',
        'message': 'Operation completed successfully'
    },
    'error': {
        'status': 'error',
        'message': 'An error occurred'
    },
    'validation_error': {
        'status': 'validation_error',
        'message': 'Input validation failed'
    }
}

# Job status constants
JOB_STATUS = {
    'CREATED': 'created',
    'UPLOADING': 'uploading',
    'PROCESSING': 'processing',
    'EXTRACTING_FEATURES': 'extracting_features',
    'CLASSIFYING': 'classifying',
    'COMPLETED': 'completed',
    'FAILED': 'failed',
    'CANCELLED': 'cancelled'
}

# File upload configuration
UPLOAD_CONFIG = {
    'allowed_extensions': ['.csv'],
    'max_file_size_bytes': MAX_FILE_SIZE_MB * 1024 * 1024,
    'chunk_size_bytes': 8192,
    'temp_file_lifetime_hours': 2
}

# Logging configuration
LOG_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'max_file_size_mb': 10,
    'backup_count': 5
}

# Environment-specific settings
ENV_SETTINGS = {
    'development': {
        'debug': True,
        'reload': True,
        'log_level': 'DEBUG'
    },
    'production': {
        'debug': False,
        'reload': False,
        'log_level': 'INFO'
    }
}