-- Supabase Storage Configuration for VESLINT
-- Creates buckets for AIS data files and results

-- Create bucket for AIS CSV uploads
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'ais-data',
    'ais-data',
    false,  -- Private bucket
    52428800,  -- 50MB limit
    ARRAY['text/csv', 'application/csv', 'text/plain']
);

-- Create bucket for processed results (optional)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'results',
    'results',
    false,  -- Private bucket
    104857600,  -- 100MB limit
    ARRAY['application/json', 'text/csv', 'application/zip']
);

-- Create bucket for temporary processing files
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'temp-processing',
    'temp-processing',
    false,  -- Private bucket
    52428800,  -- 50MB limit
    ARRAY['text/csv', 'application/json']
);

-- Storage policies for ais-data bucket
-- Users can upload their own files
CREATE POLICY "Users can upload AIS data" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'ais-data' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Users can view their own files
CREATE POLICY "Users can view own AIS data" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'ais-data' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Users can delete their own files
CREATE POLICY "Users can delete own AIS data" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'ais-data' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Backend service can access all files for processing
CREATE POLICY "Service can access AIS data" ON storage.objects
    FOR ALL USING (bucket_id = 'ais-data');

-- Storage policies for results bucket
-- Users can view their own results
CREATE POLICY "Users can view own results" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'results' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Service can create result files
CREATE POLICY "Service can create results" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'results');

-- Storage policies for temp-processing bucket
-- Service can manage temporary files
CREATE POLICY "Service can manage temp files" ON storage.objects
    FOR ALL USING (bucket_id = 'temp-processing');

-- Automatic cleanup function for old files
CREATE OR REPLACE FUNCTION cleanup_old_storage_files()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
    file_record RECORD;
BEGIN
    -- Delete AIS data files older than 7 days
    FOR file_record IN 
        SELECT bucket_id, name 
        FROM storage.objects 
        WHERE bucket_id = 'ais-data' 
        AND created_at < NOW() - INTERVAL '7 days'
    LOOP
        DELETE FROM storage.objects 
        WHERE bucket_id = file_record.bucket_id AND name = file_record.name;
        deleted_count := deleted_count + 1;
    END LOOP;
    
    -- Delete temp processing files older than 1 day
    FOR file_record IN 
        SELECT bucket_id, name 
        FROM storage.objects 
        WHERE bucket_id = 'temp-processing' 
        AND created_at < NOW() - INTERVAL '1 day'
    LOOP
        DELETE FROM storage.objects 
        WHERE bucket_id = file_record.bucket_id AND name = file_record.name;
        deleted_count := deleted_count + 1;
    END LOOP;
    
    -- Delete result files older than 30 days
    FOR file_record IN 
        SELECT bucket_id, name 
        FROM storage.objects 
        WHERE bucket_id = 'results' 
        AND created_at < NOW() - INTERVAL '30 days'
    LOOP
        DELETE FROM storage.objects 
        WHERE bucket_id = file_record.bucket_id AND name = file_record.name;
        deleted_count := deleted_count + 1;
    END LOOP;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create file upload tracking table
CREATE TABLE IF NOT EXISTS file_uploads (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL,
    job_id UUID REFERENCES jobs(id) ON DELETE CASCADE,
    
    -- File details
    original_filename TEXT NOT NULL,
    storage_path TEXT NOT NULL,
    bucket_name TEXT NOT NULL DEFAULT 'ais-data',
    file_size BIGINT NOT NULL,
    mime_type TEXT,
    
    -- Upload tracking
    upload_status TEXT DEFAULT 'uploading' CHECK (
        upload_status IN ('uploading', 'completed', 'failed', 'deleted')
    ),
    
    -- Validation results
    validation_status TEXT DEFAULT 'pending' CHECK (
        validation_status IN ('pending', 'valid', 'invalid')
    ),
    validation_errors JSONB DEFAULT '[]',
    
    -- Processing metadata
    total_rows INTEGER,
    valid_rows INTEGER,
    vessel_count INTEGER,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    validated_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for file uploads
CREATE INDEX idx_file_uploads_user_id ON file_uploads(user_id);
CREATE INDEX idx_file_uploads_job_id ON file_uploads(job_id);
CREATE INDEX idx_file_uploads_status ON file_uploads(upload_status);

-- RLS for file uploads
ALTER TABLE file_uploads ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own file uploads" ON file_uploads
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own file uploads" ON file_uploads
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Service can update file uploads" ON file_uploads
    FOR UPDATE USING (true);

-- Function to generate signed upload URL (called from backend)
CREATE OR REPLACE FUNCTION generate_upload_url(
    user_id_param UUID,
    filename_param TEXT,
    file_size_param BIGINT
)
RETURNS JSON AS $$
DECLARE
    job_id_val UUID;
    storage_path TEXT;
    upload_token TEXT;
BEGIN
    -- Generate new job ID
    job_id_val := uuid_generate_v4();
    
    -- Generate storage path: user_id/job_id/filename
    storage_path := user_id_param || '/' || job_id_val || '/' || filename_param;
    
    -- Generate upload token (simple approach)
    upload_token := encode(gen_random_bytes(32), 'hex');
    
    -- Create job record
    INSERT INTO jobs (id, user_id, filename, file_size, file_path, status)
    VALUES (job_id_val, user_id_param, filename_param, file_size_param, storage_path, 'created');
    
    -- Create file upload record
    INSERT INTO file_uploads (user_id, job_id, original_filename, storage_path, file_size)
    VALUES (user_id_param, job_id_val, filename_param, storage_path, file_size_param);
    
    -- Return upload details
    RETURN json_build_object(
        'job_id', job_id_val,
        'upload_path', storage_path,
        'upload_token', upload_token,
        'bucket', 'ais-data',
        'max_file_size', 52428800
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION generate_upload_url TO authenticated;

-- Success message
SELECT 'Storage buckets and policies configured for VESLINT!' as message;

-- Usage notes:
/*
Storage structure:
ais-data/
  ├── {user_id}/
  │   ├── {job_id}/
  │   │   ├── input.csv
  │   │   └── validated.csv
  │   └── ...
  └── ...

results/
  ├── {user_id}/
  │   ├── {job_id}/
  │   │   ├── classifications.json
  │   │   ├── summary.json
  │   │   └── export.csv
  │   └── ...
  └── ...

temp-processing/
  ├── chunks/
  │   ├── {job_id}_chunk_1.csv
  │   ├── {job_id}_chunk_2.csv
  │   └── ...
  └── features/
      ├── {job_id}_features.parquet
      └── ...

Frontend upload example:
const { data } = await supabase.storage
  .from('ais-data')
  .upload(`${userId}/${jobId}/input.csv`, file)
*/