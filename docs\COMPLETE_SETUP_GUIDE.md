# VESLINT Complete Setup & Deployment Guide

## 🎯 Overview: Free Professional Deployment

This guide will help you deploy VESLINT to your **veslint.com** domain completely **FREE** using:
- **Backend**: Render (Free tier)
- **Frontend**: Vercel (Free tier)
- **Database**: Supabase (Free tier)
- **Auth**: Firebase (Free tier)
- **Domain**: Cloudflare (Free tier)
- **ML Model**: Your trained model on Render

**Total Cost: $0/month** ✅

## 📋 Prerequisites

### Required Accounts (All Free)
1. **GitHub Account** (for code deployment)
2. **Render Account** (backend hosting)
3. **Vercel Account** (frontend hosting)
4. **Supabase Account** (database & storage)
5. **Firebase Account** (authentication)
6. **Cloudflare Account** (domain & DNS)

### Required Software
```bash
# Install these on your local machine
node --version   # v18+ required
python --version # v3.11+ required
git --version    # Any recent version
```

## 🚀 Phase 1: Local Development Setup (30 minutes)

### Step 1: Copy Your Trained Model
```bash
# Copy your existing trained model to the new structure
cp functions/assets/extreme_maritime_classifier.joblib backend/src/assets/

# Verify the model file is there
ls -la backend/src/assets/extreme_maritime_classifier.joblib
```

### Step 2: Install Backend Dependencies
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### Step 3: Set Up Local Environment
```bash
# Create local environment file
cp .env.example .env

# Edit .env with your local settings
nano .env
```

Add to `.env`:
```bash
ENVIRONMENT=development
PYTHONPATH=./src
LOG_LEVEL=DEBUG

# These will be set later for production
SUPABASE_URL=
SUPABASE_SERVICE_KEY=
FIREBASE_PROJECT_ID=
FIREBASE_CREDENTIALS_JSON=
```

### Step 4: Test Backend Locally
```bash
# Start the backend server
cd backend
python src/main.py

# Should see: "Uvicorn running on http://0.0.0.0:8000"
```

### Step 5: Test Your Model
```bash
# In another terminal, test the model endpoint
curl http://localhost:8000/health
curl http://localhost:8000/api/v1/vessels/model-info

# Should return your model information
```

## 🗄️ Phase 2: Database Setup - Supabase (15 minutes)

### Step 1: Create Supabase Project
1. Go to [supabase.com](https://supabase.com)
2. Sign in with GitHub
3. Click "New Project"
4. Name: `veslint-db`
5. Password: Generate strong password
6. Region: Choose closest to you

### Step 2: Run Database Migrations
```bash
# Install Supabase CLI
npm install -g supabase

# Initialize in your project
cd database
supabase init

# Link to your project
supabase link --project-ref YOUR_PROJECT_REF

# Run migrations
supabase db push
```

### Step 3: Get Supabase Credentials
1. In Supabase dashboard → Settings → API
2. Copy these values:
   - **URL**: `https://xxx.supabase.co`
   - **Anon key**: `eyJhbGc...` (public)
   - **Service key**: `eyJhbGc...` (secret)

### Step 4: Test Database Connection
```bash
# Update your backend/.env with Supabase credentials
SUPABASE_URL=https://xxx.supabase.co
SUPABASE_SERVICE_KEY=your_service_key_here
SUPABASE_ANON_KEY=your_anon_key_here

# Restart backend and test
curl http://localhost:8000/health/detailed
# Should show database as "healthy"
```

## 🔐 Phase 3: Authentication Setup - Firebase (15 minutes)

### Step 1: Create Firebase Project
1. Go to [console.firebase.google.com](https://console.firebase.google.com)
2. Click "Create a project"
3. Name: `veslint-auth`
4. Enable Google Analytics (optional)

### Step 2: Enable Authentication
1. In Firebase console → Authentication → Get started
2. Sign-in method → Enable:
   - **Email/Password**
   - **Google** (optional but recommended)

### Step 3: Create Web App
1. Project Overview → Add app → Web (</>) 
2. App nickname: `veslint-frontend`
3. Copy the Firebase config object

### Step 4: Generate Service Account
1. Project settings → Service accounts
2. Generate new private key
3. Download the JSON file

### Step 5: Configure Backend Auth
```bash
# Convert service account JSON to base64
base64 -i path/to/your-service-account.json

# Add to backend/.env
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_CREDENTIALS_JSON=your_base64_encoded_json
```

## 🎨 Phase 4: Frontend Migration (45 minutes)

### Step 1: Create Next.js Frontend
```bash
# Create new Next.js app
npx create-next-app@latest frontend-new --typescript --tailwind --eslint --src-dir --app

cd frontend-new
```

### Step 2: Install Required Dependencies
```bash
npm install @mui/material @emotion/react @emotion/styled
npm install firebase
npm install @supabase/supabase-js
npm install framer-motion
npm install recharts
npm install react-dropzone
```

### Step 3: Migrate Your Existing Files

**Copy these files AS-IS (no content changes):**

```bash
# Components (copy all exactly as they are)
cp -r frontend/src/components/* frontend-new/src/components/

# Hooks (copy all exactly as they are)  
cp -r frontend/src/hooks/* frontend-new/src/hooks/

# Theme files
cp -r frontend/src/theme/* frontend-new/src/lib/

# Public assets
cp -r frontend/public/* frontend-new/public/
```

**Convert Pages to App Router:**

Move your page components:
```bash
# Landing page
mv frontend/src/pages/LandingPage.tsx frontend-new/src/app/page.tsx

# Dashboard
mkdir -p frontend-new/src/app/dashboard
mv frontend/src/pages/DashboardPage.tsx frontend-new/src/app/dashboard/page.tsx

# Continue for all pages...
```

### Step 4: Create Configuration Files

Create `frontend-new/src/lib/supabase.ts`:
```typescript
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
})
```

Create `frontend-new/src/lib/api.ts`:
```typescript
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

export const api = {
  baseURL: API_BASE_URL,
  
  async classifyVessels(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    
    const response = await fetch(`${API_BASE_URL}/api/v1/vessels/classify`, {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': `Bearer ${await getAuthToken()}`
      }
    })
    
    return response.json()
  }
}

async function getAuthToken() {
  // Get Firebase auth token
  const { getAuth } = await import('firebase/auth')
  const auth = getAuth()
  return auth.currentUser?.getIdToken() || ''
}
```

### Step 5: Test Frontend Locally
```bash
cd frontend-new

# Create .env.local
cat > .env.local << EOF
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
EOF

# Start development server
npm run dev

# Visit http://localhost:3000
```

## 🚀 Phase 5: Production Deployment (30 minutes)

### Step 1: Deploy Backend to Render
```bash
# Run the deployment script
chmod +x deployment/deploy_backend.sh
./deployment/deploy_backend.sh

# Push to GitHub
git add .
git commit -m "Ready for Render deployment"
git push origin main
```

**In Render Dashboard:**
1. Connect GitHub repository
2. Create Web Service
3. Settings:
   - **Runtime**: Python 3
   - **Build**: `pip install -r requirements.txt`
   - **Start**: `gunicorn src.main:app -w 1 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:$PORT`
   - **Root Directory**: `backend`

4. Add Environment Variables (from backend/.env.render)
5. Deploy (takes ~5 minutes)

### Step 2: Deploy Frontend to Vercel
```bash
cd frontend-new

# Push to GitHub
git init
git add .
git commit -m "VESLINT Next.js frontend"
git push origin main
```

**In Vercel Dashboard:**
1. Import GitHub repository
2. Framework: Next.js
3. Root Directory: `frontend-new`
4. Add Environment Variables:
```bash
NEXT_PUBLIC_API_URL=https://veslint-api.onrender.com
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
```
5. Deploy (takes ~2 minutes)

## 🌐 Phase 6: Domain Setup - Cloudflare (20 minutes)

### Step 1: Configure DNS in Cloudflare
1. Login to Cloudflare
2. Select your `veslint.com` domain
3. Go to DNS → Records

Add these records:
```
Type: CNAME
Name: @
Target: your-frontend.vercel.app
Proxy: On (orange cloud)

Type: CNAME  
Name: api
Target: veslint-api.onrender.com
Proxy: On (orange cloud)
```

### Step 2: Configure Custom Domains

**In Vercel:**
1. Project Settings → Domains
2. Add `veslint.com` and `www.veslint.com`
3. Vercel will verify automatically

**In Render:**
1. Service Settings → Custom Domains
2. Add `api.veslint.com`
3. Render will verify automatically

### Step 3: Update Environment Variables
Update your frontend environment to use the custom domain:
```bash
NEXT_PUBLIC_API_URL=https://api.veslint.com
```

Redeploy frontend in Vercel.

## 🧪 Phase 7: Complete System Testing (20 minutes)

### Step 1: Test All Endpoints
```bash
# Backend health
curl https://api.veslint.com/health

# Model info
curl https://api.veslint.com/api/v1/vessels/model-info

# API docs
open https://api.veslint.com/docs
```

### Step 2: Test Full User Flow
1. **Visit**: https://veslint.com
2. **Register**: Create new account
3. **Upload**: Test CSV file with your AIS data
4. **Monitor**: Watch real-time progress
5. **Results**: View classification results
6. **Download**: Export results

### Step 3: Test Your Trained Model
```bash
# Create test CSV file with your AIS data format
cat > test_vessels.csv << EOF
mmsi,timestamp,lat,lon,sog,cog,heading
*********,2024-01-01T00:00:00Z,40.7128,-74.0060,12.5,180,175
*********,2024-01-01T00:05:00Z,40.7130,-74.0058,12.8,182,178
*********,2024-01-01T00:00:00Z,51.5074,-0.1278,8.2,90,95
*********,2024-01-01T00:10:00Z,51.5100,-0.1200,8.5,92,98
EOF

# Test direct classification
curl -X POST https://api.veslint.com/api/v1/vessels/classify \
  -H "Authorization: Bearer YOUR_FIREBASE_TOKEN" \
  -F "file=@test_vessels.csv"
```

## 📊 Monitoring & Maintenance

### Performance Monitoring
- **Render**: Monitor backend performance and logs
- **Vercel**: Monitor frontend performance and deployments  
- **Supabase**: Monitor database usage and real-time connections
- **Cloudflare**: Monitor domain traffic and security

### Free Tier Limits
- **Render**: 750 hours/month (auto-sleep after 15 min)
- **Vercel**: 100GB bandwidth/month
- **Supabase**: 500MB database + 1GB storage
- **Firebase**: 10k authentications/month

### Scaling (When You Outgrow Free Tiers)
- **Render**: $7/month for always-on service
- **Vercel**: $20/month for Pro features
- **Supabase**: $25/month for Pro tier
- **Firebase**: Pay-as-you-go pricing

## 🎉 Success! Your System is Live

Your VESLINT system is now running professionally at:
- **Frontend**: https://veslint.com
- **API**: https://api.veslint.com
- **Documentation**: https://api.veslint.com/docs

**Features Working:**
- ✅ Your trained ML model serving predictions
- ✅ Real-time job progress monitoring
- ✅ Professional UI with all existing features
- ✅ File upload and processing
- ✅ User authentication and security
- ✅ Custom domain with SSL certificates
- ✅ Auto-scaling and high availability

## 🆘 Troubleshooting

### Common Issues
1. **Model not loading**: Check file path and permissions
2. **Database connection failed**: Verify Supabase credentials
3. **Auth not working**: Check Firebase configuration
4. **CORS errors**: Update backend CORS origins
5. **Cold starts**: First request after 15 min takes ~30 seconds (normal on free tier)

### Support
- Check logs in Render/Vercel dashboards
- Use the health check endpoints
- Monitor Supabase real-time connections
- Review this guide for configuration issues

## 🎯 Next Steps
- Set up monitoring and alerts
- Add custom analytics
- Implement rate limiting for production
- Consider upgrading to paid tiers for high traffic
- Add more vessel classification features

**Your professional maritime intelligence platform is now live and ready for users!** 🚢⚓