# VESLINT Deployment Guide

This comprehensive guide covers deploying VESLINT to production environments, including cloud platforms, custom domains, monitoring, and maintenance procedures.

## 📋 Table of Contents

- [Overview](#overview)
- [Prerequisites](#prerequisites)
- [Cloud Platform Deployment](#cloud-platform-deployment)
- [Custom Domain Setup](#custom-domain-setup)
- [Environment Configuration](#environment-configuration)
- [Security Configuration](#security-configuration)
- [Monitoring and Logging](#monitoring-and-logging)
- [Scaling and Performance](#scaling-and-performance)
- [Backup and Recovery](#backup-and-recovery)
- [Maintenance and Updates](#maintenance-and-updates)
- [Troubleshooting](#troubleshooting)

## 🎯 Overview

VESLINT uses a modern, cloud-native architecture designed for scalability, reliability, and cost-effectiveness. The production deployment consists of:

### Architecture Components

```mermaid
graph TB
    A[👥 Users] --> B[🌐 CDN/Cloudflare]
    B --> C[🎨 Frontend - Vercel]
    C --> D[⚡ API Gateway]
    D --> E[🚀 Backend - Render]
    E --> F[🗄️ Database - Supabase]
    E --> G[📁 Storage - Supabase]
    E --> H[🤖 ML Model - HuggingFace]
    
    subgraph "🔧 Monitoring"
        I[📊 Analytics]
        J[🚨 Error Tracking]
        K[📈 Performance]
    end
```

### Deployment Strategy

- **Frontend**: Static site deployment on Vercel
- **Backend**: Containerized API on Render
- **Database**: Managed PostgreSQL on Supabase
- **Storage**: Object storage on Supabase
- **CDN**: Cloudflare for global distribution
- **Domain**: Custom domain with SSL certificates

### Cost Structure (Free Tier)

| Service | Free Tier Limits | Estimated Monthly Cost |
|---------|------------------|------------------------|
| **Vercel** | 100GB bandwidth, 1000 build minutes | $0 |
| **Render** | 750 hours/month, 512MB RAM | $0 |
| **Supabase** | 500MB database, 1GB storage | $0 |
| **Cloudflare** | Unlimited requests, basic CDN | $0 |
| **Custom Domain** | Domain registration | $10-15/year |
| **Total** | - | **~$1-2/month** |

## 📋 Prerequisites

### Required Accounts

1. **GitHub Account**: For code repository
2. **Vercel Account**: For frontend deployment
3. **Render Account**: For backend deployment
4. **Supabase Account**: For database and storage
5. **Firebase Account**: For authentication (optional)
6. **HuggingFace Account**: For ML model hosting (optional)
7. **Cloudflare Account**: For domain and CDN (optional)

### Required Tools

- **Git**: Version control
- **Node.js** 16+: For build processes
- **Python** 3.8+: For backend development
- **Docker**: For containerization (optional)

### Domain Requirements

- **Domain Name**: Purchased from registrar (e.g., veslint.com)
- **DNS Access**: Ability to modify DNS records

## ☁️ Cloud Platform Deployment

### Step 1: Database Setup (Supabase)

#### 1.1 Create Supabase Project

```bash
# Visit https://supabase.com/dashboard
# 1. Create new project
# 2. Choose region (closest to users)
# 3. Set strong database password
# 4. Wait for project initialization
```

#### 1.2 Configure Database Schema

```bash
# Option 1: Using Supabase Dashboard
# 1. Go to SQL Editor
# 2. Run migration files in order:
#    - 001_initial_schema.sql
#    - 002_enable_realtime.sql
#    - 003_storage_buckets.sql
#    - 004_rls_policies.sql

# Option 2: Using Supabase CLI
supabase login
supabase link --project-ref YOUR_PROJECT_REF
supabase db push
```

#### 1.3 Get Supabase Credentials

```bash
# From Supabase Dashboard > Settings > API
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### Step 2: Backend Deployment (Render)

#### 2.1 Prepare Repository

```bash
# Ensure all backend files are committed
git add backend/
git commit -m "Prepare backend for deployment"
git push origin main
```

#### 2.2 Deploy to Render

```bash
# Option 1: Automated Script
export SUPABASE_URL=your_supabase_url
export SUPABASE_ANON_KEY=your_anon_key
export SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
./scripts/deploy_backend.sh

# Option 2: Manual Deployment
# 1. Visit https://dashboard.render.com
# 2. Click "New +" → "Web Service"
# 3. Connect GitHub repository
# 4. Configure service:
#    - Name: veslint-api
#    - Runtime: Docker
#    - Branch: main
#    - Root Directory: backend
#    - Dockerfile Path: ./Dockerfile
```

#### 2.3 Configure Environment Variables

In Render Dashboard → Environment:

```bash
# Required Variables
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Application Configuration
ENVIRONMENT=production
LOG_LEVEL=INFO
CORS_ORIGINS=https://veslint.com,https://www.veslint.com

# Performance Configuration
MAX_WORKERS=4
WORKER_TIMEOUT=120

# Optional: Firebase Auth
FIREBASE_PROJECT_ID=your-firebase-project

# Optional: HuggingFace
HUGGINGFACE_API_TOKEN=your-hf-token

# Optional: Monitoring
SENTRY_DSN=your-sentry-dsn
```

#### 2.4 Verify Backend Deployment

```bash
# Check deployment status
curl https://your-service.onrender.com/health

# Expected response:
{
  "success": true,
  "data": {
    "status": "healthy",
    "version": "2.0.0",
    "database": "connected"
  }
}
```

### Step 3: Frontend Deployment (Vercel)

#### 3.1 Prepare Frontend

```bash
# Ensure frontend builds successfully
cd frontend
npm install
npm run build
npm run type-check
cd ..
```

#### 3.2 Deploy to Vercel

```bash
# Option 1: Automated Script
export NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
export NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
export NEXT_PUBLIC_API_URL=https://your-backend.onrender.com
./scripts/deploy_frontend.sh

# Option 2: Manual Deployment
# 1. Install Vercel CLI: npm i -g vercel
# 2. Login: vercel login
# 3. Deploy: vercel --prod
```

#### 3.3 Configure Environment Variables

In Vercel Dashboard → Settings → Environment Variables:

```bash
# Required Variables
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
NEXT_PUBLIC_API_URL=https://your-backend.onrender.com

# Application Configuration
NEXT_PUBLIC_APP_NAME=VESLINT
NEXT_PUBLIC_ENVIRONMENT=production

# Optional: Firebase Auth
NEXT_PUBLIC_FIREBASE_API_KEY=your-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id

# Optional: Analytics
NEXT_PUBLIC_ANALYTICS_ID=your-analytics-id
```

#### 3.4 Verify Frontend Deployment

```bash
# Check deployment
curl https://your-project.vercel.app

# Test API connectivity
curl https://your-project.vercel.app/api/health
```

## 🌐 Custom Domain Setup

### Step 1: Domain Configuration

#### 1.1 DNS Records Setup

Configure the following DNS records in your domain registrar:

```bash
# For veslint.com
Type    Name    Value                              TTL
A       @       76.76.19.61                       300
CNAME   www     cname.vercel-dns.com              300
CNAME   api     your-backend.onrender.com         300

# Optional subdomains
CNAME   admin   your-admin.vercel.app             300
CNAME   docs    your-docs.vercel.app              300
```

#### 1.2 Cloudflare Setup (Optional)

```bash
# 1. Add domain to Cloudflare
# 2. Update nameservers at registrar
# 3. Configure DNS records in Cloudflare:

Type    Name    Value                              Proxied
A       @       76.76.19.61                       Yes
CNAME   www     veslint.com                       Yes
CNAME   api     your-backend.onrender.com         No

# 4. SSL/TLS Settings:
#    - Encryption Mode: Full (strict)
#    - Always Use HTTPS: On
#    - HSTS: Enabled
```

### Step 2: Vercel Domain Configuration

```bash
# In Vercel Dashboard:
# 1. Go to Project Settings → Domains
# 2. Add Custom Domain: veslint.com
# 3. Add Custom Domain: www.veslint.com
# 4. Verify DNS configuration
# 5. Wait for SSL certificate provisioning
```

### Step 3: Backend Domain Configuration

```bash
# In Render Dashboard:
# 1. Go to Service Settings
# 2. Add Custom Domain: api.veslint.com
# 3. Update CORS_ORIGINS environment variable:
CORS_ORIGINS=https://veslint.com,https://www.veslint.com

# 4. Verify SSL certificate
curl https://api.veslint.com/health
```

### Step 4: Domain Verification

```bash
# Test all domains
curl https://veslint.com
curl https://www.veslint.com
curl https://api.veslint.com/health

# Check SSL certificates
openssl s_client -connect veslint.com:443 -servername veslint.com
openssl s_client -connect api.veslint.com:443 -servername api.veslint.com
```

## ⚙️ Environment Configuration

### Production Environment Variables

#### Backend (.env)

```bash
# Application
ENVIRONMENT=production
LOG_LEVEL=INFO
API_VERSION=v1
DEBUG=false

# Database
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Security
CORS_ORIGINS=https://veslint.com,https://www.veslint.com
TRUSTED_HOSTS=api.veslint.com,veslint-api.onrender.com
SECURE_COOKIES=true
SESSION_TIMEOUT=3600

# Performance
MAX_WORKERS=4
WORKER_TIMEOUT=120
KEEP_ALIVE=2
MAX_REQUESTS=1000
MAX_REQUESTS_JITTER=50

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=3600

# File Upload
MAX_FILE_SIZE=52428800
ALLOWED_FILE_TYPES=csv,txt

# ML Model
MODEL_CACHE_SIZE=100
FEATURE_CACHE_TTL=300
PREDICTION_TIMEOUT=30
HUGGINGFACE_API_TOKEN=your-hf-token

# Authentication
FIREBASE_PROJECT_ID=your-firebase-project
JWT_SECRET_KEY=your-secret-key

# Monitoring
SENTRY_DSN=your-sentry-dsn
SENTRY_ENVIRONMENT=production
ENABLE_METRICS=true

# Email (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
```

#### Frontend (.env.production)

```bash
# Application
NEXT_PUBLIC_APP_NAME=VESLINT
NEXT_PUBLIC_APP_VERSION=2.0.0
NEXT_PUBLIC_ENVIRONMENT=production

# API Configuration
NEXT_PUBLIC_API_URL=https://api.veslint.com
NEXT_PUBLIC_API_VERSION=v1

# Database
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Authentication
NEXT_PUBLIC_FIREBASE_API_KEY=your-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789
NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_ERROR_REPORTING=true
NEXT_PUBLIC_ENABLE_REAL_TIME=true

# Performance
NEXT_PUBLIC_MAX_FILE_SIZE=52428800
NEXT_PUBLIC_CHUNK_SIZE=1048576
NEXT_PUBLIC_REQUEST_TIMEOUT=30000

# Security
NEXT_PUBLIC_TRUSTED_DOMAINS=veslint.com,www.veslint.com
NEXT_PUBLIC_CSP_ENABLED=true

# Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
NEXT_PUBLIC_HOTJAR_ID=your-hotjar-id

# Monitoring
NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn
```

## 🔒 Security Configuration

### SSL/TLS Configuration

#### Cloudflare SSL Settings

```bash
# SSL/TLS Tab in Cloudflare:
Encryption Mode: Full (strict)
Always Use HTTPS: On
HTTP Strict Transport Security (HSTS): Enabled
  - Max Age Header: 6 months
  - Include Subdomains: On
  - Preload: On
Minimum TLS Version: 1.2
Opportunistic Encryption: On
TLS 1.3: Enabled
```

#### Security Headers

Configure security headers in Vercel (`vercel.json`):

```json
{
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        },
        {
          "key": "Referrer-Policy",
          "value": "strict-origin-when-cross-origin"
        },
        {
          "key": "Content-Security-Policy",
          "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://api.veslint.com https://*.supabase.co;"
        }
      ]
    }
  ]
}
```

### Database Security

#### Supabase RLS Policies

```sql
-- Enable RLS on all tables
ALTER TABLE jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE vessels ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- User isolation policies
CREATE POLICY "Users can only access their own jobs" ON jobs
    FOR ALL USING (user_id = auth.jwt() ->> 'sub');

CREATE POLICY "Users can only access their own vessels" ON vessels
    FOR ALL USING (job_id IN (
        SELECT id FROM jobs WHERE user_id = auth.jwt() ->> 'sub'
    ));
```

#### API Key Management

```bash
# Rotate API keys regularly
# Use environment variables, never commit keys
# Implement API key scoping and rate limiting
# Monitor API key usage
```

### Authentication Security

#### Firebase Auth Configuration

```javascript
// Security rules for Firebase Auth
{
  "rules": {
    "users": {
      "$uid": {
        ".read": "$uid === auth.uid",
        ".write": "$uid === auth.uid"
      }
    }
  }
}
```

## 📊 Monitoring and Logging

### Application Monitoring

#### 1. Sentry Error Tracking

```bash
# Install Sentry
# Backend
pip install sentry-sdk[fastapi]

# Frontend
npm install @sentry/nextjs

# Configure Sentry
SENTRY_DSN=your-sentry-dsn
SENTRY_ENVIRONMENT=production
SENTRY_RELEASE=2.0.0
```

#### 2. Performance Monitoring

```javascript
// Vercel Analytics
npm install @vercel/analytics

// Custom performance tracking
const trackPerformance = (metric) => {
  if (process.env.NODE_ENV === 'production') {
    analytics.track('performance', metric);
  }
};
```

#### 3. Health Checks

```bash
# Automated health checks
curl https://api.veslint.com/health
curl https://veslint.com

# Health check endpoints return:
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "version": "2.0.0",
  "services": {
    "database": "connected",
    "ml_model": "loaded",
    "storage": "available"
  }
}
```

### Logging Configuration

#### Backend Logging

```python
# logging.py
import logging
import json
from datetime import datetime

class JSONFormatter(logging.Formatter):
    def format(self, record):
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        return json.dumps(log_entry)

# Configure structured logging
logging.basicConfig(
    level=logging.INFO,
    format='%(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

for handler in logging.getLogger().handlers:
    handler.setFormatter(JSONFormatter())
```

#### Frontend Logging

```javascript
// logger.js
class Logger {
  static log(level, message, data = {}) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      url: window.location.href,
      userAgent: navigator.userAgent,
      ...data
    };
    
    if (process.env.NODE_ENV === 'production') {
      // Send to logging service
      fetch('/api/logs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(logEntry)
      });
    } else {
      console.log(logEntry);
    }
  }
  
  static error(message, error) {
    this.log('error', message, { error: error.message, stack: error.stack });
  }
}
```

### Monitoring Dashboards

#### 1. Render Monitoring

- **Resource Usage**: CPU, Memory, Disk
- **Request Metrics**: Response time, error rates
- **Deployment Health**: Build status, uptime

#### 2. Vercel Analytics

- **Performance**: Core Web Vitals, page load times
- **User Experience**: Bounce rate, session duration
- **Geographic Distribution**: User locations, CDN performance

#### 3. Supabase Monitoring

- **Database Performance**: Query performance, connection pool
- **Storage Usage**: File uploads, bandwidth
- **Real-time Connections**: WebSocket connections, message volume

## 📈 Scaling and Performance

### Auto-scaling Configuration

#### Backend Scaling (Render)

```yaml
# render.yaml
services:
  - type: web
    name: veslint-api
    plan: free  # or starter/standard for more resources
    scaling:
      minInstances: 0  # Scale to zero when idle (free tier)
      maxInstances: 1  # Free tier limit
    healthCheckPath: /health
    envVars:
      - key: MAX_WORKERS
        value: 4  # Adjust based on plan
```

#### Frontend Scaling (Vercel)

- **Edge Functions**: Automatic scaling
- **CDN**: Global edge caching
- **Image Optimization**: Automatic optimization and caching

### Performance Optimization

#### Database Optimization

```sql
-- Optimize common queries
CREATE INDEX CONCURRENTLY idx_jobs_user_status 
ON jobs (user_id, status);

CREATE INDEX CONCURRENTLY idx_vessels_job_type 
ON vessels (job_id, predicted_type);

-- Analyze query performance
EXPLAIN ANALYZE SELECT * FROM jobs WHERE user_id = 'user123';
```

#### Caching Strategy

```python
# Redis caching (optional)
import redis
from functools import wraps

redis_client = redis.Redis(host='localhost', port=6379, db=0)

def cache_result(expiration=300):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            cached_result = redis_client.get(cache_key)
            
            if cached_result:
                return json.loads(cached_result)
            
            result = func(*args, **kwargs)
            redis_client.setex(cache_key, expiration, json.dumps(result))
            return result
        return wrapper
    return decorator
```

#### Frontend Performance

```javascript
// Code splitting
const LazyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <Spinner />,
  ssr: false
});

// Image optimization
import Image from 'next/image';

<Image
  src="/vessel-image.jpg"
  alt="Vessel"
  width={800}
  height={600}
  priority={true}
  placeholder="blur"
/>

// API response caching
const fetchWithCache = async (url, options = {}) => {
  const cacheKey = `api_${url}`;
  const cached = sessionStorage.getItem(cacheKey);
  
  if (cached && Date.now() - JSON.parse(cached).timestamp < 300000) {
    return JSON.parse(cached).data;
  }
  
  const response = await fetch(url, options);
  const data = await response.json();
  
  sessionStorage.setItem(cacheKey, JSON.stringify({
    data,
    timestamp: Date.now()
  }));
  
  return data;
};
```

## 💾 Backup and Recovery

### Database Backup

#### Automated Supabase Backups

```bash
# Supabase provides automatic backups
# Daily backups for 7 days (free tier)
# Point-in-time recovery available

# Manual backup
supabase db dump --file backup.sql

# Restore from backup
supabase db reset --file backup.sql
```

#### Custom Backup Script

```bash
#!/bin/bash
# backup_database.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"
BACKUP_FILE="veslint_backup_$DATE.sql"

# Create backup
pg_dump $DATABASE_URL > "$BACKUP_DIR/$BACKUP_FILE"

# Compress backup
gzip "$BACKUP_DIR/$BACKUP_FILE"

# Upload to cloud storage (optional)
aws s3 cp "$BACKUP_DIR/$BACKUP_FILE.gz" s3://veslint-backups/

# Cleanup old backups (keep last 30 days)
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete

echo "Backup completed: $BACKUP_FILE.gz"
```

### Application Backup

#### Source Code Backup

```bash
# Git repository serves as primary backup
# Ensure regular commits and pushes
git add .
git commit -m "Backup commit $(date)"
git push origin main

# Tag releases
git tag -a v2.0.0 -m "Release version 2.0.0"
git push origin v2.0.0
```

#### Configuration Backup

```bash
# Environment variables backup
# Store in secure location (not in repository)
# Use secrets management service

# Example backup script
#!/bin/bash
echo "# Environment backup $(date)" > env_backup.txt
echo "SUPABASE_URL=***" >> env_backup.txt
echo "SUPABASE_ANON_KEY=***" >> env_backup.txt
# ... other variables

# Encrypt backup
gpg --cipher-algo AES256 --compress-algo 1 --s2k-digest-algo SHA512 \
    --cert-digest-algo SHA512 --encrypt --armor \
    -r <EMAIL> env_backup.txt
```

### Disaster Recovery Plan

#### Recovery Procedures

1. **Database Recovery**:
   ```bash
   # Restore from backup
   supabase db reset --file latest_backup.sql
   
   # Verify data integrity
   psql -c "SELECT COUNT(*) FROM jobs;"
   psql -c "SELECT COUNT(*) FROM vessels;"
   ```

2. **Application Recovery**:
   ```bash
   # Redeploy from repository
   git clone https://github.com/veslint/veslint.git
   ./scripts/deploy_backend.sh
   ./scripts/deploy_frontend.sh
   ```

3. **DNS Recovery**:
   ```bash
   # Update DNS records if needed
   # Verify propagation
   dig veslint.com
   dig api.veslint.com
   ```

## 🔄 Maintenance and Updates

### Deployment Pipeline

#### Automated Deployment

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]
    tags: ['v*']

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run tests
        run: npm run test

  deploy-backend:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to Render
        run: ./scripts/deploy_backend.sh
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}

  deploy-frontend:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to Vercel
        run: ./scripts/deploy_frontend.sh
        env:
          VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
```

#### Manual Deployment

```bash
# 1. Test locally
npm run test:system

# 2. Deploy backend
./scripts/deploy_backend.sh

# 3. Deploy frontend
./scripts/deploy_frontend.sh

# 4. Verify deployment
curl https://api.veslint.com/health
curl https://veslint.com

# 5. Run smoke tests
npm run test:production
```

### Update Procedures

#### Database Migrations

```bash
# 1. Create migration file
# database/migrations/005_new_feature.sql

# 2. Test migration locally
supabase db reset
supabase db push

# 3. Deploy to production
supabase db push --linked
```

#### Application Updates

```bash
# 1. Update version numbers
# package.json, backend/src/main.py

# 2. Update CHANGELOG.md
# Document changes and breaking changes

# 3. Create release tag
git tag -a v2.1.0 -m "Release v2.1.0"
git push origin v2.1.0

# 4. Deploy updates
./scripts/deploy.sh
```

### Maintenance Schedule

#### Weekly Tasks

- [ ] Review monitoring alerts
- [ ] Check database performance
- [ ] Update dependencies (patch versions)
- [ ] Review error logs
- [ ] Backup verification

#### Monthly Tasks

- [ ] Security updates
- [ ] Performance optimization review
- [ ] Capacity planning review
- [ ] Update documentation
- [ ] Dependency major version updates

#### Quarterly Tasks

- [ ] Security audit
- [ ] Disaster recovery testing
- [ ] Performance benchmarking
- [ ] Architecture review
- [ ] Cost optimization review

## 🔧 Troubleshooting

### Common Deployment Issues

#### Backend Deployment Failures

```bash
# Issue: Build fails on Render
# Solution: Check build logs and fix dependencies

# Issue: Health check fails
# Solution: Verify environment variables and database connection
curl https://your-service.onrender.com/health

# Issue: Model loading fails
# Solution: Check model files and HuggingFace token
```

#### Frontend Deployment Failures

```bash
# Issue: Build fails on Vercel
# Solution: Check TypeScript errors and dependencies

# Issue: Environment variables not loaded
# Solution: Verify environment variables in Vercel dashboard

# Issue: API connection fails
# Solution: Check CORS configuration and API URL
```

#### Database Connection Issues

```bash
# Issue: Connection timeouts
# Solution: Check Supabase status and connection limits

# Issue: Migration failures
# Solution: Check for syntax errors and conflicts

# Issue: Performance issues
# Solution: Analyze slow queries and add indexes
```

### Performance Issues

#### Slow API Responses

```bash
# 1. Check backend logs
# 2. Analyze database queries
# 3. Review ML model performance
# 4. Check network latency

# Debug commands
curl -w "@curl-format.txt" -o /dev/null -s https://api.veslint.com/health
```

#### High Resource Usage

```bash
# 1. Monitor Render metrics
# 2. Check memory usage
# 3. Analyze CPU utilization
# 4. Review background jobs

# Optimization strategies
# - Implement caching
# - Optimize database queries
# - Use connection pooling
# - Implement rate limiting
```

### Emergency Procedures

#### Service Outage

1. **Immediate Response**:
   ```bash
   # Check service status
   curl https://api.veslint.com/health
   curl https://veslint.com
   
   # Check external services
   # - Supabase status
   # - Render status
   # - Vercel status
   ```

2. **Rollback Procedure**:
   ```bash
   # Rollback to previous version
   git checkout previous-stable-tag
   ./scripts/deploy.sh
   ```

3. **Communication**:
   ```bash
   # Update status page
   # Notify users via email/social media
   # Document incident for post-mortem
   ```

#### Data Loss Recovery

1. **Assess Impact**:
   ```bash
   # Check what data is affected
   # Determine time range of loss
   # Identify affected users
   ```

2. **Restore from Backup**:
   ```bash
   # Restore database from latest backup
   supabase db reset --file latest_backup.sql
   
   # Verify data integrity
   # Test application functionality
   ```

3. **Post-Recovery**:
   ```bash
   # Notify affected users
   # Document incident
   # Implement preventive measures
   ```

## 📞 Support and Contacts

### Deployment Support

- **GitHub Issues**: Technical deployment issues
- **Email**: <EMAIL>
- **Emergency**: +1-XXX-XXX-XXXX (24/7 hotline)

### Service Providers

- **Vercel Support**: https://vercel.com/support
- **Render Support**: https://render.com/docs
- **Supabase Support**: https://supabase.com/support
- **Cloudflare Support**: https://support.cloudflare.com

### Monitoring Contacts

- **Uptime Monitoring**: <EMAIL>
- **Security Alerts**: <EMAIL>
- **Performance Issues**: <EMAIL>

---

This deployment guide provides comprehensive coverage for deploying and maintaining VESLINT in production. For additional support or questions, please contact our deployment team.