#!/bin/bash

# =============================================================================
# VESLINT Frontend Deployment Script for Vercel
# =============================================================================
# This script deploys the VESLINT Next.js frontend to Vercel.com
# Supports free tier deployment with optimizations and custom domain setup

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
PROJECT_NAME="veslint"
DEPLOY_BRANCH="main"
CUSTOM_DOMAIN="veslint.com"
HEALTH_CHECK_TIMEOUT=180  # 3 minutes

# =============================================================================
# Utility Functions
# =============================================================================

print_header() {
    echo -e "\n${PURPLE}================================================${NC}"
    echo -e "${WHITE}$1${NC}"
    echo -e "${PURPLE}================================================${NC}\n"
}

print_step() {
    echo -e "${CYAN}▶ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

check_command() {
    if command -v "$1" &> /dev/null; then
        return 0
    else
        return 1
    fi
}

wait_for_deployment() {
    local deployment_url="$1"
    local timeout="$2"
    local check_interval=10
    local elapsed=0
    
    print_step "Waiting for deployment to become available..."
    
    while [ $elapsed -lt $timeout ]; do
        if curl -s -f "$deployment_url" >/dev/null 2>&1; then
            print_success "Deployment is live!"
            return 0
        fi
        
        echo -n "."
        sleep $check_interval
        elapsed=$((elapsed + check_interval))
    done
    
    print_error "Deployment availability check timed out"
    return 1
}

# =============================================================================
# Prerequisites Check
# =============================================================================

check_prerequisites() {
    print_header "Checking Prerequisites"
    
    print_step "Checking required tools"
    
    local missing_tools=()
    
    if ! check_command "git"; then
        missing_tools+=("git")
    fi
    
    if ! check_command "node"; then
        missing_tools+=("node")
    fi
    
    if ! check_command "npm"; then
        missing_tools+=("npm")
    fi
    
    if ! check_command "curl"; then
        missing_tools+=("curl")
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        print_error "Missing required tools: ${missing_tools[*]}"
        print_info "Please install missing tools and try again"
        exit 1
    fi
    
    print_step "Checking Node.js version"
    node_version=$(node --version | cut -d'v' -f2)
    if node -e "process.exit(parseInt(process.version.slice(1)) >= 16 ? 0 : 1)"; then
        print_success "Node.js version $node_version is compatible"
    else
        print_error "Node.js 16 or higher is required, found $node_version"
        exit 1
    fi
    
    print_step "Checking for Vercel CLI"
    if ! check_command "vercel"; then
        print_step "Installing Vercel CLI"
        npm install -g vercel@latest
        print_success "Vercel CLI installed"
    else
        print_success "Vercel CLI is available"
    fi
    
    print_step "Checking Git repository status"
    cd "$PROJECT_ROOT"
    
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        print_error "Not in a Git repository"
        exit 1
    fi
    
    # Check if we're on the correct branch
    current_branch=$(git branch --show-current)
    if [ "$current_branch" != "$DEPLOY_BRANCH" ]; then
        print_warning "Currently on branch '$current_branch', not '$DEPLOY_BRANCH'"
        read -p "Continue with current branch? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "Please switch to '$DEPLOY_BRANCH' branch and try again"
            exit 1
        fi
        DEPLOY_BRANCH="$current_branch"
    fi
    
    print_step "Checking frontend directory"
    if [ ! -d "$FRONTEND_DIR" ]; then
        print_error "Frontend directory not found: $FRONTEND_DIR"
        exit 1
    fi
    
    print_step "Checking required frontend files"
    local required_files=(
        "$FRONTEND_DIR/package.json"
        "$FRONTEND_DIR/next.config.js"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            print_error "Required file not found: $file"
            exit 1
        fi
    done
    
    print_success "Prerequisites check passed"
}

# =============================================================================
# Environment Configuration
# =============================================================================

setup_environment() {
    print_header "Setting Up Environment Configuration"
    
    cd "$FRONTEND_DIR"
    
    print_step "Checking environment variables"
    
    # Required environment variables for production
    local required_vars=(
        "NEXT_PUBLIC_SUPABASE_URL"
        "NEXT_PUBLIC_SUPABASE_ANON_KEY"
        "NEXT_PUBLIC_API_URL"
    )
    
    # Optional environment variables
    local optional_vars=(
        "NEXT_PUBLIC_FIREBASE_API_KEY"
        "NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN"
        "NEXT_PUBLIC_FIREBASE_PROJECT_ID"
        "NEXT_PUBLIC_SENTRY_DSN"
        "NEXT_PUBLIC_ANALYTICS_ID"
        "VERCEL_TOKEN"
    )
    
    print_step "Checking required environment variables"
    local missing_required=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_required+=("$var")
        else
            print_success "$var is set"
        fi
    done
    
    if [ ${#missing_required[@]} -ne 0 ]; then
        print_error "Missing required environment variables: ${missing_required[*]}"
        print_info "Please set these variables and try again:"
        for var in "${missing_required[@]}"; do
            echo "  export $var=your_value_here"
        done
        
        print_info "Example values:"
        echo "  export NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co"
        echo "  export NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key"
        echo "  export NEXT_PUBLIC_API_URL=https://veslint-api.onrender.com"
        exit 1
    fi
    
    print_step "Checking optional environment variables"
    for var in "${optional_vars[@]}"; do
        if [ -z "${!var}" ]; then
            print_warning "$var is not set (optional)"
        else
            print_success "$var is set"
        fi
    done
    
    # Create production environment file
    print_step "Creating production environment configuration"
    cat > .env.production << EOF
# VESLINT Frontend Production Environment
# Generated on $(date)

# API Configuration
NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL
NEXT_PUBLIC_API_VERSION=v1

# Database Configuration
NEXT_PUBLIC_SUPABASE_URL=$NEXT_PUBLIC_SUPABASE_URL
NEXT_PUBLIC_SUPABASE_ANON_KEY=$NEXT_PUBLIC_SUPABASE_ANON_KEY

# Application Configuration
NEXT_PUBLIC_APP_NAME=VESLINT
NEXT_PUBLIC_APP_VERSION=2.0.0
NEXT_PUBLIC_ENVIRONMENT=production

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_ERROR_REPORTING=true
NEXT_PUBLIC_ENABLE_REAL_TIME=true

# Performance Configuration
NEXT_PUBLIC_MAX_FILE_SIZE=52428800
NEXT_PUBLIC_CHUNK_SIZE=1048576
NEXT_PUBLIC_REQUEST_TIMEOUT=30000

# Security Configuration
NEXT_PUBLIC_TRUSTED_DOMAINS=veslint.com,www.veslint.com
NEXT_PUBLIC_CSP_ENABLED=true
EOF

    # Add optional variables if they exist
    if [ -n "$NEXT_PUBLIC_FIREBASE_API_KEY" ]; then
        cat >> .env.production << EOF

# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=$NEXT_PUBLIC_FIREBASE_API_KEY
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=$NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN
NEXT_PUBLIC_FIREBASE_PROJECT_ID=$NEXT_PUBLIC_FIREBASE_PROJECT_ID
EOF
    fi
    
    if [ -n "$NEXT_PUBLIC_SENTRY_DSN" ]; then
        echo "NEXT_PUBLIC_SENTRY_DSN=$NEXT_PUBLIC_SENTRY_DSN" >> .env.production
    fi
    
    if [ -n "$NEXT_PUBLIC_ANALYTICS_ID" ]; then
        echo "NEXT_PUBLIC_ANALYTICS_ID=$NEXT_PUBLIC_ANALYTICS_ID" >> .env.production
    fi
    
    print_success "Environment configuration created"
}

# =============================================================================
# Pre-deployment Tests
# =============================================================================

run_predeploy_tests() {
    print_header "Running Pre-deployment Tests"
    
    cd "$FRONTEND_DIR"
    
    print_step "Installing dependencies"
    npm ci --silent
    print_success "Dependencies installed"
    
    print_step "Running TypeScript type check"
    npm run type-check || {
        print_error "TypeScript type check failed"
        exit 1
    }
    print_success "TypeScript type check passed"
    
    print_step "Running ESLint"
    npm run lint || {
        print_warning "ESLint found issues, attempting to fix..."
        npm run lint:fix || {
            print_error "ESLint issues could not be automatically fixed"
            read -p "Continue anyway? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                exit 1
            fi
        }
    }
    print_success "ESLint check passed"
    
    print_step "Running build test"
    npm run build || {
        print_error "Build test failed"
        exit 1
    }
    print_success "Build test passed"
    
    print_step "Running unit tests (if available)"
    if npm run test --silent -- --watchAll=false --passWithNoTests 2>/dev/null; then
        print_success "Unit tests passed"
    else
        print_warning "Unit tests not available or failed"
    fi
    
    print_step "Analyzing bundle size"
    if [ -d ".next" ]; then
        # Check if bundle analyzer is available
        if npm list --depth=0 @next/bundle-analyzer &>/dev/null; then
            npm run analyze || print_warning "Bundle analysis not available"
        fi
    fi
    
    print_success "Pre-deployment tests completed"
}

# =============================================================================
# Vercel Configuration
# =============================================================================

create_vercel_config() {
    print_header "Creating Vercel Deployment Configuration"
    
    cd "$FRONTEND_DIR"
    
    print_step "Generating vercel.json"
    cat > vercel.json << EOF
{
  "version": 2,
  "name": "$PROJECT_NAME",
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/next"
    }
  ],
  "env": {
    "NEXT_PUBLIC_APP_NAME": "VESLINT",
    "NEXT_PUBLIC_APP_VERSION": "2.0.0",
    "NEXT_PUBLIC_ENVIRONMENT": "production"
  },
  "build": {
    "env": {
      "NEXT_TELEMETRY_DISABLED": "1"
    }
  },
  "functions": {
    "pages/api/**/*.js": {
      "maxDuration": 30
    }
  },
  "regions": ["iad1"],
  "rewrites": [
    {
      "source": "/api/(.*)",
      "destination": "$NEXT_PUBLIC_API_URL/\$1"
    }
  ],
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        },
        {
          "key": "Referrer-Policy",
          "value": "strict-origin-when-cross-origin"
        },
        {
          "key": "Permissions-Policy",
          "value": "camera=(), microphone=(), geolocation=()"
        }
      ]
    },
    {
      "source": "/static/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=31536000, immutable"
        }
      ]
    }
  ],
  "redirects": [
    {
      "source": "/home",
      "destination": "/",
      "permanent": true
    }
  ]
}
EOF

    print_success "Vercel configuration created"
    
    # Create .vercelignore
    print_step "Creating .vercelignore"
    cat > .vercelignore << EOF
# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
coverage
.nyc_output

# Next.js
.next/
out/

# IDE
.vscode
.idea

# OS
.DS_Store
Thumbs.db

# Logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache
EOF

    print_success "Vercel ignore file created"
}

# =============================================================================
# Authentication and Project Setup
# =============================================================================

setup_vercel_auth() {
    print_header "Setting Up Vercel Authentication"
    
    print_step "Checking Vercel authentication"
    if vercel whoami &>/dev/null; then
        vercel_user=$(vercel whoami)
        print_success "Already authenticated as: $vercel_user"
    else
        print_step "Logging in to Vercel"
        vercel login || {
            print_error "Vercel login failed"
            exit 1
        }
        print_success "Successfully logged in to Vercel"
    fi
    
    print_step "Setting up Vercel project"
    cd "$FRONTEND_DIR"
    
    # Check if project is already linked
    if [ -f ".vercel/project.json" ]; then
        print_info "Project already linked to Vercel"
    else
        print_step "Linking project to Vercel"
        vercel link --yes || {
            print_error "Failed to link project to Vercel"
            exit 1
        }
        print_success "Project linked to Vercel"
    fi
}

# =============================================================================
# Environment Variables Setup
# =============================================================================

setup_vercel_env() {
    print_header "Setting Up Vercel Environment Variables"
    
    cd "$FRONTEND_DIR"
    
    print_step "Configuring production environment variables"
    
    # Set required environment variables
    local env_vars=(
        "NEXT_PUBLIC_SUPABASE_URL"
        "NEXT_PUBLIC_SUPABASE_ANON_KEY"
        "NEXT_PUBLIC_API_URL"
    )
    
    for var in "${env_vars[@]}"; do
        if [ -n "${!var}" ]; then
            print_step "Setting $var"
            echo "${!var}" | vercel env add "$var" production --force || {
                print_warning "Failed to set $var, may already exist"
            }
        fi
    done
    
    # Set optional environment variables
    local optional_env_vars=(
        "NEXT_PUBLIC_FIREBASE_API_KEY"
        "NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN"
        "NEXT_PUBLIC_FIREBASE_PROJECT_ID"
        "NEXT_PUBLIC_SENTRY_DSN"
        "NEXT_PUBLIC_ANALYTICS_ID"
    )
    
    for var in "${optional_env_vars[@]}"; do
        if [ -n "${!var}" ]; then
            print_step "Setting optional $var"
            echo "${!var}" | vercel env add "$var" production --force || {
                print_warning "Failed to set $var"
            }
        fi
    done
    
    print_success "Environment variables configured"
}

# =============================================================================
# Deployment
# =============================================================================

deploy_to_vercel() {
    print_header "Deploying to Vercel"
    
    cd "$FRONTEND_DIR"
    
    print_step "Starting deployment to Vercel"
    deployment_url=$(vercel --prod --yes | tail -1)
    
    if [ -n "$deployment_url" ]; then
        print_success "Deployment completed!"
        print_info "Deployment URL: $deployment_url"
        
        # Wait for deployment to be available
        if wait_for_deployment "$deployment_url" $HEALTH_CHECK_TIMEOUT; then
            test_deployment "$deployment_url"
        else
            print_error "Deployment availability check failed"
            exit 1
        fi
    else
        print_error "Deployment failed - no URL returned"
        exit 1
    fi
    
    DEPLOYMENT_URL="$deployment_url"
}

# =============================================================================
# Custom Domain Setup
# =============================================================================

setup_custom_domain() {
    print_header "Setting Up Custom Domain"
    
    if [ -z "$CUSTOM_DOMAIN" ]; then
        print_info "No custom domain specified, skipping"
        return 0
    fi
    
    cd "$FRONTEND_DIR"
    
    print_step "Adding custom domain: $CUSTOM_DOMAIN"
    vercel domains add "$CUSTOM_DOMAIN" || {
        print_warning "Failed to add custom domain, may already exist"
    }
    
    print_step "Adding www subdomain"
    vercel domains add "www.$CUSTOM_DOMAIN" || {
        print_warning "Failed to add www subdomain, may already exist"
    }
    
    print_step "DNS Configuration Required"
    echo -e "${WHITE}Please configure the following DNS records:${NC}"
    echo ""
    echo -e "${CYAN}A Record:${NC}"
    echo "  Name: @"
    echo "  Value: ***********"
    echo ""
    echo -e "${CYAN}CNAME Record:${NC}"
    echo "  Name: www"
    echo "  Value: cname.vercel-dns.com"
    echo ""
    
    read -p "Press Enter when DNS records are configured..."
    
    print_step "Verifying domain configuration"
    vercel domains verify "$CUSTOM_DOMAIN" || {
        print_warning "Domain verification failed, may need more time for DNS propagation"
    }
    
    print_success "Custom domain setup completed"
}

# =============================================================================
# Deployment Testing
# =============================================================================

test_deployment() {
    local deployment_url="$1"
    print_header "Testing Deployment"
    
    print_step "Testing homepage"
    if curl -s -f "$deployment_url" >/dev/null; then
        print_success "Homepage accessible"
    else
        print_error "Homepage not accessible"
        return 1
    fi
    
    print_step "Testing API connectivity"
    # Try to access a simple API endpoint through the frontend
    if curl -s -f "$deployment_url/api/health" >/dev/null 2>&1; then
        print_success "API connectivity working"
    else
        print_warning "API connectivity may need verification"
    fi
    
    print_step "Testing performance"
    load_time=$(curl -s -w "%{time_total}" -o /dev/null "$deployment_url")
    if (( $(echo "$load_time < 3.0" | bc -l) )); then
        print_success "Page load time: ${load_time}s (Good)"
    else
        print_warning "Page load time: ${load_time}s (Could be better)"
    fi
    
    print_step "Testing security headers"
    headers=$(curl -s -I "$deployment_url")
    if echo "$headers" | grep -q "X-Frame-Options"; then
        print_success "Security headers configured"
    else
        print_warning "Security headers may need attention"
    fi
    
    print_success "Deployment testing completed"
}

# =============================================================================
# Post-deployment Setup
# =============================================================================

post_deployment_setup() {
    print_header "Post-deployment Setup"
    
    print_step "Deployment Summary"
    echo -e "${WHITE}✅ Frontend deployed to Vercel${NC}"
    echo -e "${WHITE}✅ Environment variables configured${NC}"
    echo -e "${WHITE}✅ Performance optimizations applied${NC}"
    if [ -n "$CUSTOM_DOMAIN" ]; then
        echo -e "${WHITE}✅ Custom domain configured${NC}"
    fi
    echo ""
    
    print_step "Deployment URLs:"
    if [ -n "$DEPLOYMENT_URL" ]; then
        echo -e "${WHITE}Production: $DEPLOYMENT_URL${NC}"
    fi
    if [ -n "$CUSTOM_DOMAIN" ]; then
        echo -e "${WHITE}Custom Domain: https://$CUSTOM_DOMAIN${NC}"
        echo -e "${WHITE}WWW Domain: https://www.$CUSTOM_DOMAIN${NC}"
    fi
    echo ""
    
    print_step "Next Steps:"
    echo "1. Update backend CORS configuration to include:"
    if [ -n "$CUSTOM_DOMAIN" ]; then
        echo "   - https://$CUSTOM_DOMAIN"
        echo "   - https://www.$CUSTOM_DOMAIN"
    fi
    echo "   - $DEPLOYMENT_URL"
    echo ""
    echo "2. Set up monitoring and analytics:"
    echo "   - Configure Vercel Analytics"
    echo "   - Set up error tracking"
    echo "   - Monitor Core Web Vitals"
    echo ""
    echo "3. Configure CDN and caching:"
    echo "   - Verify static asset caching"
    echo "   - Monitor edge function performance"
    echo ""
    echo "4. Security checklist:"
    echo "   - Verify CSP headers"
    echo "   - Test authentication flow"
    echo "   - Validate API security"
    echo ""
    
    print_success "Frontend deployment completed! 🎉"
}

# =============================================================================
# Main Execution
# =============================================================================

main() {
    print_header "VESLINT Frontend Deployment to Vercel"
    
    echo -e "${BLUE}This script will deploy your VESLINT frontend to Vercel.com${NC}"
    echo -e "${BLUE}using the free tier with performance optimizations.${NC}"
    echo ""
    
    # Confirmation
    read -p "Continue with deployment? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Deployment cancelled"
        exit 0
    fi
    
    # Run deployment steps
    check_prerequisites
    setup_environment
    run_predeploy_tests
    create_vercel_config
    setup_vercel_auth
    setup_vercel_env
    deploy_to_vercel
    setup_custom_domain
    post_deployment_setup
    
    print_success "VESLINT frontend deployment completed successfully! 🚀"
}

# Handle script arguments
case "${1:-}" in
    "--help"|"-h")
        echo "VESLINT Frontend Deployment Script"
        echo "Usage: $0 [--help]"
        echo ""
        echo "This script deploys the VESLINT Next.js frontend to Vercel.com"
        echo "using the free tier with performance optimizations."
        echo ""
        echo "Required environment variables:"
        echo "  NEXT_PUBLIC_SUPABASE_URL"
        echo "  NEXT_PUBLIC_SUPABASE_ANON_KEY"
        echo "  NEXT_PUBLIC_API_URL"
        echo ""
        echo "Optional environment variables:"
        echo "  NEXT_PUBLIC_FIREBASE_API_KEY"
        echo "  NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN"
        echo "  NEXT_PUBLIC_FIREBASE_PROJECT_ID"
        echo "  NEXT_PUBLIC_SENTRY_DSN"
        echo "  NEXT_PUBLIC_ANALYTICS_ID"
        echo "  VERCEL_TOKEN"
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac