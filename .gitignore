# =============================================================================
# VESLINT Git Ignore Rules
# =============================================================================
# Comprehensive .gitignore for the VESLINT maritime intelligence platform
# Covers Node.js, Python, Next.js, FastAPI, ML models, and deployment files

# =============================================================================
# Operating System Files
# =============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# IDEs and Editors
# =============================================================================

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets
.history/
*.vsix

# JetBrains IDEs
.idea/
*.swp
*.swo
*~
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
*.sublime-project

# Vim
[._]*.s[a-v][a-z]
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]
Session.vim
Sessionx.vim
.netrwhist
tags
[._]*.un~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# Node.js / Frontend
# =============================================================================

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next/
out/

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Webpack Bundle Analyzer
frontend/webpack-bundle-analyzer-report.html

# =============================================================================
# Python / Backend
# =============================================================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff
instance/
.webassets-cache

# Scrapy stuff
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
backend/venv/
backend/.env
backend/.env.*

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# =============================================================================
# Machine Learning / ML Models
# =============================================================================

# Large model files
*.joblib
*.pkl
*.pickle
*.model
*.h5
*.hdf5
*.onnx
*.pb
*.pth
*.pt
*.bin
*.safetensors

# Model directories
models/
checkpoints/
artifacts/
experiments/

# Training outputs
logs/
runs/
wandb/
mlruns/

# Dataset files (large)
*.csv
*.json
!test-data/*.csv
!test-data/*.json
!**/sample*.csv
!**/example*.csv
*.parquet
*.feather
*.h5
*.hdf5

# Jupyter notebook checkpoints
.ipynb_checkpoints/

# MLflow
mlruns/
mlartifacts/

# Weights & Biases
wandb/

# TensorBoard
tensorboard/
tb_logs/

# HuggingFace
.huggingface/
transformers_cache/

# Specific ML model assets
ml-model/assets/*.joblib
ml-model/assets/*.pkl
ml-model/models/
ml-model/data/
ml-model/experiments/

# =============================================================================
# Database
# =============================================================================

# Database files
*.db
*.sqlite
*.sqlite3
database/*.sql
!database/migrations/*.sql
!database/seed.sql

# Database dumps
*.dump
*.sql.gz

# PostgreSQL
postgresql/

# Supabase
supabase/logs/
supabase/.temp/

# =============================================================================
# Deployment and Infrastructure
# =============================================================================

# Deployment environment files
.env.deploy
.env.production
.env.staging

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Docker
.docker/

# Kubernetes
*.kubeconfig

# Vercel
.vercel

# Render
render.yaml.bak

# AWS
.aws/

# Google Cloud
.gcloud/
*-credentials.json
service-account-*.json

# Azure
.azure/

# Netlify
.netlify/

# =============================================================================
# Logs and Monitoring
# =============================================================================

# Log files
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
pnpm-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage reports
coverage/
.nyc_output/

# Monitoring
newrelic_agent.log

# Sentry
.sentryclirc

# =============================================================================
# Temporary and Cache Files
# =============================================================================

# Temporary files
tmp/
temp/
.tmp/
.temp/

# Cache directories
.cache/
.next/cache/
.nuxt/
.vuepress/
.serverless_nextjs/

# Build artifacts
build/
dist/
out/

# =============================================================================
# Security and Secrets
# =============================================================================

# Environment files with secrets
.env.secret
.env.private
secrets.json
config/secrets.json

# SSH keys
*.pem
*.key
!*.pub

# GPG keys
*.asc

# API keys and tokens
.apikey
.token
credentials.json
service-account.json

# SSL certificates
*.crt
*.cer
*.p12
*.pfx

# =============================================================================
# Testing
# =============================================================================

# Test results
test-results/
test-reports/
junit.xml
coverage-reports/

# E2E test artifacts
cypress/videos/
cypress/screenshots/
playwright-report/
test-results/

# Performance testing
lighthouse-reports/

# =============================================================================
# Documentation
# =============================================================================

# Generated documentation
docs/_build/
docs/build/
site/

# =============================================================================
# Backup Files
# =============================================================================

# Backup files
*.bak
*.backup
*.old
*.orig
*.swp
*.swo

# =============================================================================
# Archive Files
# =============================================================================

# Compressed files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# =============================================================================
# VESLINT Specific
# =============================================================================

# Custom ignores for VESLINT project
frontend/public/uploads/
backend/uploads/
backend/temp/
test-data/large/
screenshots/
performance-reports/

# Local development overrides
docker-compose.override.yml
.env.override

# IDE specific project files
.vscode/launch.json
.idea/workspace.xml

# Custom scripts (if sensitive)
scripts/deploy-prod.sh
scripts/secrets.sh

# Local test files
test-local/
temp-tests/

# =============================================================================
# Keep These Files (Negated Patterns)
# =============================================================================

# Keep essential config files
!.gitkeep
!.eslintrc.*
!.prettierrc.*
!next.config.js
!tailwind.config.js
!tsconfig.json
!pytest.ini
!requirements.txt
!package.json
!Dockerfile
!docker-compose.yml
!render.yaml
!vercel.json

# Keep documentation
!README.md
!CHANGELOG.md
!LICENSE
!docs/**/*.md

# Keep example/template files
!**/*.example
!**/*.template
!**/example-*
!**/template-*

# Keep sample data (small files only)
!**/sample-data.json
!**/test-data.csv
!**/example.csv