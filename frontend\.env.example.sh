# =============================================================================
# VESLINT Frontend Environment Variables
# =============================================================================
# Copy this file to .env.local and fill in your actual values

# Application Configuration
NEXT_PUBLIC_APP_NAME=VESLINT
NEXT_PUBLIC_APP_VERSION=2.0.0
NEXT_PUBLIC_ENVIRONMENT=development

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_API_VERSION=v1

# Database Configuration (Supabase)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# Authentication (Firebase - Optional)
NEXT_PUBLIC_FIREBASE_API_KEY=your-firebase-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-firebase-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789
NEXT_PUBLIC_FIREBASE_APP_ID=your-firebase-app-id

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_ERROR_REPORTING=false
NEXT_PUBLIC_ENABLE_REAL_TIME=true

# Performance Configuration
NEXT_PUBLIC_MAX_FILE_SIZE=52428800
NEXT_PUBLIC_CHUNK_SIZE=1048576
NEXT_PUBLIC_REQUEST_TIMEOUT=30000

# Security Configuration
NEXT_PUBLIC_TRUSTED_DOMAINS=localhost,veslint.com
NEXT_PUBLIC_CSP_ENABLED=false

# Analytics (Optional)
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
NEXT_PUBLIC_HOTJAR_ID=your-hotjar-id

# Monitoring (Optional)
NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn

# Development Configuration
NEXT_PUBLIC_DEBUG=false
NEXT_TELEMETRY_DISABLED=1