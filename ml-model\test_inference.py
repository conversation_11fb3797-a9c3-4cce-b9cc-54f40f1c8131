#!/usr/bin/env python
"""
Test inference script for VESLINT vessel classification model
Tests the Hugging Face model endpoint with sample data
"""

import os
import json
import time
import argparse
import requests
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime

# Default Hugging Face API URL
HF_API_URL = "https://api-inference.huggingface.co/models/"
DEFAULT_MODEL = "veslint/vessel-classification-v2"

def load_sample_data(file_path: Optional[str] = None) -> pd.DataFrame:
    """
    Load sample AIS data for testing
    
    Args:
        file_path: Path to sample CSV file (optional)
        
    Returns:
        DataFrame with sample AIS data
    """
    if file_path and os.path.exists(file_path):
        print(f"Loading sample data from {file_path}")
        return pd.read_csv(file_path)
    
    # Generate synthetic sample data if no file provided
    print("Generating synthetic sample data")
    
    # Sample vessel types with different movement patterns
    vessel_types = {
        "cargo": {
            "mmsi": "123456789",
            "speed_range": (8, 15),
            "course_stability": 0.9
        },
        "fishing": {
            "mmsi": "234567890",
            "speed_range": (3, 8),
            "course_stability": 0.3
        },
        "pleasure": {
            "mmsi": "345678901",
            "speed_range": (5, 20),
            "course_stability": 0.5
        },
        "tug": {
            "mmsi": "456789012",
            "speed_range": (4, 10),
            "course_stability": 0.7
        }
    }
    
    # Generate sample data
    data = []
    now = datetime.now().timestamp()
    
    for vessel_type, params in vessel_types.items():
        mmsi = params["mmsi"]
        speed_range = params["speed_range"]
        course_stability = params["course_stability"]
        
        # Generate 50 points per vessel
        lat, lon = 40.7, -74.0  # Start near NYC
        course = 45.0
        
        for i in range(50):
            # Time is every 5 minutes
            timestamp = now - (50 - i) * 300
            dt = datetime.fromtimestamp(timestamp).isoformat()
            
            # Speed varies within range
            speed = np.random.uniform(speed_range[0], speed_range[1])
            
            # Course changes based on stability parameter
            if np.random.random() > course_stability:
                course_change = np.random.normal(0, 30)
                course = (course + course_change) % 360
            
            # Update position based on speed and course
            lat += np.sin(np.radians(course)) * speed * 5/60 / 60
            lon += np.cos(np.radians(course)) * speed * 5/60 / 60 / np.cos(np.radians(lat))
            
            # Add some noise
            lat += np.random.normal(0, 0.0005)
            lon += np.random.normal(0, 0.0005)
            
            data.append({
                "mmsi": mmsi,
                "timestamp": dt,
                "lat": lat,
                "lon": lon,
                "sog": speed,
                "cog": course,
                "heading": course + np.random.normal(0, 5),
                "true_vessel_type": vessel_type
            })
    
    return pd.DataFrame(data)

def extract_features(df: pd.DataFrame) -> Dict[str, Any]:
    """
    Extract basic features from AIS data
    
    Args:
        df: DataFrame with AIS data
        
    Returns:
        Dictionary of extracted features
    """
    features = {}
    
    # Group by MMSI
    for mmsi, group in df.groupby("mmsi"):
        # Basic statistical features
        features[mmsi] = {
            "mmsi": mmsi,
            "point_count": len(group),
            "speed_stats": {
                "mean_speed": group["sog"].mean(),
                "max_speed": group["sog"].max(),
                "min_speed": group["sog"].min(),
                "std_speed": group["sog"].std()
            },
            "course_stats": {
                "course_stability": 1.0 - (group["cog"].std() / 180.0),
                "course_changes": len(group[abs(group["cog"].diff()) > 30])
            },
            "true_vessel_type": group["true_vessel_type"].iloc[0] if "true_vessel_type" in group.columns else "unknown"
        }
    
    return features

def test_model_inference(
    api_key: str,
    model_id: str = DEFAULT_MODEL,
    sample_data: Optional[str] = None,
    verbose: bool = False
) -> None:
    """
    Test model inference with sample data
    
    Args:
        api_key: Hugging Face API key
        model_id: Hugging Face model ID
        sample_data: Path to sample data CSV (optional)
        verbose: Whether to print verbose output
    """
    # Load sample data
    df = load_sample_data(sample_data)
    
    if verbose:
        print(f"Loaded {len(df)} AIS data points for {df['mmsi'].nunique()} vessels")
        print(df.head())
    
    # Extract features
    features = extract_features(df)
    
    if verbose:
        print(f"Extracted features for {len(features)} vessels")
        print(json.dumps(list(features.values())[0], indent=2))
    
    # Prepare API request
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    api_url = f"{HF_API_URL}{model_id}"
    
    # Send request to model API
    print(f"Sending inference request to {api_url}")
    start_time = time.time()
    
    try:
        response = requests.post(
            api_url,
            headers=headers,
            json={"inputs": list(features.values())}
        )
        
        elapsed = time.time() - start_time
        
        # Check response
        if response.status_code == 200:
            results = response.json()
            print(f"✅ Inference successful! ({elapsed:.2f}s)")
            
            # Print results
            print("\nClassification Results:")
            print("======================")
            
            for i, (mmsi, vessel_data) in enumerate(features.items()):
                result = results[i]
                true_type = vessel_data.get("true_vessel_type", "unknown")
                
                print(f"\nVessel MMSI: {mmsi}")
                print(f"True Type: {true_type}")
                print(f"Predicted Type: {result['predicted_class']}")
                print(f"Confidence: {result['confidence']:.2%}")
                
                if "class_probabilities" in result:
                    print("Class Probabilities:")
                    for cls, prob in sorted(result["class_probabilities"].items(), key=lambda x: x[1], reverse=True)[:3]:
                        print(f"  - {cls}: {prob:.2%}")
                
                if true_type != "unknown" and true_type == result["predicted_class"]:
                    print("✓ Correct classification")
                elif true_type != "unknown":
                    print("✗ Incorrect classification")
            
            # Calculate accuracy if true types are available
            if "true_vessel_type" in df.columns:
                correct = sum(1 for i, (mmsi, data) in enumerate(features.items()) 
                             if data["true_vessel_type"] == results[i]["predicted_class"])
                accuracy = correct / len(features)
                print(f"\nOverall Accuracy: {accuracy:.2%} ({correct}/{len(features)})")
                
        else:
            print(f"❌ Error: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Test VESLINT model inference")
    parser.add_argument("--api-key", type=str, help="Hugging Face API key")
    parser.add_argument("--model", type=str, default=DEFAULT_MODEL, help="Hugging Face model ID")
    parser.add_argument("--data", type=str, help="Path to sample CSV data")
    parser.add_argument("--verbose", action="store_true", help="Print verbose output")
    
    args = parser.parse_args()
    
    # Get API key from args or environment
    api_key = args.api_key or os.environ.get("HF_API_TOKEN")
    
    if not api_key:
        print("Error: No API key provided. Use --api-key or set HF_API_TOKEN environment variable.")
        exit(1)
    
    test_model_inference(api_key, args.model, args.data, args.verbose)
