import { Job, JobResponse, JobsListResponse } from './job';
import { Vessel, VesselClassificationResponse } from './vessel';

export interface ApiResponse<T> {
  data?: T;
  error?: {
    message: string;
    code?: string;
    details?: any;
  };
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  has_more: boolean;
}

export interface HealthCheckResponse {
  status: 'ok' | 'error';
  version: string;
  timestamp: string;
  services: {
    database: {
      status: 'ok' | 'error';
      message?: string;
    };
    ml_service: {
      status: 'ok' | 'error';
      message?: string;
    };
  };
}

// Export all response types for convenience
export type {
  Job,
  JobResponse,
  JobsListResponse,
  Vessel,
  VesselClassificationResponse,
}; 