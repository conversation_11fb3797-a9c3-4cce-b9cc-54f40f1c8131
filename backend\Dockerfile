# Multi-stage Dockerfile for VESLINT Backend API
# Optimized for Render deployment and free tier usage

# Build stage - for installing dependencies and building
FROM python:3.11-slim as builder

# Set build arguments
ARG BUILDPLATFORM
ARG TARGETPLATFORM

# Set environment variables for build
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies needed for building
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    gcc \
    g++ \
    libc6-dev \
    libffi-dev \
    libssl-dev \
    pkg-config \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Upgrade pip and install build tools
RUN pip install --upgrade pip setuptools wheel

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Production stage - minimal runtime image
FROM python:3.11-slim as production

# Set metadata
LABEL maintainer="VESLINT Team"
LABEL description="VESLINT Maritime Vessel Classification API"
LABEL version="1.0.0"

# Set runtime environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH="/app/src" \
    PORT=8000 \
    ENVIRONMENT=production \
    LOG_LEVEL=info

# Install minimal runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user for security
RUN groupadd -r veslint && useradd -r -g veslint -d /app -s /bin/bash veslint

# Create application directory
WORKDIR /app

# Copy virtual environment from builder stage
COPY --from=builder /opt/venv /opt/venv

# Add virtual environment to PATH
ENV PATH="/opt/venv/bin:$PATH"

# Copy application code
COPY src/ ./src/

# Copy models directory (create if doesn't exist)
COPY models/ ./models/

# Create necessary directories
RUN mkdir -p /app/logs /app/tmp \
    && chown -R veslint:veslint /app

# Copy startup script
COPY docker-entrypoint.sh ./
RUN chmod +x docker-entrypoint.sh

# Switch to non-root user
USER veslint

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:${PORT}/health || exit 1

# Expose port
EXPOSE $PORT

# Set entrypoint and default command
ENTRYPOINT ["./docker-entrypoint.sh"]
CMD ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000"]

# Development stage - includes development tools
FROM production as development

# Switch back to root to install dev dependencies
USER root

# Install development tools
RUN apt-get update && apt-get install -y --no-install-recommends \
    git \
    vim \
    htop \
    net-tools \
    && rm -rf /var/lib/apt/lists/*

# Install development Python packages
RUN /opt/venv/bin/pip install --no-cache-dir \
    pytest \
    pytest-asyncio \
    pytest-cov \
    black \
    flake8 \
    mypy \
    httpx \
    pytest-xdist

# Override environment for development
ENV ENVIRONMENT=development \
    LOG_LEVEL=debug \
    RELOAD=true

# Switch back to app user
USER veslint

# Development command with auto-reload
CMD ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--reload-dir", "src"]

# =============================================================================
# Build Instructions:
# =============================================================================
#
# Production build:
# docker build --target production -t veslint-api:latest .
#
# Development build:
# docker build --target development -t veslint-api:dev .
#
# Multi-platform build for Render:
# docker buildx build --platform linux/amd64 --target production -t veslint-api:latest .
#
# =============================================================================
# Environment Variables for Render:
# =============================================================================
#
# Required:
# - SUPABASE_URL: Your Supabase project URL
# - SUPABASE_ANON_KEY: Your Supabase anon key
# - FIREBASE_PROJECT_ID: Your Firebase project ID
#
# Optional:
# - LOG_LEVEL: debug, info, warning, error (default: info)
# - ENVIRONMENT: development, staging, production (default: production)
# - ML_MODEL_PATH: Path to the ML model file (default: ./models/extreme_maritime_classifier.joblib)
# - MAX_FILE_SIZE_MB: Maximum upload file size in MB (default: 50)
# - CORS_ORIGINS: Comma-separated list of allowed origins
#
# =============================================================================
# Render Deployment Notes:
# =============================================================================
#
# 1. Render will automatically detect this Dockerfile
# 2. Set Build Command: docker build --target production -t veslint-api .
# 3. Set Start Command: (leave empty, uses CMD from Dockerfile)
# 4. Environment: Python 3.11
# 5. Instance Type: Free tier (512 MB RAM, 0.5 CPU)
# 6. Auto-Deploy: Enable for main branch
#
# =============================================================================
# Resource Optimization for Free Tier:
# =============================================================================
#
# - Multi-stage build reduces final image size
# - Minimal runtime dependencies
# - Efficient Python dependencies installation
# - No unnecessary packages in production
# - Virtual environment for isolation
# - Non-root user for security
# - Health checks for reliability
#
# =============================================================================
