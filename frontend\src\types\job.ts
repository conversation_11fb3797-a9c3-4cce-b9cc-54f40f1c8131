export type JobStatus = 'pending' | 'processing' | 'completed' | 'failed';

export interface Job {
  id: string;
  user_id: string;
  status: JobStatus;
  created_at: string;
  updated_at: string;
  name: string;
  description?: string;
  result_url?: string;
  error_message?: string;
  metadata?: Record<string, any>;
}

export interface JobCreatePayload {
  name: string;
  description?: string;
  file_url?: string;
  metadata?: Record<string, any>;
}

export interface JobsListResponse {
  jobs: Job[];
}

export interface JobResponse {
  job: Job;
} 