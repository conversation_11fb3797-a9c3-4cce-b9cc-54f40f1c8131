# VESLINT Setup Guide

This comprehensive guide will help you set up VESLINT for development, testing, and production deployment. Follow the steps appropriate for your use case.

## 📋 Table of Contents

- [Prerequisites](#prerequisites)
- [Quick Start](#quick-start)
- [Manual Setup](#manual-setup)
- [Docker Setup](#docker-setup)
- [Environment Configuration](#environment-configuration)
- [Database Setup](#database-setup)
- [ML Model Setup](#ml-model-setup)
- [Development Workflow](#development-workflow)
- [Production Deployment](#production-deployment)
- [Troubleshooting](#troubleshooting)

## 🔧 Prerequisites

### System Requirements

- **Operating System**: Linux, macOS, or Windows 10+
- **Memory**: 4GB RAM minimum, 8GB recommended
- **Storage**: 2GB free space minimum
- **Network**: Internet connection for dependencies and services

### Required Software

| Software | Minimum Version | Installation |
|----------|----------------|--------------|
| **Node.js** | 16.0.0+ | [Download](https://nodejs.org/) or use [nvm](https://github.com/nvm-sh/nvm) |
| **npm** | 8.0.0+ | Comes with Node.js |
| **Python** | 3.8.0+ | [Download](https://python.org/downloads/) or use [pyenv](https://github.com/pyenv/pyenv) |
| **Git** | 2.0.0+ | [Download](https://git-scm.com/downloads) |

### Optional Software

| Software | Purpose | Installation |
|----------|---------|--------------|
| **Docker** | Containerized development | [Download](https://docs.docker.com/get-docker/) |
| **VS Code** | Recommended IDE | [Download](https://code.visualstudio.com/) |
| **Supabase CLI** | Database management | `npm install -g supabase` |

### Version Check Commands

```bash
# Check all required versions
node --version    # Should be 16.0.0+
npm --version     # Should be 8.0.0+
python --version  # Should be 3.8.0+
git --version     # Should be 2.0.0+

# Optional checks
docker --version  # Optional
code --version    # Optional
```

## 🚀 Quick Start

### Automated Setup (Recommended)

The fastest way to get VESLINT running locally:

```bash
# 1. Clone the repository
git clone https://github.com/veslint/veslint.git
cd veslint

# 2. Run automated setup script
./scripts/setup_development.sh

# 3. Start development servers
npm run dev
```

The setup script will:
- ✅ Check system prerequisites
- ✅ Set up Python virtual environment
- ✅ Install all dependencies
- ✅ Configure local database
- ✅ Set up environment files
- ✅ Run health checks

### Quick Verification

After setup, verify everything is working:

```bash
# Check services are running
curl http://localhost:8000/health  # Backend health
curl http://localhost:3000         # Frontend

# Run system tests
npm run test:system
```

## 🛠️ Manual Setup

If you prefer manual setup or the automated script fails, follow these detailed steps:

### 1. Repository Setup

```bash
# Clone the repository
git clone https://github.com/veslint/veslint.git
cd veslint

# Check out the main branch
git checkout main
git pull origin main
```

### 2. Python Backend Setup

```bash
# Navigate to backend directory
cd backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Linux/macOS:
source venv/bin/activate
# On Windows:
# venv\Scripts\activate

# Upgrade pip
pip install --upgrade pip

# Install dependencies
pip install -r requirements.txt

# Verify installation
python -c "import fastapi, pandas, sklearn; print('✅ Backend dependencies installed')"

cd ..
```

### 3. Node.js Frontend Setup

```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Verify installation
npm run type-check

cd ..
```

### 4. Root Workspace Setup

```bash
# Install workspace dependencies
npm install

# Verify workspace setup
npm run lint
```

## 🐳 Docker Setup

For a containerized development environment:

### Prerequisites for Docker

- Docker 20.0.0+
- Docker Compose 2.0.0+

### Docker Development

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Rebuild images
docker-compose build --no-cache
```

### Docker Services

The Docker setup includes:

| Service | Port | Purpose |
|---------|------|---------|
| `veslint-frontend` | 3000 | Next.js frontend |
| `veslint-backend` | 8000 | FastAPI backend |
| `veslint-db` | 5432 | PostgreSQL database |
| `supabase-rest` | 3001 | Supabase REST API |
| `supabase-auth` | 9999 | Supabase Auth |
| `supabase-storage` | 5000 | Supabase Storage |
| `redis` | 6379 | Caching (optional) |

### Docker Troubleshooting

```bash
# Check service status
docker-compose ps

# View service logs
docker-compose logs [service-name]

# Restart specific service
docker-compose restart [service-name]

# Clean up everything
docker-compose down -v
docker system prune -a
```

## ⚙️ Environment Configuration

### Required Environment Variables

Create these environment files:

#### Backend Environment (`backend/.env`)

```bash
# Database Configuration
SUPABASE_URL=http://localhost:3001
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Application Configuration
ENVIRONMENT=development
LOG_LEVEL=DEBUG
API_VERSION=v1
CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# Performance Configuration
MAX_WORKERS=4
WORKER_TIMEOUT=120

# Optional: Firebase Auth
FIREBASE_PROJECT_ID=your_firebase_project_id

# Optional: HuggingFace Model
HUGGINGFACE_API_TOKEN=your_huggingface_token
```

#### Frontend Environment (`frontend/.env.local`)

```bash
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_API_VERSION=v1

# Database Configuration
NEXT_PUBLIC_SUPABASE_URL=http://localhost:3001
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Application Configuration
NEXT_PUBLIC_APP_NAME=VESLINT
NEXT_PUBLIC_ENVIRONMENT=development

# Optional: Firebase Auth
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_firebase_project_id
```

### Getting Supabase Credentials

1. **Local Development**: Use the automated setup script or follow Docker setup
2. **Cloud Supabase**:
   ```bash
   # Sign up at https://supabase.com
   # Create a new project
   # Get credentials from Settings > API
   ```

### Getting Firebase Credentials (Optional)

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Create a new project or select existing
3. Go to Project Settings > General
4. Copy the configuration values

## 🗄️ Database Setup

### Local Database (Supabase)

#### Option 1: Automated (Recommended)

```bash
# Install Supabase CLI
npm install -g supabase

# Initialize and start local Supabase
supabase init
supabase start

# Get connection details
supabase status
```

#### Option 2: Docker

If using Docker setup, the database is automatically configured.

#### Option 3: Manual PostgreSQL

```bash
# Install PostgreSQL 14+
# Create database
createdb veslint_dev

# Run migrations
cd database
psql -h localhost -p 5432 -U postgres -d veslint_dev -f migrations/001_initial_schema.sql
psql -h localhost -p 5432 -U postgres -d veslint_dev -f migrations/002_enable_realtime.sql
psql -h localhost -p 5432 -U postgres -d veslint_dev -f migrations/003_storage_buckets.sql
psql -h localhost -p 5432 -U postgres -d veslint_dev -f migrations/004_rls_policies.sql

# Seed with sample data
psql -h localhost -p 5432 -U postgres -d veslint_dev -f seed.sql
```

### Cloud Database (Production)

For production, use [Supabase Cloud](https://supabase.com):

1. Create a new project
2. Note the connection details
3. Run migrations through Supabase Dashboard or CLI
4. Update environment variables

## 🤖 ML Model Setup

### Model Files

The ML model requires trained model files. You have three options:

#### Option 1: Download Pre-trained Model

```bash
cd ml-model
# Download from HuggingFace (if available)
python -c "
from huggingface_hub import hf_hub_download
model_path = hf_hub_download('veslint/veslint-maritime-classifier', 'model.joblib')
print(f'Model downloaded to: {model_path}')
"
```

#### Option 2: Use Local Model

If you have a trained model file:

```bash
# Place your model file in the correct location
cp your_model.joblib backend/assets/extreme_maritime_classifier.joblib
```

#### Option 3: Train New Model

```bash
cd ml-model
# Install ML dependencies
pip install -r requirements.txt

# Run training script (if available)
python train_model.py

# Test the model
python test_inference.py
```

### Model Verification

```bash
# Test model loading
cd backend
python -c "
import joblib
model = joblib.load('assets/extreme_maritime_classifier.joblib')
print('✅ Model loaded successfully')
print(f'Model type: {type(model)}')
"
```

## 🔄 Development Workflow

### Starting Development

```bash
# Start all services
npm run dev

# Or start services individually
npm run dev:frontend  # Start frontend only
npm run dev:backend   # Start backend only
```

### Development URLs

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **Database Studio**: http://localhost:54323 (if using Supabase)

### Common Development Commands

```bash
# Code quality
npm run lint          # Lint all code
npm run lint:fix      # Fix linting issues
npm run type-check    # TypeScript checking

# Testing
npm run test          # Run all tests
npm run test:frontend # Frontend tests only
npm run test:backend  # Backend tests only
npm run test:system   # System integration tests

# Database
npm run db:migrate    # Run migrations
npm run db:seed       # Seed with sample data
npm run db:studio     # Open database studio

# Utilities
npm run clean         # Clean build artifacts
npm run health-check  # Check service health
```

### Hot Reloading

Both frontend and backend support hot reloading:

- **Frontend**: Automatic reload on file changes
- **Backend**: Automatic reload with `--reload` flag
- **Database**: Manual migration required for schema changes

## 🚀 Production Deployment

### Prerequisites for Deployment

- **GitHub Account**: For code repository
- **Vercel Account**: For frontend deployment
- **Render Account**: For backend deployment
- **Supabase Account**: For production database

### Deployment Steps

#### 1. Prepare Repository

```bash
# Ensure code is committed and pushed
git add .
git commit -m "Prepare for deployment"
git push origin main
```

#### 2. Deploy Backend

```bash
# Set required environment variables
export SUPABASE_URL=your_production_supabase_url
export SUPABASE_ANON_KEY=your_production_anon_key
export SUPABASE_SERVICE_ROLE_KEY=your_production_service_role_key

# Run deployment script
./scripts/deploy_backend.sh
```

#### 3. Deploy Frontend

```bash
# Set required environment variables
export NEXT_PUBLIC_SUPABASE_URL=your_production_supabase_url
export NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_anon_key
export NEXT_PUBLIC_API_URL=your_backend_url

# Run deployment script
./scripts/deploy_frontend.sh
```

#### 4. Configure Custom Domain

```bash
# DNS Configuration for veslint.com
A     @     ***********
CNAME www   cname.vercel-dns.com
CNAME api   your-backend.onrender.com
```

### Post-Deployment

1. **Verify Deployment**: Run system tests against production
2. **Monitor Logs**: Check service logs for errors
3. **Performance Testing**: Validate response times
4. **Security Check**: Verify HTTPS and security headers

## 🐛 Troubleshooting

### Common Issues and Solutions

#### Installation Issues

**Problem**: `npm install` fails with permission errors
```bash
# Solution: Fix npm permissions
npm config set prefix ~/.npm-global
export PATH=~/.npm-global/bin:$PATH
```

**Problem**: Python virtual environment activation fails
```bash
# Solution: Recreate virtual environment
rm -rf backend/venv
cd backend
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate on Windows
pip install -r requirements.txt
```

#### Runtime Issues

**Problem**: Backend fails to start with database connection error
```bash
# Solution: Check database is running
supabase status
# If not running:
supabase start
```

**Problem**: Frontend can't connect to backend
```bash
# Solution: Check backend is running and CORS is configured
curl http://localhost:8000/health
# Check CORS_ORIGINS in backend/.env
```

**Problem**: ML model loading fails
```bash
# Solution: Verify model file exists and is accessible
ls -la backend/assets/extreme_maritime_classifier.joblib
# If missing, download or copy model file
```

#### Docker Issues

**Problem**: Docker containers fail to start
```bash
# Solution: Check Docker daemon and clean up
docker system info
docker system prune -a
docker-compose down -v
docker-compose up -d --force-recreate
```

**Problem**: Port conflicts
```bash
# Solution: Check what's using the ports
lsof -i :3000  # Frontend port
lsof -i :8000  # Backend port
lsof -i :5432  # Database port
# Kill processes or change ports in docker-compose.yml
```

### Getting Help

If you encounter issues not covered here:

1. **Check Logs**: Look at service logs for error messages
2. **Search Issues**: Check [GitHub Issues](https://github.com/veslint/veslint/issues)
3. **Ask Questions**: Use [GitHub Discussions](https://github.com/veslint/veslint/discussions)
4. **Contact Support**: Email <EMAIL>

### Debug Mode

Enable debug mode for more detailed logging:

```bash
# Backend debug mode
export LOG_LEVEL=DEBUG

# Frontend debug mode
export NODE_ENV=development
export NEXT_PUBLIC_DEBUG=true
```

### Health Checks

Verify system health:

```bash
# Quick health check
npm run health-check

# Comprehensive system test
npm run test:system

# Manual checks
curl http://localhost:8000/health
curl http://localhost:3000
```

## 📝 Next Steps

After successful setup:

1. **Explore the Application**: Upload sample AIS data and test classification
2. **Read Documentation**: Check out [API.md](./API.md) and [ARCHITECTURE.md](./ARCHITECTURE.md)
3. **Contribute**: See our [Contributing Guidelines](../CONTRIBUTING.md)
4. **Deploy**: Follow the [Deployment Guide](./DEPLOYMENT.md) for production

## 🎯 Success Checklist

- [ ] All prerequisites installed and verified
- [ ] Repository cloned and dependencies installed
- [ ] Environment variables configured
- [ ] Database running and migrations applied
- [ ] ML model loaded successfully
- [ ] Frontend accessible at http://localhost:3000
- [ ] Backend API accessible at http://localhost:8000
- [ ] API documentation available at http://localhost:8000/docs
- [ ] Health checks passing
- [ ] System tests passing

Congratulations! You now have VESLINT running locally. Happy coding! 🎉