{"name": "veslint-frontend", "version": "2.0.0", "private": true, "description": "VESLINT Frontend - Maritime Vessel Classification Platform", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "export": "next export", "analyze": "cross-env ANALYZE=true next build", "clean": "rm -rf .next out node_modules/.cache"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.20", "@mui/x-data-grid": "^6.18.2", "@supabase/supabase-js": "^2.38.4", "@vercel/analytics": "^1.1.1", "firebase": "^10.7.1", "framer-motion": "^10.16.16", "next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "recharts": "^2.8.0", "swr": "^2.2.4", "tailwindcss": "^3.3.6", "uuid": "^9.0.1", "zustand": "^4.4.7"}, "devDependencies": {"@next/bundle-analyzer": "^14.0.4", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@types/node": "^20.10.4", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "eslint": "^8.54.0", "eslint-config-next": "^14.0.4", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.32", "prettier": "^3.1.0", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "keywords": ["maritime", "vessel-classification", "nextjs", "react", "typescript", "supabase", "firebase", "machine-learning"], "author": "VESLINT Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/veslint/veslint.git", "directory": "frontend"}}