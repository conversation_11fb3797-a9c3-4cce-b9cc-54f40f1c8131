export interface Vessel {
  id: string;
  mmsi: string;
  name?: string;
  vessel_type?: string;
  vessel_type_code?: number;
  length?: number;
  width?: number;
  flag?: string;
  classification_confidence?: number;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface VesselClassification {
  vessel_id: string;
  vessel_type: string;
  confidence: number;
  features_used: string[];
  model_version: string;
  created_at: string;
}

export interface VesselClassificationRequest {
  mmsi: string;
  ais_data: Record<string, any>;
  include_features?: boolean;
}

export interface VesselClassificationResponse {
  classification: VesselClassification;
  vessel: Vessel;
  features?: Record<string, any>;
} 