{"name": "veslint-workspace", "version": "2.0.0", "description": "VESLINT - AI-Powered Maritime Intelligence Platform", "private": true, "workspaces": ["frontend", "backend", "ml-model"], "repository": {"type": "git", "url": "https://github.com/veslint/veslint.git"}, "keywords": ["maritime", "vessel-classification", "ais-data", "machine-learning", "nextjs", "<PERSON><PERSON><PERSON>", "supabase", "typescript", "python"], "author": "VESLINT Team", "license": "MIT", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0", "python": ">=3.8.0"}, "scripts": {"dev": "run-p dev:*", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && python -m uvicorn src.main:app --reload --host 0.0.0.0 --port 8000", "build": "run-s build:*", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && python -m pip install -r requirements.txt", "start": "run-p start:*", "start:frontend": "cd frontend && npm start", "start:backend": "cd backend && python -m uvicorn src.main:app --host 0.0.0.0 --port 8000", "test": "run-s test:*", "test:frontend": "cd frontend && npm test -- --watchAll=false", "test:backend": "cd backend && python -m pytest tests/ -v", "test:system": "./scripts/test_system.sh", "lint": "run-s lint:*", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && python -m flake8 src/ && python -m black --check src/", "lint:fix": "run-s lint:fix:*", "lint:fix:frontend": "cd frontend && npm run lint:fix", "lint:fix:backend": "cd backend && python -m black src/", "type-check": "run-s type-check:*", "type-check:frontend": "cd frontend && npm run type-check", "type-check:backend": "cd backend && python -m mypy src/", "clean": "run-s clean:*", "clean:frontend": "cd frontend && rm -rf .next out node_modules/.cache", "clean:backend": "cd backend && find . -type d -name __pycache__ -delete && find . -name '*.pyc' -delete", "clean:deps": "rm -rf node_modules frontend/node_modules backend/venv", "setup": "./scripts/setup_development.sh", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:build": "docker-compose build --no-cache", "docker:logs": "docker-compose logs -f", "deploy:backend": "./scripts/deploy_backend.sh", "deploy:frontend": "./scripts/deploy_frontend.sh", "deploy": "run-s deploy:*", "db:migrate": "cd database && supabase db reset", "db:seed": "cd database && psql -h localhost -p 5432 -U postgres -d postgres -f seed.sql", "db:studio": "supabase start && open http://localhost:54323", "model:upload": "cd ml-model && python model_upload.py", "model:test": "cd ml-model && python test_inference.py", "prepare": "husky install", "docs:serve": "cd docs && python -m http.server 8080", "health-check": "curl -f http://localhost:8000/health && curl -f http://localhost:3000"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "husky": "^8.0.3", "lint-staged": "^15.2.0", "npm-run-all": "^4.1.5", "concurrently": "^8.2.2"}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"frontend/**/*.{js,jsx,ts,tsx}": ["cd frontend && npm run lint:fix", "cd frontend && npm run type-check"], "backend/**/*.py": ["cd backend && python -m black", "cd backend && python -m flake8"], "*.md": ["prettier --write"], "*.{json,yml,yaml}": ["prettier --write"]}, "commitlint": {"extends": ["@commitlint/config-conventional"], "rules": {"type-enum": [2, "always", ["feat", "fix", "docs", "style", "refactor", "perf", "test", "build", "ci", "chore", "revert"]], "scope-enum": [2, "always", ["frontend", "backend", "ml-model", "database", "scripts", "docs", "config", "deps"]]}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "volta": {"node": "18.18.0", "npm": "9.8.1"}, "config": {"backend_port": 8000, "frontend_port": 3000, "db_port": 5432}, "dependencies": {"cross-env": "^7.0.3"}, "overrides": {"semver": "^7.5.4"}, "pnpm": {"overrides": {"semver": "^7.5.4"}}, "resolutions": {"semver": "^7.5.4"}}