# VESLINT - AI-Powered Maritime Intelligence Platform

<div align="center">

![VESLINT Logo](https://via.placeholder.com/200x80/0066CC/FFFFFF?text=VESLINT)

**Advanced Maritime Vessel Classification using AI and AIS Data**

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Frontend](https://img.shields.io/badge/Frontend-Next.js-black)](https://nextjs.org/)
[![Backend](https://img.shields.io/badge/Backend-FastAPI-009688)](https://fastapi.tiangolo.com/)
[![Database](https://img.shields.io/badge/Database-Supabase-3ECF8E)](https://supabase.com/)
[![ML](https://img.shields.io/badge/ML-XGBoost-FF6600)](https://xgboost.readthedocs.io/)
[![Deploy](https://img.shields.io/badge/Deploy-Vercel%20%2B%20Render-000000)](https://vercel.com/)

[🌐 Live Demo](https://veslint.com) • [📖 Documentation](./docs/README.md) • [🚀 Quick Start](#quick-start) • [🤝 Contributing](#contributing)

</div>

---

## 🎯 Overview

**VESLINT** is a cutting-edge maritime intelligence platform that leverages artificial intelligence to classify vessel types from AIS (Automatic Identification System) data. Our advanced machine learning pipeline analyzes vessel movement patterns, behavioral characteristics, and operational data to provide accurate, real-time vessel classification.

### 🌟 Key Features

- **🤖 AI-Powered Classification**: Advanced XGBoost model with 99 engineered features
- **📊 Real-time Processing**: Live AIS data analysis and classification
- **🎯 High Accuracy**: 92%+ classification accuracy across 12 vessel types
- **💨 Fast Processing**: Process 1000+ vessels per minute
- **🌐 Web-based Platform**: Intuitive React/Next.js interface
- **🔒 Enterprise Security**: JWT authentication, RLS policies, HTTPS encryption
- **☁️ Cloud-native**: Deployed on Vercel and Render with auto-scaling
- **📱 Mobile Responsive**: Works seamlessly on all devices

## 🏗️ Architecture

VESLINT uses a modern, scalable architecture designed for performance and reliability:

```mermaid
graph TB
    A[🌐 Frontend - Next.js] --> B[🔗 API Gateway]
    B --> C[⚡ Backend - FastAPI]
    C --> D[🗄️ Database - Supabase]
    C --> E[🤖 ML Model - XGBoost]
    C --> F[📁 File Storage]
    
    subgraph "🚀 Deployment"
        G[Vercel - Frontend]
        H[Render - Backend]
        I[Supabase - Database]
    end
    
    subgraph "🔧 Features"
        J[Real-time Updates]
        K[Authentication]
        L[File Processing]
        M[Analytics]
    end
```

### 🛠️ Technology Stack

| Component | Technology | Purpose |
|-----------|------------|---------|
| **Frontend** | Next.js 13+, TypeScript, Tailwind CSS | Modern, responsive web interface |
| **Backend** | FastAPI, Python 3.11+, Pydantic | High-performance API with auto-docs |
| **Database** | Supabase (PostgreSQL), Real-time subscriptions | Scalable database with live updates |
| **ML Model** | XGBoost, scikit-learn, 99 features | Advanced vessel classification |
| **Authentication** | Firebase Auth, JWT tokens | Secure user management |
| **Storage** | Supabase Storage, File upload handling | CSV file processing and storage |
| **Deployment** | Vercel (Frontend), Render (Backend) | Serverless, auto-scaling deployment |
| **Monitoring** | Health checks, Logging, Error tracking | Production monitoring and debugging |

## 🚀 Quick Start

### Prerequisites

- **Node.js** 16+ and npm 8+
- **Python** 3.8+ and pip
- **Git** for version control
- **Docker** (optional, for local development)

### 🏃‍♂️ One-Command Setup

```bash
# Clone the repository
git clone https://github.com/veslint/veslint.git
cd veslint

# Run automated setup
./scripts/setup_development.sh

# Start development servers
npm run dev
```

### 🐳 Docker Development (Alternative)

```bash
# Start all services with Docker
docker-compose up -d

# View logs
docker-compose logs -f
```

### 🌐 Access the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Database Studio**: http://localhost:54323

## 📋 Usage

### 1️⃣ Upload AIS Data

Upload your AIS data in CSV format with the following required columns:
- `mmsi` - Maritime Mobile Service Identity
- `timestamp` - ISO 8601 timestamp
- `lat` - Latitude (-90 to 90)
- `lon` - Longitude (-180 to 180)
- `sog` - Speed over ground (knots)
- `cog` - Course over ground (degrees)

### 2️⃣ Process and Classify

Our AI model automatically:
- Extracts 99 behavioral and movement features
- Applies advanced ML algorithms
- Classifies vessels into 12 categories:
  - 🚢 Cargo
  - ⛽ Tanker
  - 🚢 Passenger
  - 🎣 Fishing
  - ⛵ Pleasure Craft
  - 🚤 High Speed Craft
  - 🚤 Tug/Tow
  - 🚢 Military
  - 🚁 Pilot
  - 🆘 Search & Rescue
  - ❓ Other
  - ❔ Unknown

### 3️⃣ View Results

Get comprehensive results including:
- **Classification Results**: Vessel type with confidence scores
- **Interactive Maps**: Vessel tracks and movement patterns
- **Analytics Dashboard**: Summary statistics and insights
- **Export Options**: Download results in multiple formats

## 🧠 Machine Learning Model

### Model Architecture

Our vessel classification system uses an **XGBoost** ensemble model trained on extensive AIS datasets:

- **Features**: 99 engineered features across 4 categories
- **Algorithm**: Extreme Gradient Boosting
- **Training Data**: 1M+ vessel tracks from global AIS data
- **Accuracy**: 92%+ across all vessel types
- **Processing**: Real-time inference < 50ms per vessel

### Feature Engineering

The model analyzes four key feature categories:

1. **📊 Statistical Features** (25)
   - Mean, std, min, max for position, speed, course, heading

2. **⏰ Temporal Features** (8)
   - Time patterns, activity ratios, data consistency

3. **🏃‍♂️ Movement Features** (15)
   - Speed profiles, course changes, stops, acceleration

4. **🎯 Behavioral Features** (51)
   - Port activity, operational patterns, route efficiency

## 🏗️ Project Structure

```
VESLINT/
├── 📂 backend/              # FastAPI backend service
│   ├── 📂 src/
│   │   ├── 📄 main.py       # FastAPI application
│   │   ├── 📂 api/          # API routes
│   │   ├── 📂 models/       # Data models
│   │   ├── 📂 services/     # Business logic
│   │   ├── 📂 database/     # Database layer
│   │   └── 📂 utils/        # Utilities
│   ├── 📄 requirements.txt  # Python dependencies
│   ├── 📄 Dockerfile        # Container configuration
│   └── 📄 render.yaml       # Render deployment config
├── 📂 frontend/             # Next.js frontend application
│   ├── 📂 src/
│   │   ├── 📂 app/          # Next.js 13+ app directory
│   │   ├── 📂 components/   # React components
│   │   ├── 📂 hooks/        # Custom hooks
│   │   ├── 📂 lib/          # Utilities
│   │   └── 📂 types/        # TypeScript types
│   ├── 📄 package.json      # Node.js dependencies
│   ├── 📄 next.config.js    # Next.js configuration
│   └── 📄 vercel.json       # Vercel deployment config
├── 📂 database/             # Database schema and migrations
│   ├── 📂 migrations/       # SQL migration files
│   └── 📄 seed.sql          # Sample data
├── 📂 ml-model/             # Machine learning components
│   ├── 📄 model_upload.py   # Model deployment script
│   ├── 📄 test_inference.py # Model testing
│   └── 📄 feature_schema.json # Feature documentation
├── 📂 scripts/              # Development and deployment scripts
│   ├── 📄 setup_development.sh    # Environment setup
│   ├── 📄 deploy_backend.sh       # Backend deployment
│   ├── 📄 deploy_frontend.sh      # Frontend deployment
│   └── 📄 test_system.sh          # System testing
├── 📂 docs/                 # Documentation
│   ├── 📄 SETUP.md          # Setup instructions
│   ├── 📄 API.md            # API documentation
│   ├── 📄 DEPLOYMENT.md     # Deployment guide
│   └── 📄 ARCHITECTURE.md   # Architecture overview
├── 📄 docker-compose.yml    # Local development environment
├── 📄 package.json          # Workspace configuration
└── 📄 README.md             # This file
```

## 📚 Documentation

Comprehensive documentation is available in the `/docs` directory:

- **[📋 Setup Guide](./docs/SETUP.md)** - Detailed setup instructions
- **[🔌 API Documentation](./docs/API.md)** - Complete API reference
- **[🚀 Deployment Guide](./docs/DEPLOYMENT.md)** - Production deployment
- **[🏗️ Architecture Overview](./docs/ARCHITECTURE.md)** - System architecture
- **[🧪 Testing Guide](./docs/TESTING.md)** - Testing procedures

## 🛠️ Development

### Available Scripts

```bash
# Development
npm run dev              # Start development servers
npm run build            # Build for production
npm run test             # Run all tests
npm run lint             # Lint code
npm run type-check       # TypeScript type checking

# Docker
npm run docker:up        # Start Docker development environment
npm run docker:down      # Stop Docker environment
npm run docker:build     # Rebuild Docker images

# Database
npm run db:migrate       # Run database migrations
npm run db:seed          # Seed with sample data
npm run db:studio        # Open database studio

# ML Model
npm run model:upload     # Upload model to HuggingFace
npm run model:test       # Test model inference

# Deployment
npm run deploy:backend   # Deploy backend to Render
npm run deploy:frontend  # Deploy frontend to Vercel
npm run deploy           # Deploy both services

# System
npm run test:system      # Run system tests
npm run health-check     # Check service health
```

### 🔧 Environment Variables

Create the following environment files:

**Backend** (`.env`):
```bash
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
FIREBASE_PROJECT_ID=your_firebase_project_id
HUGGINGFACE_API_TOKEN=your_hf_token
```

**Frontend** (`.env.local`):
```bash
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_firebase_project_id
```

## 🚀 Deployment

### Production Deployment

VESLINT is designed for easy deployment on modern cloud platforms:

1. **Frontend** → [Vercel](https://vercel.com/) (Free tier)
2. **Backend** → [Render](https://render.com/) (Free tier)
3. **Database** → [Supabase](https://supabase.com/) (Free tier)

### Quick Deploy

```bash
# Deploy backend
./scripts/deploy_backend.sh

# Deploy frontend
./scripts/deploy_frontend.sh

# Or deploy both
npm run deploy
```

### Custom Domain Setup

1. Configure DNS records:
   ```
   A     @           ***********
   CNAME www         cname.vercel-dns.com
   CNAME api         your-backend.onrender.com
   ```

2. Update CORS settings in backend deployment
3. Configure SSL certificates (automatic with Vercel/Render)

## 🧪 Testing

### Test Suites

- **Unit Tests**: Component and function testing
- **Integration Tests**: API and database testing
- **E2E Tests**: Complete user workflow testing
- **Performance Tests**: Load and stress testing
- **Security Tests**: Vulnerability scanning

### Run Tests

```bash
# All tests
npm run test

# Frontend tests
cd frontend && npm test

# Backend tests
cd backend && python -m pytest

# System tests
./scripts/test_system.sh
```

## 🔒 Security

VESLINT implements enterprise-grade security:

- **🔐 Authentication**: Firebase Auth with JWT tokens
- **🛡️ Authorization**: Row-level security (RLS) policies
- **🔒 HTTPS**: End-to-end encryption
- **🚫 Input Validation**: Comprehensive data validation
- **🔍 SQL Injection Protection**: Parameterized queries
- **🛡️ XSS Protection**: Content Security Policy headers
- **📝 Audit Logging**: Complete action tracking

## 📊 Performance

### Benchmarks

- **API Response Time**: < 100ms average
- **File Processing**: 1000+ vessels/minute
- **ML Inference**: < 50ms per vessel
- **Database Queries**: < 10ms average
- **Frontend Load Time**: < 2s first load, < 500ms subsequent

### Optimization Features

- **⚡ Caching**: Redis caching layer
- **🗜️ Compression**: Gzip/Brotli compression
- **📦 Code Splitting**: Lazy loading components
- **🖼️ Image Optimization**: Next.js image optimization
- **📊 Database Indexing**: Optimized query performance

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](./CONTRIBUTING.md) for details.

### Development Workflow

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

### Code Standards

- **Frontend**: ESLint + Prettier with TypeScript
- **Backend**: Black + Flake8 + MyPy for Python
- **Commits**: Conventional Commits format
- **Documentation**: JSDoc for functions, inline comments for complex logic

## 📈 Roadmap

### 🔮 Upcoming Features

- **🌊 Real-time AIS Stream**: Live data integration
- **🗺️ Enhanced Mapping**: Advanced visualization features
- **🤖 Model Improvements**: Additional vessel types and accuracy
- **📱 Mobile App**: Native iOS/Android applications
- **🔄 API Rate Limiting**: Enhanced API protection
- **📊 Analytics Dashboard**: Advanced reporting features
- **🌍 Multi-language**: Internationalization support

### 🎯 Q1 2024 Goals

- [ ] Real-time AIS data streaming
- [ ] Mobile-responsive design improvements
- [ ] Enhanced ML model with 95%+ accuracy
- [ ] API rate limiting and quotas
- [ ] Advanced analytics dashboard

## 📜 License

This project is licensed under the **MIT License** - see the [LICENSE](./LICENSE) file for details.

## 🙏 Acknowledgments

- **AIS Data Providers** - For maritime tracking data
- **Open Source Community** - For the amazing tools and libraries
- **Maritime Industry** - For domain expertise and feedback
- **Contributors** - For making VESLINT better every day

## 📞 Support

### 🆘 Getting Help

- **📖 Documentation**: Check our [comprehensive docs](./docs/README.md)
- **💬 Discussions**: [GitHub Discussions](https://github.com/veslint/veslint/discussions)
- **🐛 Issues**: [GitHub Issues](https://github.com/veslint/veslint/issues)
- **📧 Email**: <EMAIL>

### 🌟 Stay Updated

- **⭐ Star** this repository for updates
- **👀 Watch** for new releases
- **🐦 Follow** us on [Twitter](https://twitter.com/veslint)
- **💼 Connect** on [LinkedIn](https://linkedin.com/company/veslint)

---

<div align="center">

**Made with ❤️ by the VESLINT Team**

[🌐 Website](https://veslint.com) • [📧 Email](mailto:<EMAIL>) • [🐦 Twitter](https://twitter.com/veslint) • [💼 LinkedIn](https://linkedin.com/company/veslint)

</div>