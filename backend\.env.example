# =============================================================================
# VESLINT Backend Environment Variables
# =============================================================================
# Copy this file to .env and fill in your actual values
# Never commit the actual .env file to version control
# =============================================================================

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Application Environment
# Values: development, staging, production
ENVIRONMENT=production

# Application Settings
APP_NAME=VESLINT
APP_VERSION=1.0.0
API_VERSION=v1

# Server Configuration
PORT=8000
HOST=0.0.0.0

# Logging Configuration
# Values: debug, info, warning, error, critical
LOG_LEVEL=info

# Enable/disable debug mode
DEBUG=false

# =============================================================================
# DATABASE CONFIGURATION - SUPABASE
# =============================================================================

# Supabase Configuration (Required)
# Get these from: https://app.supabase.com/project/YOUR_PROJECT/settings/api
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...your-anon-key
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...your-service-role-key

# Database Connection Pool Settings
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30

# =============================================================================
# AUTHENTICATION CONFIGURATION - FIREBASE
# =============================================================================

# Firebase Configuration (Required)
# Get these from: https://console.firebase.google.com/project/YOUR_PROJECT/settings/general
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour-Private-Key-Here\n-----END PRIVATE KEY-----"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xxxxx%40your-project.iam.gserviceaccount.com

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440

# =============================================================================
# MACHINE LEARNING CONFIGURATION
# =============================================================================

# ML Model Configuration
ML_MODEL_PATH=./assets/extreme_maritime_classifier.joblib
ML_MODEL_NAME=extreme_maritime_classifier
ML_MODEL_VERSION=1.0.0

# Hugging Face Configuration (Optional - for model hosting)
HUGGINGFACE_TOKEN=hf_your_hugging_face_token_here
HUGGINGFACE_MODEL_ID=your-username/extreme-maritime-classifier

# Feature Engineering Settings
FEATURE_COUNT=99
ENABLE_FEATURE_CACHING=true
FEATURE_CACHE_TTL=3600

# ML Processing Settings
MAX_BATCH_SIZE=1000
CHUNK_SIZE=100
PROCESSING_TIMEOUT=300

# Model Performance Settings
ENABLE_MODEL_WARMING=true
MODEL_PREDICTION_TIMEOUT=30
ENABLE_DUMMY_MODE=false

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================

# File Upload Settings
MAX_FILE_SIZE_MB=50
UPLOAD_CHUNK_SIZE=1048576
ALLOWED_FILE_TYPES=text/csv,application/csv

# Temporary File Settings
TEMP_DIR=/tmp
CLEANUP_TEMP_FILES=true
TEMP_FILE_TTL=3600

# CSV Processing Settings
CSV_DELIMITER=,
CSV_MAX_ROWS=100000
CSV_ENCODING=utf-8
ENABLE_CSV_VALIDATION=true

# =============================================================================
# EXTERNAL SERVICES CONFIGURATION
# =============================================================================

# Email Service Configuration (Optional)
# Using SendGrid for email notifications
SENDGRID_API_KEY=SG.your-sendgrid-api-key-here
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=VESLINT
ENABLE_EMAIL_NOTIFICATIONS=false

# Alternative email providers (choose one)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your-app-password
# SMTP_TLS=true

# Monitoring and Analytics
SENTRY_DSN=https://<EMAIL>/project-id
ENABLE_SENTRY=false

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# CORS Settings
CORS_ORIGINS=https://veslint.com,https://www.veslint.com,https://app.veslint.com
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOW_HEADERS=Content-Type,Authorization,X-Requested-With

# Security Headers
SECURITY_HEADERS_ENABLED=true
HSTS_MAX_AGE=31536000
CSP_POLICY="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
RATE_LIMIT_STORAGE=memory

# API Key Settings (for service-to-service communication)
API_KEY_HEADER=X-API-Key
INTERNAL_API_KEY=your-internal-api-key-for-service-communication

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# Cache Configuration
CACHE_TYPE=memory
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# Redis Configuration (if using Redis for caching)
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=your-redis-password
REDIS_DB=0

# Background Job Configuration
BACKGROUND_JOBS_ENABLED=true
MAX_CONCURRENT_JOBS=5
JOB_TIMEOUT=1800
JOB_RETRY_ATTEMPTS=3
JOB_RETRY_DELAY=60

# Worker Configuration
WORKER_CONCURRENCY=4
WORKER_PREFETCH_MULTIPLIER=1
WORKER_MAX_TASKS_PER_CHILD=1000

# =============================================================================
# MONITORING AND OBSERVABILITY
# =============================================================================

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_TIMEOUT=5
DEEP_HEALTH_CHECK=false

# Metrics Configuration
ENABLE_METRICS=true
METRICS_PORT=9090
METRICS_PATH=/metrics

# Logging Configuration
LOG_FORMAT=json
LOG_FILE_ENABLED=false
LOG_FILE_PATH=/var/log/veslint/app.log
LOG_FILE_MAX_SIZE=100MB
LOG_FILE_BACKUP_COUNT=5

# Request Logging
LOG_REQUESTS=true
LOG_RESPONSES=false
LOG_REQUEST_BODY=false
LOG_RESPONSE_BODY=false

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Development Settings (only for ENVIRONMENT=development)
DEV_RELOAD=true
DEV_DEBUG=true
DEV_PROFILING=false

# Testing Configuration
TEST_DATABASE_URL=postgresql://user:password@localhost/veslint_test
TEST_DISABLE_AUTH=false
TEST_SKIP_ML_MODELS=false

# Mock Services (for development/testing)
MOCK_FIREBASE_AUTH=false
MOCK_SUPABASE=false
MOCK_ML_SERVICE=false

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# Render.com Configuration
RENDER_INSTANCE_ID=auto-generated-by-render
RENDER_SERVICE_NAME=veslint-api
RENDER_GIT_COMMIT=auto-generated-by-render

# Health Check URLs (for monitoring services)
HEALTH_CHECK_URL=https://your-api-domain.com/health
READINESS_CHECK_URL=https://your-api-domain.com/health/ready
LIVENESS_CHECK_URL=https://your-api-domain.com/health/live

# Deployment Environment Detection
IS_PRODUCTION=true
IS_STAGING=false
IS_DEVELOPMENT=false

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Feature toggles for enabling/disabling features
FEATURE_BATCH_PROCESSING=true
FEATURE_REAL_TIME_UPDATES=true
FEATURE_EMAIL_NOTIFICATIONS=false
FEATURE_ADVANCED_ANALYTICS=true
FEATURE_EXPORT_RESULTS=true
FEATURE_API_DOCS=true

# Experimental Features
EXPERIMENTAL_FEATURES=false
EXPERIMENTAL_NEW_ML_MODEL=false
EXPERIMENTAL_PERFORMANCE_OPTIMIZATIONS=false

# =============================================================================
# THIRD-PARTY INTEGRATIONS
# =============================================================================

# Google Cloud Platform (if using any GCP services)
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json
GCP_PROJECT_ID=your-gcp-project-id

# AWS Configuration (if using any AWS services)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_DEFAULT_REGION=us-east-1

# Other External APIs
WEATHER_API_KEY=your-weather-api-key-if-needed
MARINE_TRAFFIC_API_KEY=your-marine-traffic-api-key-if-needed

# =============================================================================
# BACKUP AND MAINTENANCE
# =============================================================================

# Backup Configuration
BACKUP_ENABLED=false
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=/backups

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE="VESLINT is currently under maintenance. Please try again later."

# Cleanup Tasks
AUTO_CLEANUP_ENABLED=true
CLEANUP_OLD_JOBS_DAYS=30
CLEANUP_TEMP_FILES_HOURS=24

# =============================================================================
# CUSTOM CONFIGURATION
# =============================================================================

# Add any custom environment variables your application needs
CUSTOM_FEATURE_X=false
CUSTOM_INTEGRATION_Y_TOKEN=your-custom-token

# =============================================================================
# ENVIRONMENT VALIDATION
# =============================================================================

# The following variables are required for the application to start
# SUPABASE_URL - Database connection
# SUPABASE_ANON_KEY - Database authentication
# FIREBASE_PROJECT_ID - User authentication
# ML_MODEL_PATH - Machine learning functionality

# Optional but recommended for production
# SENTRY_DSN - Error monitoring
# SENDGRID_API_KEY - Email notifications
# CORS_ORIGINS - Security configuration

# =============================================================================
# NOTES
# =============================================================================

# 1. Replace all placeholder values with your actual credentials
# 2. Never commit the actual .env file to version control
# 3. Use different values for development, staging, and production
# 4. Regularly rotate sensitive credentials like API keys and secrets
# 5. Use a secure method to manage secrets in production (e.g., Render's environment variables)
# 6. Test your configuration thoroughly before deploying to production
# 7. Monitor your application logs for configuration-related errors

# For more information, see the deployment documentation:
# docs/DEPLOYMENT.md
