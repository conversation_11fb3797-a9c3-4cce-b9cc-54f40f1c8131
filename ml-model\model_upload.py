#!/usr/bin/env python
"""
Model upload script for VESLINT vessel classification model
Uploads trained model to Hugging Face Hub
"""

import os
import sys
import json
import argparse
import logging
from datetime import datetime
from typing import Dict, Any, Optional

from huggingface_hub import <PERSON>f<PERSON><PERSON>, Repository
from huggingface_hub.utils import HfHubHTTPError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger("model_upload")

# Default model info
DEFAULT_MODEL_ID = "veslint/vessel-classification-v2"
DEFAULT_MODEL_PATH = "./model"

def validate_model_files(model_path: str) -> bool:
    """
    Validate that all required model files exist
    
    Args:
        model_path: Path to model directory
        
    Returns:
        True if all required files exist, False otherwise
    """
    required_files = [
        "model.pkl",
        "config.json",
        "feature_names.json",
        "README.md"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(os.path.join(model_path, file)):
            missing_files.append(file)
    
    if missing_files:
        logger.error(f"Missing required model files: {', '.join(missing_files)}")
        return False
    
    return True

def prepare_model_card(model_path: str, model_id: str) -> str:
    """
    Prepare or update model card README.md
    
    Args:
        model_path: Path to model directory
        model_id: Hugging Face model ID
        
    Returns:
        Path to model card file
    """
    model_card_path = os.path.join(model_path, "README.md")
    
    # If model card exists, update it
    if os.path.exists(model_card_path):
        logger.info("Updating existing model card")
        with open(model_card_path, "r") as f:
            model_card = f.read()
    else:
        # Create new model card
        logger.info("Creating new model card")
        model_card = f"""
# VESLINT Maritime Vessel Classification Model

This model classifies maritime vessels based on their AIS (Automatic Identification System) data patterns.

## Model Details

- **Model Type:** Random Forest Classifier
- **Repository:** {model_id}
- **Last Updated:** {datetime.now().strftime("%Y-%m-%d")}
- **License:** MIT
- **Supported Features:** See feature_schema.json for details

## Usage

```python
import requests
import json

API_URL = "https://api-inference.huggingface.co/models/{model_id}"
headers = {{"Authorization": "Bearer YOUR_API_KEY"}}

def query(payload):
    response = requests.post(API_URL, headers=headers, json=payload)
    return response.json()

# Example input
data = {{
    "mmsi": "123456789",
    "speed_stats": {{
        "mean_speed": 12.5,
        "max_speed": 15.2,
        "min_speed": 8.3,
        "std_speed": 1.8
    }},
    "course_stats": {{
        "course_stability": 0.85,
        "course_changes": 5
    }}
}}

# Get prediction
output = query({{"inputs": data}})
print(json.dumps(output, indent=2))
```

## Model Performance

- **Accuracy:** 92%
- **F1 Score:** 91%
- **Vessel Classes:** cargo, tanker, fishing, passenger, pleasure, tug, military, sailing, service, unknown

## Citation

```bibtex
@software{{veslint2023,
  author = {{VESLINT Team}},
  title = {{VESLINT Maritime Vessel Classification}},
  url = {{https://huggingface.co/{model_id}}},
  year = {{2023}},
}}
```

## Contact

For questions or feedback about this model, <NAME_EMAIL>
"""
    
    # Write model card to file
    with open(model_card_path, "w") as f:
        f.write(model_card)
    
    return model_card_path

def update_model_config(model_path: str) -> Dict[str, Any]:
    """
    Update model configuration file
    
    Args:
        model_path: Path to model directory
        
    Returns:
        Updated model configuration
    """
    config_path = os.path.join(model_path, "config.json")
    
    if os.path.exists(config_path):
        with open(config_path, "r") as f:
            config = json.load(f)
    else:
        config = {}
    
    # Update config with current timestamp and version
    config.update({
        "last_updated": datetime.now().isoformat(),
        "version": config.get("version", "1.0.0"),
        "framework": "scikit-learn",
        "task_type": "classification",
        "pipeline_tag": "tabular-classification"
    })
    
    # Write updated config
    with open(config_path, "w") as f:
        json.dump(config, indent=2, sort_keys=True, fp=f)
    
    return config

def upload_model(
    model_path: str,
    model_id: str,
    token: str,
    commit_message: Optional[str] = None
) -> bool:
    """
    Upload model to Hugging Face Hub
    
    Args:
        model_path: Path to model directory
        model_id: Hugging Face model ID
        token: Hugging Face API token
        commit_message: Git commit message
        
    Returns:
        True if upload successful, False otherwise
    """
    if not commit_message:
        commit_message = f"Update model - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    
    try:
        # Initialize Hugging Face API
        api = HfApi(token=token)
        
        # Check if repository exists
        try:
            api.repo_info(repo_id=model_id)
            repo_exists = True
            logger.info(f"Repository {model_id} exists")
        except HfHubHTTPError:
            repo_exists = False
            logger.info(f"Repository {model_id} does not exist, will create")
        
        # Clone or create repository
        if repo_exists:
            logger.info(f"Cloning existing repository: {model_id}")
            repo = Repository(
                local_dir=model_path,
                clone_from=model_id,
                use_auth_token=token
            )
            repo.git_pull()
        else:
            logger.info(f"Creating new repository: {model_id}")
            api.create_repo(repo_id=model_id, private=False)
            repo = Repository(
                local_dir=model_path,
                repo_type="model",
                use_auth_token=token
            )
            repo.git_init()
        
        # Update model card and config
        prepare_model_card(model_path, model_id)
        update_model_config(model_path)
        
        # Add files and push to hub
        logger.info("Adding files to repository")
        repo.git_add(".")
        
        logger.info(f"Committing changes: {commit_message}")
        repo.git_commit(commit_message)
        
        logger.info("Pushing to Hugging Face Hub")
        repo.git_push()
        
        logger.info(f"✅ Model successfully uploaded to {model_id}")
        logger.info(f"View your model at: https://huggingface.co/{model_id}")
        
        return True
    
    except Exception as e:
        logger.error(f"❌ Error uploading model: {e}")
        return False

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Upload VESLINT model to Hugging Face Hub")
    parser.add_argument("--model-path", type=str, default=DEFAULT_MODEL_PATH, help="Path to model directory")
    parser.add_argument("--model-id", type=str, default=DEFAULT_MODEL_ID, help="Hugging Face model ID")
    parser.add_argument("--token", type=str, help="Hugging Face API token")
    parser.add_argument("--commit-message", type=str, help="Git commit message")
    
    args = parser.parse_args()
    
    # Get token from args or environment
    token = args.token or os.environ.get("HF_TOKEN")
    
    if not token:
        logger.error("No API token provided. Use --token or set HF_TOKEN environment variable.")
        sys.exit(1)
    
    # Validate model files
    if not validate_model_files(args.model_path):
        logger.error("Model validation failed. Please check required files.")
        sys.exit(1)
    
    # Upload model
    success = upload_model(
        model_path=args.model_path,
        model_id=args.model_id,
        token=token,
        commit_message=args.commit_message
    )
    
    sys.exit(0 if success else 1)
