#!/bin/bash

# =============================================================================
# VESLINT System Testing Script
# =============================================================================
# Comprehensive testing script that validates the entire VESLINT system
# including backend APIs, frontend functionality, database connectivity,
# and end-to-end workflows

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKEND_DIR="$PROJECT_ROOT/backend"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
TEST_RESULTS_DIR="$PROJECT_ROOT/test-results"

# Test Configuration
LOCAL_BACKEND_URL="http://localhost:8000"
LOCAL_FRONTEND_URL="http://localhost:3000"
PRODUCTION_BACKEND_URL=""
PRODUCTION_FRONTEND_URL=""
TEST_TIMEOUT=30
MAX_RETRIES=3

# Test Data
TEST_CSV_FILE="$PROJECT_ROOT/test-data/sample_vessels.csv"
TEST_MMSI="123456789"
TEST_USER_EMAIL="<EMAIL>"

# =============================================================================
# Utility Functions
# =============================================================================

print_header() {
    echo -e "\n${PURPLE}================================================${NC}"
    echo -e "${WHITE}$1${NC}"
    echo -e "${PURPLE}================================================${NC}\n"
}

print_section() {
    echo -e "\n${CYAN}--- $1 ---${NC}"
}

print_step() {
    echo -e "${BLUE}▶ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Test result tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
TEST_RESULTS=()

log_test_result() {
    local test_name="$1"
    local result="$2"
    local message="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [ "$result" = "PASS" ]; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        print_success "$test_name: $message"
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        print_error "$test_name: $message"
    fi
    
    TEST_RESULTS+=("$test_name:$result:$message")
}

wait_for_service() {
    local service_name="$1"
    local url="$2"
    local timeout="$3"
    local check_interval=2
    local elapsed=0
    
    print_step "Waiting for $service_name to be ready..."
    
    while [ $elapsed -lt $timeout ]; do
        if curl -s -f "$url" >/dev/null 2>&1; then
            print_success "$service_name is ready"
            return 0
        fi
        
        echo -n "."
        sleep $check_interval
        elapsed=$((elapsed + check_interval))
    done
    
    print_error "$service_name failed to start within $timeout seconds"
    return 1
}

make_api_request() {
    local method="$1"
    local url="$2"
    local data="$3"
    local expected_status="${4:-200}"
    
    local response
    local status_code
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
            -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$url" 2>/dev/null)
    else
        response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
            -X "$method" \
            "$url" 2>/dev/null)
    fi
    
    status_code=$(echo "$response" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    response_body=$(echo "$response" | sed -e 's/HTTPSTATUS:.*//g')
    
    if [ "$status_code" = "$expected_status" ]; then
        echo "$response_body"
        return 0
    else
        echo "ERROR: Expected status $expected_status, got $status_code"
        echo "Response: $response_body"
        return 1
    fi
}

# =============================================================================
# Test Data Setup
# =============================================================================

setup_test_data() {
    print_header "Setting Up Test Data"
    
    # Create test results directory
    mkdir -p "$TEST_RESULTS_DIR"
    
    # Create sample CSV file if it doesn't exist
    if [ ! -f "$TEST_CSV_FILE" ]; then
        print_step "Creating sample test CSV file"
        mkdir -p "$(dirname "$TEST_CSV_FILE")"
        
        cat > "$TEST_CSV_FILE" << 'EOF'
mmsi,timestamp,lat,lon,sog,cog,heading,vessel_name,call_sign
123456789,2024-01-01T00:00:00Z,40.7128,-74.0060,12.5,90.0,85.0,TEST_VESSEL_1,TEST1
123456789,2024-01-01T00:15:00Z,40.7130,-74.0040,13.2,92.0,88.0,TEST_VESSEL_1,TEST1
123456789,2024-01-01T00:30:00Z,40.7135,-74.0020,14.1,95.0,92.0,TEST_VESSEL_1,TEST1
987654321,2024-01-01T00:00:00Z,51.5074,-0.1278,8.5,180.0,175.0,TEST_VESSEL_2,TEST2
987654321,2024-01-01T00:20:00Z,51.5070,-0.1280,7.8,185.0,182.0,TEST_VESSEL_2,TEST2
987654321,2024-01-01T00:40:00Z,51.5065,-0.1285,9.2,190.0,188.0,TEST_VESSEL_2,TEST2
555666777,2024-01-01T00:00:00Z,37.7749,-122.4194,25.3,270.0,265.0,TEST_VESSEL_3,TEST3
555666777,2024-01-01T00:10:00Z,37.7750,-122.4200,26.1,275.0,270.0,TEST_VESSEL_3,TEST3
555666777,2024-01-01T00:20:00Z,37.7755,-122.4210,24.8,280.0,275.0,TEST_VESSEL_3,TEST3
EOF
        print_success "Sample CSV file created"
    else
        print_info "Test CSV file already exists"
    fi
    
    print_success "Test data setup completed"
}

# =============================================================================
# Environment Detection
# =============================================================================

detect_environment() {
    print_header "Detecting Test Environment"
    
    # Check for local development environment
    print_step "Checking for local backend"
    if curl -s -f "$LOCAL_BACKEND_URL/health" >/dev/null 2>&1; then
        print_success "Local backend detected at $LOCAL_BACKEND_URL"
        BACKEND_URL="$LOCAL_BACKEND_URL"
    else
        print_warning "Local backend not running"
    fi
    
    print_step "Checking for local frontend"
    if curl -s -f "$LOCAL_FRONTEND_URL" >/dev/null 2>&1; then
        print_success "Local frontend detected at $LOCAL_FRONTEND_URL"
        FRONTEND_URL="$LOCAL_FRONTEND_URL"
    else
        print_warning "Local frontend not running"
    fi
    
    # Check for production URLs in environment
    if [ -n "$PRODUCTION_BACKEND_URL" ]; then
        print_step "Checking production backend"
        if curl -s -f "$PRODUCTION_BACKEND_URL/health" >/dev/null 2>&1; then
            print_success "Production backend available at $PRODUCTION_BACKEND_URL"
            BACKEND_URL="$PRODUCTION_BACKEND_URL"
        fi
    fi
    
    if [ -n "$PRODUCTION_FRONTEND_URL" ]; then
        print_step "Checking production frontend"
        if curl -s -f "$PRODUCTION_FRONTEND_URL" >/dev/null 2>&1; then
            print_success "Production frontend available at $PRODUCTION_FRONTEND_URL"
            FRONTEND_URL="$PRODUCTION_FRONTEND_URL"
        fi
    fi
    
    # Set defaults if nothing detected
    BACKEND_URL="${BACKEND_URL:-$LOCAL_BACKEND_URL}"
    FRONTEND_URL="${FRONTEND_URL:-$LOCAL_FRONTEND_URL}"
    
    print_info "Testing against:"
    print_info "  Backend: $BACKEND_URL"
    print_info "  Frontend: $FRONTEND_URL"
}

# =============================================================================
# Backend API Tests
# =============================================================================

test_backend_health() {
    print_section "Backend Health Tests"
    
    print_step "Testing health endpoint"
    if response=$(make_api_request "GET" "$BACKEND_URL/health" "" "200"); then
        log_test_result "backend_health" "PASS" "Health endpoint responding"
        
        # Parse health response
        if echo "$response" | grep -q '"status":"healthy"'; then
            log_test_result "backend_health_status" "PASS" "Backend reports healthy status"
        else
            log_test_result "backend_health_status" "FAIL" "Backend reports unhealthy status"
        fi
    else
        log_test_result "backend_health" "FAIL" "Health endpoint not responding"
    fi
    
    print_step "Testing API documentation"
    if curl -s -f "$BACKEND_URL/docs" >/dev/null 2>&1; then
        log_test_result "backend_docs" "PASS" "API documentation accessible"
    else
        log_test_result "backend_docs" "FAIL" "API documentation not accessible"
    fi
    
    print_step "Testing OpenAPI schema"
    if curl -s -f "$BACKEND_URL/openapi.json" >/dev/null 2>&1; then
        log_test_result "backend_openapi" "PASS" "OpenAPI schema available"
    else
        log_test_result "backend_openapi" "FAIL" "OpenAPI schema not available"
    fi
}

test_backend_auth() {
    print_section "Backend Authentication Tests"
    
    print_step "Testing unauthenticated access to protected endpoints"
    if response=$(make_api_request "GET" "$BACKEND_URL/api/v1/jobs" "" "401"); then
        log_test_result "backend_auth_protection" "PASS" "Protected endpoints require authentication"
    else
        log_test_result "backend_auth_protection" "FAIL" "Protected endpoints not properly secured"
    fi
    
    print_step "Testing CORS configuration"
    if curl -s -H "Origin: https://veslint.com" -f "$BACKEND_URL/health" >/dev/null 2>&1; then
        log_test_result "backend_cors" "PASS" "CORS properly configured"
    else
        log_test_result "backend_cors" "FAIL" "CORS configuration issues"
    fi
}

test_backend_vessel_classification() {
    print_section "Backend Vessel Classification Tests"
    
    print_step "Testing vessel classification endpoint structure"
    # This would normally require authentication, so we just test the endpoint exists
    response=$(make_api_request "POST" "$BACKEND_URL/api/v1/vessels/classify" '{}' "401")
    if [ $? -eq 0 ]; then
        log_test_result "backend_classify_endpoint" "PASS" "Classification endpoint exists"
    else
        log_test_result "backend_classify_endpoint" "FAIL" "Classification endpoint not found"
    fi
    
    print_step "Testing jobs endpoint structure"
    response=$(make_api_request "GET" "$BACKEND_URL/api/v1/jobs" "" "401")
    if [ $? -eq 0 ]; then
        log_test_result "backend_jobs_endpoint" "PASS" "Jobs endpoint exists"
    else
        log_test_result "backend_jobs_endpoint" "FAIL" "Jobs endpoint not found"
    fi
}

test_backend_performance() {
    print_section "Backend Performance Tests"
    
    print_step "Testing response time"
    start_time=$(date +%s.%N)
    if curl -s -f "$BACKEND_URL/health" >/dev/null 2>&1; then
        end_time=$(date +%s.%N)
        response_time=$(echo "$end_time - $start_time" | bc)
        
        if (( $(echo "$response_time < 2.0" | bc -l) )); then
            log_test_result "backend_response_time" "PASS" "Response time: ${response_time}s"
        else
            log_test_result "backend_response_time" "WARN" "Slow response time: ${response_time}s"
        fi
    else
        log_test_result "backend_response_time" "FAIL" "Could not measure response time"
    fi
    
    print_step "Testing concurrent requests"
    local concurrent_success=0
    for i in {1..5}; do
        if curl -s -f "$BACKEND_URL/health" >/dev/null 2>&1 &; then
            concurrent_success=$((concurrent_success + 1))
        fi
    done
    wait
    
    if [ $concurrent_success -eq 5 ]; then
        log_test_result "backend_concurrency" "PASS" "Handles concurrent requests"
    else
        log_test_result "backend_concurrency" "FAIL" "Failed concurrent request test"
    fi
}

# =============================================================================
# Frontend Tests
# =============================================================================

test_frontend_accessibility() {
    print_section "Frontend Accessibility Tests"
    
    print_step "Testing homepage availability"
    if curl -s -f "$FRONTEND_URL" >/dev/null 2>&1; then
        log_test_result "frontend_homepage" "PASS" "Homepage accessible"
    else
        log_test_result "frontend_homepage" "FAIL" "Homepage not accessible"
    fi
    
    print_step "Testing static assets"
    # Check for common static files
    local static_files=("favicon.ico" "_next/static/css" "_next/static/js")
    local static_success=0
    
    for file in "${static_files[@]}"; do
        if curl -s -f "$FRONTEND_URL/$file" >/dev/null 2>&1; then
            static_success=$((static_success + 1))
        fi
    done
    
    if [ $static_success -gt 0 ]; then
        log_test_result "frontend_static_assets" "PASS" "Static assets loading"
    else
        log_test_result "frontend_static_assets" "FAIL" "Static assets not loading"
    fi
    
    print_step "Testing security headers"
    headers=$(curl -s -I "$FRONTEND_URL")
    if echo "$headers" | grep -q "X-Frame-Options"; then
        log_test_result "frontend_security_headers" "PASS" "Security headers present"
    else
        log_test_result "frontend_security_headers" "FAIL" "Security headers missing"
    fi
}

test_frontend_performance() {
    print_section "Frontend Performance Tests"
    
    print_step "Testing page load time"
    start_time=$(date +%s.%N)
    if curl -s -f "$FRONTEND_URL" >/dev/null 2>&1; then
        end_time=$(date +%s.%N)
        load_time=$(echo "$end_time - $start_time" | bc)
        
        if (( $(echo "$load_time < 3.0" | bc -l) )); then
            log_test_result "frontend_load_time" "PASS" "Load time: ${load_time}s"
        else
            log_test_result "frontend_load_time" "WARN" "Slow load time: ${load_time}s"
        fi
    else
        log_test_result "frontend_load_time" "FAIL" "Could not measure load time"
    fi
    
    print_step "Testing compression"
    if curl -s -H "Accept-Encoding: gzip" -I "$FRONTEND_URL" | grep -q "Content-Encoding: gzip"; then
        log_test_result "frontend_compression" "PASS" "Gzip compression enabled"
    else
        log_test_result "frontend_compression" "WARN" "Gzip compression not detected"
    fi
}

# =============================================================================
# Database Tests
# =============================================================================

test_database_connectivity() {
    print_section "Database Connectivity Tests"
    
    print_step "Testing database connection via backend"
    # Test through backend health check which includes DB check
    if response=$(make_api_request "GET" "$BACKEND_URL/health" "" "200"); then
        if echo "$response" | grep -q '"database":"connected"'; then
            log_test_result "database_connection" "PASS" "Database connection successful"
        else
            log_test_result "database_connection" "FAIL" "Database connection failed"
        fi
    else
        log_test_result "database_connection" "FAIL" "Could not test database connection"
    fi
    
    print_step "Testing database schema"
    # This would require direct database access or a specific endpoint
    log_test_result "database_schema" "INFO" "Manual verification required"
}

# =============================================================================
# End-to-End Tests
# =============================================================================

test_end_to_end_workflow() {
    print_section "End-to-End Workflow Tests"
    
    print_step "Testing complete user workflow simulation"
    log_test_result "e2e_workflow" "INFO" "E2E tests require authentication setup"
    
    # Future implementation would include:
    # 1. User registration/login
    # 2. File upload
    # 3. Job creation
    # 4. Processing monitoring
    # 5. Results retrieval
    
    print_step "Testing file upload size limits"
    log_test_result "file_upload_limits" "INFO" "Manual testing required"
    
    print_step "Testing real-time updates"
    log_test_result "realtime_updates" "INFO" "WebSocket testing required"
}

# =============================================================================
# Integration Tests
# =============================================================================

test_external_integrations() {
    print_section "External Integration Tests"
    
    print_step "Testing Supabase connectivity"
    # Test if environment variables are set
    if [ -n "$SUPABASE_URL" ] && [ -n "$SUPABASE_ANON_KEY" ]; then
        if curl -s -f "$SUPABASE_URL/rest/v1/" -H "apikey: $SUPABASE_ANON_KEY" >/dev/null 2>&1; then
            log_test_result "supabase_integration" "PASS" "Supabase connection successful"
        else
            log_test_result "supabase_integration" "FAIL" "Supabase connection failed"
        fi
    else
        log_test_result "supabase_integration" "SKIP" "Supabase credentials not configured"
    fi
    
    print_step "Testing HuggingFace model access"
    if [ -n "$HUGGINGFACE_API_TOKEN" ]; then
        log_test_result "huggingface_integration" "INFO" "HuggingFace token configured"
    else
        log_test_result "huggingface_integration" "SKIP" "HuggingFace token not configured"
    fi
    
    print_step "Testing Firebase Auth"
    if [ -n "$FIREBASE_PROJECT_ID" ]; then
        log_test_result "firebase_integration" "INFO" "Firebase project configured"
    else
        log_test_result "firebase_integration" "SKIP" "Firebase not configured"
    fi
}

# =============================================================================
# Security Tests
# =============================================================================

test_security() {
    print_section "Security Tests"
    
    print_step "Testing SQL injection protection"
    # Test with common SQL injection patterns
    response=$(make_api_request "GET" "$BACKEND_URL/api/v1/jobs?id=1';DROP TABLE jobs;--" "" "401")
    if [ $? -eq 0 ]; then
        log_test_result "sql_injection_protection" "PASS" "SQL injection protection in place"
    else
        log_test_result "sql_injection_protection" "FAIL" "SQL injection vulnerability possible"
    fi
    
    print_step "Testing XSS protection"
    # Test XSS in query parameters
    if curl -s -f "$FRONTEND_URL?search=%3Cscript%3Ealert('xss')%3C/script%3E" >/dev/null 2>&1; then
        log_test_result "xss_protection" "INFO" "Manual XSS testing required"
    else
        log_test_result "xss_protection" "INFO" "Basic XSS protection appears active"
    fi
    
    print_step "Testing HTTPS enforcement"
    if [[ "$FRONTEND_URL" == https://* ]] && [[ "$BACKEND_URL" == https://* ]]; then
        log_test_result "https_enforcement" "PASS" "HTTPS enforced"
    else
        log_test_result "https_enforcement" "WARN" "Not all endpoints use HTTPS"
    fi
}

# =============================================================================
# Report Generation
# =============================================================================

generate_test_report() {
    print_header "Test Results Summary"
    
    # Calculate success rate
    local success_rate=0
    if [ $TOTAL_TESTS -gt 0 ]; then
        success_rate=$(echo "scale=1; $PASSED_TESTS * 100 / $TOTAL_TESTS" | bc)
    fi
    
    # Print summary
    echo -e "${WHITE}Total Tests: $TOTAL_TESTS${NC}"
    echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
    echo -e "${RED}Failed: $FAILED_TESTS${NC}"
    echo -e "${WHITE}Success Rate: $success_rate%${NC}"
    echo ""
    
    # Generate detailed report
    local report_file="$TEST_RESULTS_DIR/test_report_$(date +%Y%m%d_%H%M%S).json"
    cat > "$report_file" << EOF
{
  "test_run": {
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "total_tests": $TOTAL_TESTS,
    "passed_tests": $PASSED_TESTS,
    "failed_tests": $FAILED_TESTS,
    "success_rate": $success_rate,
    "backend_url": "$BACKEND_URL",
    "frontend_url": "$FRONTEND_URL"
  },
  "test_results": [
EOF

    # Add test results
    local first=true
    for result in "${TEST_RESULTS[@]}"; do
        if [ "$first" = true ]; then
            first=false
        else
            echo "," >> "$report_file"
        fi
        
        IFS=':' read -r test_name test_result test_message <<< "$result"
        cat >> "$report_file" << EOF
    {
      "test_name": "$test_name",
      "result": "$test_result",
      "message": "$test_message"
    }
EOF
    done
    
    echo "" >> "$report_file"
    echo "  ]" >> "$report_file"
    echo "}" >> "$report_file"
    
    print_success "Test report generated: $report_file"
    
    # Print failed tests
    if [ $FAILED_TESTS -gt 0 ]; then
        echo ""
        print_error "Failed Tests:"
        for result in "${TEST_RESULTS[@]}"; do
            IFS=':' read -r test_name test_result test_message <<< "$result"
            if [ "$test_result" = "FAIL" ]; then
                echo -e "  ${RED}$test_name: $test_message${NC}"
            fi
        done
    fi
    
    # Return appropriate exit code
    if [ $FAILED_TESTS -eq 0 ]; then
        print_success "All tests passed! 🎉"
        return 0
    else
        print_error "Some tests failed. Please review the results."
        return 1
    fi
}

# =============================================================================
# Main Execution
# =============================================================================

main() {
    print_header "VESLINT System Testing Suite"
    
    echo -e "${BLUE}This script performs comprehensive testing of the VESLINT system${NC}"
    echo -e "${BLUE}including backend APIs, frontend functionality, and integrations.${NC}"
    echo ""
    
    # Setup
    setup_test_data
    detect_environment
    
    # Run test suites
    test_backend_health
    test_backend_auth
    test_backend_vessel_classification
    test_backend_performance
    test_frontend_accessibility
    test_frontend_performance
    test_database_connectivity
    test_end_to_end_workflow
    test_external_integrations
    test_security
    
    # Generate report
    if generate_test_report; then
        exit 0
    else
        exit 1
    fi
}

# Handle script arguments
case "${1:-}" in
    "--help"|"-h")
        echo "VESLINT System Testing Script"
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h    Show this help message"
        echo "  --backend-url URL    Override backend URL"
        echo "  --frontend-url URL   Override frontend URL"
        echo ""
        echo "Environment Variables:"
        echo "  PRODUCTION_BACKEND_URL   Production backend URL"
        echo "  PRODUCTION_FRONTEND_URL  Production frontend URL"
        echo "  SUPABASE_URL            Supabase project URL"
        echo "  SUPABASE_ANON_KEY       Supabase anonymous key"
        echo "  FIREBASE_PROJECT_ID     Firebase project ID"
        echo "  HUGGINGFACE_API_TOKEN   HuggingFace API token"
        exit 0
        ;;
    "--backend-url")
        PRODUCTION_BACKEND_URL="$2"
        shift 2
        ;;
    "--frontend-url")
        PRODUCTION_FRONTEND_URL="$2"
        shift 2
        ;;
    *)
        main "$@"
        ;;
esac