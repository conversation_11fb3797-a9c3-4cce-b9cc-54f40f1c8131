# =============================================================================
# VESLINT Frontend .dockerignore
# =============================================================================
# Exclude files and directories from Docker build context to improve build performance
# and reduce image size. This is especially important for the chown operation.

# =============================================================================
# Node.js Dependencies
# =============================================================================
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# =============================================================================
# Next.js Build Artifacts
# =============================================================================
.next/
out/
build/
dist/

# =============================================================================
# Development Files
# =============================================================================
.env*.local
.env.development
.env.test

# =============================================================================
# IDE and Editor Files
# =============================================================================
.vscode/
.idea/
*.swp
*.swo
*~

# =============================================================================
# OS Generated Files
# =============================================================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# =============================================================================
# Git Files
# =============================================================================
.git
.gitignore
.gitattributes

# =============================================================================
# Testing and Coverage
# =============================================================================
coverage/
.nyc_output
.jest-cache/
test-results/
playwright-report/

# =============================================================================
# Logs
# =============================================================================
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# =============================================================================
# Runtime Data
# =============================================================================
pids
*.pid
*.seed
*.pid.lock

# =============================================================================
# Documentation
# =============================================================================
README.md
CHANGELOG.md
docs/
*.md

# =============================================================================
# Docker Files (avoid recursive Docker builds)
# =============================================================================
Dockerfile*
docker-compose*.yml
.dockerignore

# =============================================================================
# Temporary Files
# =============================================================================
.tmp/
temp/
tmp/

# =============================================================================
# Package Manager Files (keep package.json and package-lock.json)
# =============================================================================
.yarn/
.pnp.*

# =============================================================================
# Storybook
# =============================================================================
.storybook/
storybook-static/

# =============================================================================
# ESLint and Prettier Cache
# =============================================================================
.eslintcache
.prettierignore

# =============================================================================
# TypeScript Cache
# =============================================================================
*.tsbuildinfo

# =============================================================================
# Vercel
# =============================================================================
.vercel

# =============================================================================
# Sentry
# =============================================================================
.sentryclirc

# =============================================================================
# Performance and Analysis
# =============================================================================
.bundle-analyzer/
bundle-analyzer-report.html

# =============================================================================
# Miscellaneous
# =============================================================================
.cache/
.parcel-cache/
.sass-cache/
.env.backup
