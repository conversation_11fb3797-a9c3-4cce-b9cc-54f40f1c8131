import React, { useState, useEffect, useContext, createContext, FC } from 'react';
import { auth, isFirebaseInitialized } from '../lib/firebase'; // Fixed: Corrected import path to match actual file location
import { User, onAuthStateChanged } from 'firebase/auth';

interface AuthContextType {
    user: User | null;
    loading: boolean;
}

const AuthContext = createContext<AuthContextType>({ user: null, loading: true });

/**
 * Enhanced Auth Provider with Crystal Intelligence styling
 * Preserves all Firebase authentication logic while improving UX
 */
export const AuthProvider: FC<{children: React.ReactNode}> = ({ children }) => {
    const [user, setUser] = useState<User | null>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        // Enhanced Firebase auth state listener with error handling
        let unsubscribe: (() => void) | undefined;

        try {
            // Check if Firebase is properly initialized
            if (!isFirebaseInitialized() || !auth) {
                console.warn('⚠️ Firebase not initialized, authentication will not work');
                setUser(null);
                setLoading(false);
                return;
            }

            // Set up auth state listener
            unsubscribe = onAuthStateChanged(auth, (user) => {
                setUser(user);
                setLoading(false);
            }, (error) => {
                // Handle auth state change errors
                console.error('❌ Firebase auth state change error:', error);
                setUser(null);
                setLoading(false);
            });

        } catch (error) {
            console.error('❌ Failed to set up Firebase auth listener:', error);
            setUser(null);
            setLoading(false);
        }

        // Cleanup function
        return () => {
            if (unsubscribe) {
                try {
                    unsubscribe();
                } catch (error) {
                    console.error('❌ Error during auth listener cleanup:', error);
                }
            }
        };
    }, []);

    const value = {
        user,
        loading,
    };

    return (
        <AuthContext.Provider value={value}>
            {children}
        </AuthContext.Provider>
    );
};

/**
 * Custom hook for accessing authentication context
 * Maintains exact same interface as original implementation
 */
export const useAuth = (): AuthContextType => {
    const context = useContext(AuthContext);
    
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    
    return context;
};