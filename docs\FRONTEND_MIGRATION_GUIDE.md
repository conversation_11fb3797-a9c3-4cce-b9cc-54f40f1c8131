# Frontend Migration Guide: React to Next.js 13+

## 🎯 Goal: Zero Content Changes, Just Reorganization

This guide will help you migrate your existing React frontend to Next.js 13+ App Router **without changing any component content**. We're only moving files and updating imports.

## 📁 Step 1: Create New Next.js Structure

```bash
# Create new Next.js project
npx create-next-app@latest veslint-frontend --typescript --tailwind --eslint --src-dir --app --import-alias="@/*"

cd veslint-frontend
```

## 📦 Step 2: Install Required Dependencies

```bash
# Install the exact same dependencies your current project uses
npm install @mui/material @emotion/react @emotion/styled @mui/icons-material
npm install firebase
npm install framer-motion
npm install react-dropzone
npm install react-router-dom  # This will be replaced by Next.js routing
npm install recharts
npm install @supabase/supabase-js  # New for Supabase integration

# Install additional Next.js compatible packages
npm install @next/font
```

## 🗂️ Step 3: File Migration Map

### **DO NOT MODIFY CONTENT - JUST MOVE & RENAME**

| Current Location | New Location | Action Required |
|------------------|--------------|-----------------|
| **PAGE COMPONENTS** | | |
| `frontend/src/pages/LandingPage.tsx` | `veslint-frontend/src/app/page.tsx` | Move + rename default export |
| `frontend/src/pages/DashboardPage.tsx` | `veslint-frontend/src/app/dashboard/page.tsx` | Move + rename default export |
| `frontend/src/pages/NewAnalysisPage.tsx` | `veslint-frontend/src/app/new-analysis/page.tsx` | Move + rename default export |
| `frontend/src/pages/ResultsPage.tsx` | `veslint-frontend/src/app/results/[jobId]/page.tsx` | Move + update to use params |
| `frontend/src/pages/LoginPage.tsx` | `veslint-frontend/src/app/login/page.tsx` | Move + rename default export |
| `frontend/src/pages/RegisterPage.tsx` | `veslint-frontend/src/app/register/page.tsx` | Move + rename default export |
| `frontend/src/pages/AboutUsPage.tsx` | `veslint-frontend/src/app/about/page.tsx` | Move + rename default export |
| `frontend/src/pages/ContactPage.tsx` | `veslint-frontend/src/app/contact/page.tsx` | Move + rename default export |
| `frontend/src/pages/ResearchPage.tsx` | `veslint-frontend/src/app/research/page.tsx` | Move + rename default export |
| `frontend/src/pages/PrivacyPolicyPage.tsx` | `veslint-frontend/src/app/privacy/page.tsx` | Move + rename default export |
| `frontend/src/pages/TermsOfServicePage.tsx` | `veslint-frontend/src/app/terms/page.tsx` | Move + rename default export |
| **COMPONENTS** | | |
| `frontend/src/components/**/*` | `veslint-frontend/src/components/**/*` | **Copy ALL as-is** |
| **HOOKS** | | |
| `frontend/src/hooks/**/*` | `veslint-frontend/src/hooks/**/*` | **Copy ALL as-is** |
| **THEME & UTILITIES** | | |
| `frontend/src/theme/**/*` | `veslint-frontend/src/lib/theme/**/*` | Move to lib directory |
| `frontend/src/features/**/*` | `veslint-frontend/src/components/**/*` | Merge into components |
| **ASSETS** | | |
| `frontend/public/**/*` | `veslint-frontend/public/**/*` | **Copy ALL as-is** |
| `frontend/src/index.css` | `veslint-frontend/src/app/globals.css` | Move global styles |

## 🔄 Step 4: Detailed Migration Steps

### A. Create Directory Structure

```bash
cd veslint-frontend/src

# Create necessary directories
mkdir -p app/dashboard app/new-analysis app/results/\[jobId\] app/login app/register
mkdir -p app/about app/contact app/research app/privacy app/terms
mkdir -p components/ui components/layout components/auth components/dashboard
mkdir -p components/analysis components/charts
mkdir -p hooks lib/theme types
```

### B. Copy Static Assets (No Changes)

```bash
# Copy all public assets exactly as they are
cp -r ../frontend/public/* veslint-frontend/public/

# Copy components exactly as they are
cp -r ../frontend/src/components/* veslint-frontend/src/components/

# Copy hooks exactly as they are
cp -r ../frontend/src/hooks/* veslint-frontend/src/hooks/

# Copy theme files
cp -r ../frontend/src/theme/* veslint-frontend/src/lib/theme/
```

### C. Migrate Page Components

For each page component, follow this pattern:

#### Example: Landing Page Migration

**Original file:** `frontend/src/pages/LandingPage.tsx`
```typescript
// Keep ALL the existing component code exactly the same
// Just change the export and add this wrapper

import React from 'react';
// ... all your existing imports stay the same

// ALL YOUR EXISTING COMPONENT CODE STAYS EXACTLY THE SAME
const LandingPage: React.FC = () => {
  // ... all your existing component logic unchanged
  return (
    // ... all your existing JSX unchanged
  );
};

export default LandingPage;
```

**New file:** `veslint-frontend/src/app/page.tsx`
```typescript
// Same imports and component code, just different export name
import React from 'react';
// ... copy ALL your existing imports exactly

// Copy your ENTIRE component code here - no changes to content
const HomePage: React.FC = () => {
  // Copy ALL your existing component logic exactly as-is
  return (
    // Copy ALL your existing JSX exactly as-is
  );
};

export default HomePage;  // Next.js convention
```

#### Example: Dynamic Route (Results Page)

**Original:** `frontend/src/pages/ResultsPage.tsx`
```typescript
// Your existing component that uses useParams from react-router-dom
import { useParams } from 'react-router-dom';

const ResultsPage: React.FC = () => {
  const { jobId } = useParams<{ jobId: string }>();
  // ... rest of your component
};
```

**New:** `veslint-frontend/src/app/results/[jobId]/page.tsx`
```typescript
// Update only the params usage - keep everything else the same
interface PageProps {
  params: { jobId: string }
}

const ResultsPage: React.FC<PageProps> = ({ params }) => {
  const { jobId } = params;  // Next.js way to get dynamic route params
  
  // ALL your existing component logic stays exactly the same
  // ... copy everything else unchanged
};

export default ResultsPage;
```

### D. Update App Layout

Create `veslint-frontend/src/app/layout.tsx`:
```typescript
import './globals.css'
import { Inter } from 'next/font/google'
import { AuthProvider } from '@/hooks/useAuth'  // Your existing hook
import { ThemeProvider } from '@/lib/theme/theme'  // Your existing theme

const inter = Inter({ subsets: ['latin'] })

export const metadata = {
  title: 'VESLINT - Maritime Intelligence',
  description: 'AI-Powered Vessel Classification System',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthProvider>
          <ThemeProvider>
            {children}
          </ThemeProvider>
        </AuthProvider>
      </body>
    </html>
  )
}
```

### E. Create New Configuration Files

Create `veslint-frontend/src/lib/supabase.ts`:
```typescript
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
})
```

Create `veslint-frontend/src/lib/api.ts`:
```typescript
// Replace your existing API calls to point to the new Render backend
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

export const api = {
  baseURL: API_BASE_URL,
  
  // Copy your existing API functions but update the base URL
  async classifyVessels(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    
    const response = await fetch(`${API_BASE_URL}/api/v1/vessels/classify`, {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': `Bearer ${await getAuthToken()}`
      }
    })
    
    return response.json()
  }
  
  // Add all your other existing API methods here
}

async function getAuthToken() {
  const { getAuth } = await import('firebase/auth')
  const auth = getAuth()
  return auth.currentUser?.getIdToken() || ''
}
```

### F. Update Hook for Real-time with Supabase

Update your existing `useJobMonitoring.ts` hook:
```typescript
// Keep all your existing logic, just add Supabase real-time
import { useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase'

export function useJobMonitoring(userId: string) {
  const [jobs, setJobs] = useState([])
  
  useEffect(() => {
    // Your existing job loading logic
    const loadJobs = async () => {
      // Keep your existing job loading code
    }
    
    loadJobs()
    
    // Add real-time subscription
    const subscription = supabase
      .channel('job-changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'jobs' },
        (payload) => {
          // Your existing job update logic
          // Just add real-time updates here
        }
      )
      .subscribe()
    
    return () => {
      subscription.unsubscribe()
    }
  }, [userId])
  
  return jobs
}
```

## 🔧 Step 5: Update Configuration Files

### Environment Variables
Create `veslint-frontend/.env.local`:
```bash
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Firebase Configuration (keep your existing values)
NEXT_PUBLIC_FIREBASE_API_KEY=your_existing_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_existing_auth_domain
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_existing_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_existing_storage_bucket
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_existing_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_existing_app_id
```

### Next.js Configuration
Update `veslint-frontend/next.config.js`:
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  // Add any custom configuration your project needs
  images: {
    domains: ['your-image-domains.com'],
  },
  // Keep any existing webpack config you have
}

module.exports = nextConfig
```

### Tailwind Configuration
Update `veslint-frontend/tailwind.config.js`:
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      // Copy any custom theme extensions from your existing project
    },
  },
  plugins: [
    // Copy any existing Tailwind plugins
  ],
}
```

## 🧪 Step 6: Test the Migration

```bash
# Start the development server
cd veslint-frontend
npm run dev

# Visit http://localhost:3000
# Test each page to ensure everything works
```

### Test Checklist:
- [ ] Landing page loads correctly
- [ ] Navigation between pages works
- [ ] Authentication flow works
- [ ] File upload functionality works
- [ ] All components render correctly
- [ ] Real-time updates work (when backend is connected)
- [ ] Styles and themes are applied correctly

## 🚀 Step 7: Deploy to Vercel

```bash
# Initialize git
git init
git add .
git commit -m "VESLINT Next.js migration"

# Push to GitHub
git remote add origin your-github-repo-url
git push -u origin main

# Deploy to Vercel
# 1. Connect repository in Vercel dashboard
# 2. Add environment variables
# 3. Deploy automatically
```

## ✅ Migration Complete!

Your frontend is now:
- ✅ **Running on Next.js 13+** with App Router
- ✅ **All existing components preserved** without content changes
- ✅ **Modern routing** with file-based navigation
- ✅ **Optimized performance** with Next.js features
- ✅ **Real-time updates** via Supabase
- ✅ **API integration** with your new Render backend
- ✅ **Ready for Vercel deployment**

## 🔧 Common Issues & Solutions

### Issue: Import Errors
**Solution:** Update import paths to use the new directory structure
```typescript
// Old
import { Component } from '../components/Component'

// New (use alias)
import { Component } from '@/components/Component'
```

### Issue: Router Hooks Not Working
**Solution:** Replace react-router-dom hooks with Next.js equivalents
```typescript
// Old
import { useNavigate, useParams } from 'react-router-dom'

// New
import { useRouter } from 'next/navigation'
// For params, use the params prop in page components
```

### Issue: Environment Variables Not Loading
**Solution:** Ensure variables are prefixed with `NEXT_PUBLIC_`
```bash
# Wrong
API_URL=http://localhost:8000

# Correct
NEXT_PUBLIC_API_URL=http://localhost:8000
```

Your migration is complete! The frontend now uses modern Next.js architecture while preserving all your existing functionality.