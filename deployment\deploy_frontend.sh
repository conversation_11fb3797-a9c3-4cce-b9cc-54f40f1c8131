#!/bin/bash

# =============================================================================
# VESLINT Frontend Deployment Script
# =============================================================================
# Professional deployment script for deploying the VESLINT frontend to Vercel
# while preserving existing React components and ensuring proper integration
# with the backend services and databases.
# =============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

log_deploy() {
    echo -e "${CYAN}[DEPLOY]${NC} $1"
}

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &> /dev/null && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
CONFIG_DIR="$PROJECT_ROOT/frontend-config"

# Deployment settings
ENVIRONMENT="${1:-production}"
SKIP_TESTS="${2:-false}"
SKIP_BUILD="${3:-false}"
FORCE_DEPLOY="${4:-false}"

# Vercel settings
VERCEL_ORG="${VERCEL_ORG:-}"
VERCEL_PROJECT="${VERCEL_PROJECT:-veslint-frontend}"

# =============================================================================
# Helper Functions
# =============================================================================

check_prerequisites() {
    log_step "Checking prerequisites..."
    
    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed. Please install Node.js 18+ first."
        exit 1
    fi
    
    # Check Node.js version
    NODE_VERSION=$(node --version | cut -d'v' -f2)
    REQUIRED_VERSION="18.0.0"
    if ! npx semver "$NODE_VERSION" -r ">=$REQUIRED_VERSION" &> /dev/null; then
        log_error "Node.js version $NODE_VERSION is not supported. Please install Node.js 18+ first."
        exit 1
    fi
    
    # Check if npm is installed
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    # Check if Vercel CLI is installed
    if ! command -v vercel &> /dev/null; then
        log_warning "Vercel CLI is not installed. Installing globally..."
        npm install -g vercel@latest
    fi
    
    # Check if git is available
    if ! command -v git &> /dev/null; then
        log_warning "Git is not available. Some deployment features may not work."
    fi
    
    log_success "Prerequisites check completed"
}

setup_environment() {
    log_step "Setting up deployment environment..."
    
    # Create frontend directory if it doesn't exist
    if [ ! -d "$FRONTEND_DIR" ]; then
        log_error "Frontend directory not found: $FRONTEND_DIR"
        exit 1
    fi
    
    cd "$FRONTEND_DIR"
    
    # Check if package.json exists
    if [ ! -f "package.json" ]; then
        log_error "package.json not found in frontend directory"
        exit 1
    fi
    
    # Copy configuration files if they exist
    if [ -d "$CONFIG_DIR" ]; then
        log_info "Copying configuration files..."
        
        if [ -f "$CONFIG_DIR/next.config.js" ]; then
            cp "$CONFIG_DIR/next.config.js" ./
            log_info "Copied next.config.js"
        fi
        
        if [ -f "$CONFIG_DIR/tailwind.config.js" ]; then
            cp "$CONFIG_DIR/tailwind.config.js" ./
            log_info "Copied tailwind.config.js"
        fi
        
        if [ -f "$CONFIG_DIR/tsconfig.json" ]; then
            cp "$CONFIG_DIR/tsconfig.json" ./
            log_info "Copied tsconfig.json"
        fi
        
        if [ -f "$CONFIG_DIR/.env.example" ]; then
            if [ ! -f ".env.local" ]; then
                cp "$CONFIG_DIR/.env.example" .env.local
                log_warning "Copied .env.example to .env.local. Please update with your actual values."
            fi
        fi
    fi
    
    log_success "Environment setup completed"
}

validate_environment_variables() {
    log_step "Validating environment variables..."
    
    # Required environment variables for production
    REQUIRED_VARS=(
        "NEXT_PUBLIC_FIREBASE_API_KEY"
        "NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN"
        "NEXT_PUBLIC_FIREBASE_PROJECT_ID"
        "NEXT_PUBLIC_SUPABASE_URL"
        "NEXT_PUBLIC_SUPABASE_ANON_KEY"
        "NEXT_PUBLIC_API_BASE_URL"
    )
    
    missing_vars=()
    
    # Check if .env.local exists
    if [ -f ".env.local" ]; then
        source .env.local
    fi
    
    # Check environment variables
    for var in "${REQUIRED_VARS[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        log_error "Missing required environment variables:"
        for var in "${missing_vars[@]}"; do
            log_error "  - $var"
        done
        log_error "Please check your .env.local file or Vercel environment variables"
        
        if [ "$ENVIRONMENT" = "production" ]; then
            exit 1
        else
            log_warning "Continuing with missing variables for $ENVIRONMENT environment"
        fi
    fi
    
    log_success "Environment variables validation completed"
}

install_dependencies() {
    log_step "Installing dependencies..."
    
    # Check if node_modules exists and is up to date
    if [ -f "package-lock.json" ] && [ -d "node_modules" ]; then
        # Check if package-lock.json is newer than node_modules
        if [ "package-lock.json" -nt "node_modules" ]; then
            log_info "package-lock.json is newer, reinstalling dependencies..."
            rm -rf node_modules
            npm ci
        else
            log_info "Dependencies are up to date, skipping installation"
        fi
    else
        log_info "Installing fresh dependencies..."
        npm ci
    fi
    
    # Install or update Next.js if needed
    if ! npm list next &> /dev/null; then
        log_info "Installing Next.js..."
        npm install next@latest react@latest react-dom@latest
    fi
    
    # Check for peer dependency issues
    npm ls &> /dev/null || {
        log_warning "Peer dependency issues detected. Attempting to fix..."
        npm install --legacy-peer-deps
    }
    
    log_success "Dependencies installation completed"
}

run_type_checking() {
    log_step "Running TypeScript type checking..."
    
    # Check if TypeScript is configured
    if [ -f "tsconfig.json" ]; then
        # Run TypeScript compiler check
        if npm run type-check &> /dev/null || npx tsc --noEmit; then
            log_success "TypeScript type checking passed"
        else
            log_error "TypeScript type checking failed"
            if [ "$ENVIRONMENT" = "production" ]; then
                exit 1
            else
                log_warning "Continuing despite TypeScript errors for $ENVIRONMENT environment"
            fi
        fi
    else
        log_warning "TypeScript configuration not found, skipping type checking"
    fi
}

run_linting() {
    log_step "Running ESLint..."
    
    # Check if ESLint is configured
    if [ -f ".eslintrc.js" ] || [ -f ".eslintrc.json" ] || [ -f "eslint.config.js" ]; then
        if npm run lint &> /dev/null || npx eslint . --ext .ts,.tsx,.js,.jsx; then
            log_success "ESLint passed"
        else
            log_warning "ESLint issues found"
            if [ "$ENVIRONMENT" = "production" ]; then
                log_error "ESLint errors in production build"
                exit 1
            fi
        fi
    else
        log_warning "ESLint configuration not found, skipping linting"
    fi
}

run_tests() {
    if [ "$SKIP_TESTS" = "true" ]; then
        log_warning "Skipping tests (SKIP_TESTS=true)"
        return
    fi
    
    log_step "Running tests..."
    
    # Check if tests exist
    if [ -d "src/__tests__" ] || [ -d "__tests__" ] || ls src/**/*.test.* &> /dev/null; then
        if npm test -- --coverage --watchAll=false; then
            log_success "All tests passed"
        else
            log_error "Tests failed"
            if [ "$ENVIRONMENT" = "production" ]; then
                exit 1
            else
                log_warning "Continuing despite test failures for $ENVIRONMENT environment"
            fi
        fi
    else
        log_info "No tests found, skipping test execution"
    fi
}

build_application() {
    if [ "$SKIP_BUILD" = "true" ]; then
        log_warning "Skipping build (SKIP_BUILD=true)"
        return
    fi
    
    log_step "Building application..."
    
    # Set build environment
    export NODE_ENV="production"
    export NEXT_PUBLIC_BUILD_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # Get git commit hash if available
    if command -v git &> /dev/null && git rev-parse --git-dir &> /dev/null; then
        export NEXT_PUBLIC_GIT_COMMIT_SHA=$(git rev-parse HEAD | cut -c1-8)
    fi
    
    # Clean previous build
    if [ -d ".next" ]; then
        rm -rf .next
    fi
    
    # Build the application
    if npm run build; then
        log_success "Build completed successfully"
    else
        log_error "Build failed"
        exit 1
    fi
    
    # Verify build output
    if [ ! -d ".next" ]; then
        log_error "Build output directory not found"
        exit 1
    fi
    
    # Check build size
    BUILD_SIZE=$(du -sh .next | cut -f1)
    log_info "Build size: $BUILD_SIZE"
}

check_vercel_auth() {
    log_step "Checking Vercel authentication..."
    
    # Check if user is logged in to Vercel
    if ! vercel whoami &> /dev/null; then
        log_info "Not logged in to Vercel. Please authenticate..."
        vercel login
    fi
    
    # Verify authentication
    VERCEL_USER=$(vercel whoami 2>/dev/null)
    if [ -n "$VERCEL_USER" ]; then
        log_success "Authenticated as: $VERCEL_USER"
    else
        log_error "Vercel authentication failed"
        exit 1
    fi
}

setup_vercel_project() {
    log_step "Setting up Vercel project..."
    
    # Check if vercel.json exists
    if [ ! -f "vercel.json" ]; then
        log_info "Creating vercel.json configuration..."
        cat > vercel.json << EOF
{
  "version": 2,
  "name": "veslint-frontend",
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "outputDirectory": ".next",
  "installCommand": "npm ci",
  "devCommand": "npm run dev",
  "env": {
    "NODE_ENV": "production"
  },
  "build": {
    "env": {
      "NEXT_PUBLIC_BUILD_TIME": "@now"
    }
  },
  "functions": {
    "app/**": {
      "maxDuration": 30
    }
  },
  "regions": ["iad1"],
  "rewrites": [
    {
      "source": "/api/:path*",
      "destination": "$NEXT_PUBLIC_API_BASE_URL/api/v1/:path*"
    }
  ],
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "Referrer-Policy",
          "value": "strict-origin-when-cross-origin"
        }
      ]
    }
  ]
}
EOF
        log_success "Created vercel.json"
    fi
    
    # Link to Vercel project if not already linked
    if [ ! -f ".vercel/project.json" ]; then
        log_info "Linking to Vercel project..."
        if [ -n "$VERCEL_ORG" ]; then
            vercel link --scope="$VERCEL_ORG" --project="$VERCEL_PROJECT" --yes
        else
            vercel link --project="$VERCEL_PROJECT" --yes
        fi
    fi
}

deploy_to_vercel() {
    log_step "Deploying to Vercel..."
    
    # Prepare deployment command
    DEPLOY_CMD="vercel"
    
    # Add environment-specific flags
    if [ "$ENVIRONMENT" = "production" ]; then
        DEPLOY_CMD="$DEPLOY_CMD --prod"
    elif [ "$ENVIRONMENT" = "staging" ]; then
        DEPLOY_CMD="$DEPLOY_CMD --target preview"
    fi
    
    # Add force flag if requested
    if [ "$FORCE_DEPLOY" = "true" ]; then
        DEPLOY_CMD="$DEPLOY_CMD --force"
    fi
    
    # Add organization and project if specified
    if [ -n "$VERCEL_ORG" ]; then
        DEPLOY_CMD="$DEPLOY_CMD --scope=$VERCEL_ORG"
    fi
    
    # Execute deployment
    log_deploy "Executing: $DEPLOY_CMD"
    
    if DEPLOYMENT_URL=$($DEPLOY_CMD --yes 2>&1 | tail -n 1); then
        log_success "Deployment completed successfully"
        log_success "Deployment URL: $DEPLOYMENT_URL"
        
        # Store deployment info
        echo "$DEPLOYMENT_URL" > .vercel/last-deployment-url
        echo "$(date -u +"%Y-%m-%dT%H:%M:%SZ")" > .vercel/last-deployment-time
        
        return 0
    else
        log_error "Deployment failed"
        return 1
    fi
}

run_post_deployment_checks() {
    log_step "Running post-deployment checks..."
    
    # Get deployment URL
    if [ -f ".vercel/last-deployment-url" ]; then
        DEPLOYMENT_URL=$(cat .vercel/last-deployment-url)
        
        # Wait a moment for deployment to be ready
        sleep 10
        
        # Test deployment URL
        log_info "Testing deployment at: $DEPLOYMENT_URL"
        
        if curl -f -s "$DEPLOYMENT_URL" > /dev/null; then
            log_success "Deployment is accessible"
        else
            log_warning "Deployment may not be ready yet or there's an issue"
        fi
        
        # Test API connectivity
        if [ -n "$NEXT_PUBLIC_API_BASE_URL" ]; then
            API_HEALTH_URL="$NEXT_PUBLIC_API_BASE_URL/health"
            log_info "Testing API connectivity: $API_HEALTH_URL"
            
            if curl -f -s "$API_HEALTH_URL" > /dev/null; then
                log_success "Backend API is accessible"
            else
                log_warning "Backend API may not be accessible"
            fi
        fi
    fi
}

cleanup() {
    log_step "Cleaning up..."
    
    # Remove temporary files
    if [ -f "npm-debug.log" ]; then
        rm npm-debug.log
    fi
    
    # Clean npm cache if needed
    if [ "$ENVIRONMENT" = "production" ]; then
        npm cache clean --force
    fi
    
    log_success "Cleanup completed"
}

show_deployment_summary() {
    log_step "Deployment Summary"
    
    echo "=============================================="
    echo "🚀 VESLINT Frontend Deployment Complete"
    echo "=============================================="
    echo "Environment: $ENVIRONMENT"
    echo "Timestamp: $(date)"
    
    if [ -f ".vercel/last-deployment-url" ]; then
        echo "Deployment URL: $(cat .vercel/last-deployment-url)"
    fi
    
    if [ -f ".vercel/last-deployment-time" ]; then
        echo "Deployment Time: $(cat .vercel/last-deployment-time)"
    fi
    
    echo ""
    echo "Next Steps:"
    echo "1. Test the deployment thoroughly"
    echo "2. Update DNS records if using custom domain"
    echo "3. Monitor performance and errors"
    echo "4. Update team members about the deployment"
    echo "=============================================="
}

# =============================================================================
# Main Deployment Flow
# =============================================================================

main() {
    echo "=============================================="
    echo "🚢 VESLINT Frontend Deployment Script"
    echo "=============================================="
    echo "Environment: $ENVIRONMENT"
    echo "Skip Tests: $SKIP_TESTS"
    echo "Skip Build: $SKIP_BUILD"
    echo "Force Deploy: $FORCE_DEPLOY"
    echo "=============================================="
    
    # Execute deployment steps
    check_prerequisites
    setup_environment
    validate_environment_variables
    install_dependencies
    run_type_checking
    run_linting
    run_tests
    build_application
    check_vercel_auth
    setup_vercel_project
    
    if deploy_to_vercel; then
        run_post_deployment_checks
        cleanup
        show_deployment_summary
        exit 0
    else
        log_error "Deployment failed"
        cleanup
        exit 1
    fi
}

# =============================================================================
# Script Execution
# =============================================================================

# Handle script arguments
case "$1" in
    "help"|"-h"|"--help")
        echo "VESLINT Frontend Deployment Script"
        echo ""
        echo "Usage: $0 [environment] [skip_tests] [skip_build] [force_deploy]"
        echo ""
        echo "Arguments:"
        echo "  environment   - Deployment environment (development|staging|production) [default: production]"
        echo "  skip_tests    - Skip running tests (true|false) [default: false]"
        echo "  skip_build    - Skip building application (true|false) [default: false]"
        echo "  force_deploy  - Force deployment even if no changes (true|false) [default: false]"
        echo ""
        echo "Examples:"
        echo "  $0                           # Deploy to production"
        echo "  $0 staging                   # Deploy to staging"
        echo "  $0 production false false    # Deploy to production with tests and build"
        echo "  $0 staging true true true    # Quick staging deploy (skip tests, build, force)"
        echo ""
        echo "Environment Variables:"
        echo "  VERCEL_ORG      - Vercel organization/team name"
        echo "  VERCEL_PROJECT  - Vercel project name [default: veslint-frontend]"
        exit 0
        ;;
    *)
        main
        ;;
esac
