"""
Utilities Initialization for VESLINT Backend.

This module provides centralized access to all utility functions and classes
used throughout the VESLINT maritime vessel classification system.
"""

import os
import sys
import time
import uuid
import hashlib
import hmac
import secrets
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union, Callable, TypeVar
from functools import wraps
import asyncio

# Import constants first (no dependencies)
from .constants import *

# Import logging utilities
from .logging import (
    get_logger,
    setup_logging,
    set_request_context,
    clear_request_context,
    log_function_call,
    log_performance,
    log_api_request,
    log_job_event,
    log_ml_event,
    TimerContext,
    timer,
    JSONFormatter,
    ColoredFormatter,
    PerformanceFilter,
    HealthCheckFilter
)

# Import authentication utilities
from .auth import (
    verify_firebase_token,
    get_current_user,
    get_current_user_optional,
    verify_admin_user,
    get_user_info,
    set_custom_claims,
    revoke_user_tokens,
    create_custom_token
)

# Import validation utilities
from .validation import (
    validate_email,
    validate_mmsi,
    validate_coordinates,
    validate_vessel_data,
    validate_job_data,
    validate_file_upload,
    validate_csv_structure,
    sanitize_input,
    ValidationError,
    ValidationRule,
    DataValidator
)

# Initialize logger
logger = get_logger(__name__)

# Type definitions for utility functions
T = TypeVar('T')
F = TypeVar('F', bound=Callable[..., Any])

# Utility registry for dynamic access
UTILITY_REGISTRY: Dict[str, Dict[str, Any]] = {
    "crypto": {},
    "text": {},
    "data": {},
    "time": {},
    "security": {},
    "performance": {},
    "validation": {},
    "conversion": {}
}


class UtilityManager:
    """
    Centralized utility manager for VESLINT backend utilities.
    
    Provides organized access to utility functions and manages
    cross-cutting concerns like performance monitoring and error handling.
    """
    
    def __init__(self):
        self.start_time = datetime.utcnow()
        self.request_count = 0
        self.error_count = 0
        self.performance_metrics: Dict[str, List[float]] = {}
        
    def register_utility(self, category: str, name: str, func: Callable) -> None:
        """Register a utility function in the registry."""
        if category not in UTILITY_REGISTRY:
            UTILITY_REGISTRY[category] = {}
        UTILITY_REGISTRY[category][name] = {
            "function": func,
            "doc": func.__doc__,
            "module": func.__module__,
            "registered_at": datetime.utcnow()
        }
        
    def get_utility(self, category: str, name: str) -> Optional[Callable]:
        """Get a utility function from the registry."""
        return UTILITY_REGISTRY.get(category, {}).get(name, {}).get("function")
    
    def list_utilities(self, category: Optional[str] = None) -> Dict[str, Any]:
        """List all registered utilities, optionally filtered by category."""
        if category:
            return UTILITY_REGISTRY.get(category, {})
        return UTILITY_REGISTRY
    
    def track_performance(self, operation: str, duration_ms: float) -> None:
        """Track performance metrics for operations."""
        if operation not in self.performance_metrics:
            self.performance_metrics[operation] = []
        self.performance_metrics[operation].append(duration_ms)
        
        # Keep only last 100 measurements per operation
        if len(self.performance_metrics[operation]) > 100:
            self.performance_metrics[operation] = self.performance_metrics[operation][-100:]
    
    def get_performance_stats(self, operation: Optional[str] = None) -> Dict[str, Any]:
        """Get performance statistics for operations."""
        if operation:
            measurements = self.performance_metrics.get(operation, [])
            if not measurements:
                return {"error": f"No measurements found for operation: {operation}"}
            
            return {
                "operation": operation,
                "count": len(measurements),
                "avg_ms": sum(measurements) / len(measurements),
                "min_ms": min(measurements),
                "max_ms": max(measurements),
                "last_ms": measurements[-1] if measurements else None
            }
        
        return {
            "total_operations": len(self.performance_metrics),
            "operations": {
                op: {
                    "count": len(measurements),
                    "avg_ms": sum(measurements) / len(measurements)
                }
                for op, measurements in self.performance_metrics.items()
            }
        }


# Global utility manager instance
utility_manager = UtilityManager()


# Cryptographic utilities
def generate_secure_token(length: int = 32) -> str:
    """
    Generate a cryptographically secure random token.
    
    Args:
        length: Length of the token in bytes
        
    Returns:
        Hex-encoded secure token
    """
    return secrets.token_hex(length)


def generate_uuid() -> str:
    """Generate a UUID4 string."""
    return str(uuid.uuid4())


def hash_password(password: str, salt: Optional[str] = None) -> tuple[str, str]:
    """
    Hash a password using PBKDF2 with SHA-256.
    
    Args:
        password: Password to hash
        salt: Optional salt (generated if not provided)
        
    Returns:
        Tuple of (hashed_password, salt)
    """
    if salt is None:
        salt = secrets.token_hex(16)
    
    password_hash = hashlib.pbkdf2_hmac(
        'sha256',
        password.encode('utf-8'),
        salt.encode('utf-8'),
        100000  # iterations
    )
    
    return password_hash.hex(), salt


def verify_password(password: str, hashed_password: str, salt: str) -> bool:
    """
    Verify a password against its hash.
    
    Args:
        password: Password to verify
        hashed_password: Expected hash
        salt: Salt used for hashing
        
    Returns:
        True if password matches
    """
    computed_hash, _ = hash_password(password, salt)
    return hmac.compare_digest(computed_hash, hashed_password)


def generate_api_key(prefix: str = "vsl") -> str:
    """
    Generate an API key with prefix.
    
    Args:
        prefix: Prefix for the API key
        
    Returns:
        API key string
    """
    token = secrets.token_urlsafe(32)
    return f"{prefix}_{token}"


# Text utilities
def slugify(text: str) -> str:
    """
    Convert text to a URL-friendly slug.
    
    Args:
        text: Text to slugify
        
    Returns:
        Slugified text
    """
    import re
    text = text.lower()
    text = re.sub(r'[^a-z0-9\s-]', '', text)
    text = re.sub(r'[\s-]+', '-', text)
    return text.strip('-')


def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """
    Truncate text to maximum length.
    
    Args:
        text: Text to truncate
        max_length: Maximum length
        suffix: Suffix to add if truncated
        
    Returns:
        Truncated text
    """
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix


def extract_domain(email: str) -> Optional[str]:
    """
    Extract domain from email address.
    
    Args:
        email: Email address
        
    Returns:
        Domain or None if invalid
    """
    try:
        return email.split('@')[1].lower()
    except (IndexError, AttributeError):
        return None


def mask_sensitive_data(data: str, mask_char: str = "*", reveal_chars: int = 4) -> str:
    """
    Mask sensitive data leaving only last few characters visible.
    
    Args:
        data: Data to mask
        mask_char: Character to use for masking
        reveal_chars: Number of characters to reveal at end
        
    Returns:
        Masked data
    """
    if len(data) <= reveal_chars:
        return mask_char * len(data)
    
    masked_length = len(data) - reveal_chars
    return mask_char * masked_length + data[-reveal_chars:]


# Data utilities
def deep_merge(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
    """
    Deep merge two dictionaries.
    
    Args:
        dict1: First dictionary
        dict2: Second dictionary
        
    Returns:
        Merged dictionary
    """
    result = dict1.copy()
    
    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = deep_merge(result[key], value)
        else:
            result[key] = value
    
    return result


def flatten_dict(d: Dict[str, Any], parent_key: str = '', sep: str = '.') -> Dict[str, Any]:
    """
    Flatten a nested dictionary.
    
    Args:
        d: Dictionary to flatten
        parent_key: Parent key prefix
        sep: Separator for nested keys
        
    Returns:
        Flattened dictionary
    """
    items = []
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)


def chunk_list(lst: List[T], chunk_size: int) -> List[List[T]]:
    """
    Split a list into chunks of specified size.
    
    Args:
        lst: List to chunk
        chunk_size: Size of each chunk
        
    Returns:
        List of chunks
    """
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]


def remove_duplicates(lst: List[T], key: Optional[Callable[[T], Any]] = None) -> List[T]:
    """
    Remove duplicates from list while preserving order.
    
    Args:
        lst: List to deduplicate
        key: Optional key function for comparison
        
    Returns:
        List without duplicates
    """
    seen = set()
    result = []
    
    for item in lst:
        lookup_key = key(item) if key else item
        if lookup_key not in seen:
            seen.add(lookup_key)
            result.append(item)
    
    return result


# Time utilities
def get_utc_now() -> datetime:
    """Get current UTC datetime."""
    return datetime.now(timezone.utc)


def format_datetime(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S UTC") -> str:
    """
    Format datetime to string.
    
    Args:
        dt: Datetime to format
        format_str: Format string
        
    Returns:
        Formatted datetime string
    """
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)
    return dt.strftime(format_str)


def parse_datetime(dt_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> datetime:
    """
    Parse datetime from string.
    
    Args:
        dt_str: Datetime string
        format_str: Format string
        
    Returns:
        Parsed datetime
    """
    return datetime.strptime(dt_str, format_str).replace(tzinfo=timezone.utc)


def time_ago(dt: datetime) -> str:
    """
    Get human-readable time difference.
    
    Args:
        dt: Datetime to compare
        
    Returns:
        Human-readable time difference
    """
    now = get_utc_now()
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)
    
    diff = now - dt
    
    if diff.days > 0:
        return f"{diff.days} day{'s' if diff.days != 1 else ''} ago"
    elif diff.seconds > 3600:
        hours = diff.seconds // 3600
        return f"{hours} hour{'s' if hours != 1 else ''} ago"
    elif diff.seconds > 60:
        minutes = diff.seconds // 60
        return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
    else:
        return "just now"


# Security utilities
def is_safe_path(path: str, base_path: str = ".") -> bool:
    """
    Check if a file path is safe (no directory traversal).
    
    Args:
        path: Path to check
        base_path: Base path to restrict to
        
    Returns:
        True if path is safe
    """
    try:
        resolved_path = os.path.realpath(os.path.join(base_path, path))
        resolved_base = os.path.realpath(base_path)
        return resolved_path.startswith(resolved_base)
    except (ValueError, OSError):
        return False


def sanitize_filename(filename: str) -> str:
    """
    Sanitize a filename for safe storage.
    
    Args:
        filename: Filename to sanitize
        
    Returns:
        Sanitized filename
    """
    import re
    # Remove dangerous characters
    filename = re.sub(r'[<>:"/\\|?*]', '', filename)
    # Replace spaces with underscores
    filename = filename.replace(' ', '_')
    # Limit length
    name, ext = os.path.splitext(filename)
    if len(name) > 100:
        name = name[:100]
    return f"{name}{ext}"


# Performance utilities
def monitored(operation_name: str):
    """
    Decorator to monitor function performance.
    
    Args:
        operation_name: Name of the operation for tracking
    """
    def decorator(func: F) -> F:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.perf_counter()
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                duration_ms = (time.perf_counter() - start_time) * 1000
                utility_manager.track_performance(operation_name, duration_ms)
                log_performance(operation_name, duration_ms)
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.perf_counter()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration_ms = (time.perf_counter() - start_time) * 1000
                utility_manager.track_performance(operation_name, duration_ms)
                log_performance(operation_name, duration_ms)
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def rate_limit(calls: int, period: int):
    """
    Decorator for rate limiting function calls.
    
    Args:
        calls: Number of calls allowed
        period: Time period in seconds
    """
    call_times = []
    
    def decorator(func: F) -> F:
        @wraps(func)
        def wrapper(*args, **kwargs):
            now = time.time()
            # Remove old calls outside the period
            call_times[:] = [t for t in call_times if now - t < period]
            
            if len(call_times) >= calls:
                raise Exception(f"Rate limit exceeded: {calls} calls per {period} seconds")
            
            call_times.append(now)
            return func(*args, **kwargs)
        
        return wrapper
    return decorator


# Conversion utilities
def bytes_to_human(bytes_size: int) -> str:
    """
    Convert bytes to human-readable format.
    
    Args:
        bytes_size: Size in bytes
        
    Returns:
        Human-readable size string
    """
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_size < 1024.0:
            return f"{bytes_size:.1f} {unit}"
        bytes_size /= 1024.0
    return f"{bytes_size:.1f} PB"


def human_to_bytes(size_str: str) -> int:
    """
    Convert human-readable size to bytes.
    
    Args:
        size_str: Size string (e.g., "1.5 MB")
        
    Returns:
        Size in bytes
    """
    import re
    match = re.match(r'^(\d+(?:\.\d+)?)\s*([KMGT]?B)$', size_str.upper())
    if not match:
        raise ValueError(f"Invalid size format: {size_str}")
    
    size, unit = match.groups()
    size = float(size)
    
    multipliers = {'B': 1, 'KB': 1024, 'MB': 1024**2, 'GB': 1024**3, 'TB': 1024**4}
    return int(size * multipliers[unit])


# Register utilities in the registry
def register_all_utilities():
    """Register all utility functions in the utility registry."""
    
    # Crypto utilities
    utility_manager.register_utility("crypto", "generate_secure_token", generate_secure_token)
    utility_manager.register_utility("crypto", "generate_uuid", generate_uuid)
    utility_manager.register_utility("crypto", "hash_password", hash_password)
    utility_manager.register_utility("crypto", "verify_password", verify_password)
    utility_manager.register_utility("crypto", "generate_api_key", generate_api_key)
    
    # Text utilities
    utility_manager.register_utility("text", "slugify", slugify)
    utility_manager.register_utility("text", "truncate_text", truncate_text)
    utility_manager.register_utility("text", "extract_domain", extract_domain)
    utility_manager.register_utility("text", "mask_sensitive_data", mask_sensitive_data)
    
    # Data utilities
    utility_manager.register_utility("data", "deep_merge", deep_merge)
    utility_manager.register_utility("data", "flatten_dict", flatten_dict)
    utility_manager.register_utility("data", "chunk_list", chunk_list)
    utility_manager.register_utility("data", "remove_duplicates", remove_duplicates)
    
    # Time utilities
    utility_manager.register_utility("time", "get_utc_now", get_utc_now)
    utility_manager.register_utility("time", "format_datetime", format_datetime)
    utility_manager.register_utility("time", "parse_datetime", parse_datetime)
    utility_manager.register_utility("time", "time_ago", time_ago)
    
    # Security utilities
    utility_manager.register_utility("security", "is_safe_path", is_safe_path)
    utility_manager.register_utility("security", "sanitize_filename", sanitize_filename)
    
    # Conversion utilities
    utility_manager.register_utility("conversion", "bytes_to_human", bytes_to_human)
    utility_manager.register_utility("conversion", "human_to_bytes", human_to_bytes)


# Initialize utilities
register_all_utilities()

# Log successful initialization
logger.info(f"Utilities initialized: {sum(len(cat) for cat in UTILITY_REGISTRY.values())} functions in {len(UTILITY_REGISTRY)} categories")

# Export all utilities and components
__all__ = [
    # Constants (from constants.py)
    "API_VERSION", "SERVICE_NAME", "ENVIRONMENT", "LOG_LEVEL", "ML_MODEL_PATH",
    "SUPABASE_URL", "SUPABASE_ANON_KEY", "FIREBASE_PROJECT_ID",
    "MAX_FILE_SIZE_MB", "SUPPORTED_FILE_TYPES", "VESSEL_CLASSES",
    
    # Logging utilities
    "get_logger", "setup_logging", "set_request_context", "clear_request_context",
    "log_function_call", "log_performance", "log_api_request", "log_job_event", "log_ml_event",
    "TimerContext", "timer", "JSONFormatter", "ColoredFormatter", "PerformanceFilter", "HealthCheckFilter",
    
    # Authentication utilities
    "verify_firebase_token", "get_current_user", "require_authentication", "extract_user_claims",
    "validate_token_signature", "AuthenticationError", "TokenValidationError", "UserContext",
    
    # Validation utilities
    "validate_email", "validate_mmsi", "validate_coordinates", "validate_vessel_data",
    "validate_job_data", "validate_file_upload", "validate_csv_structure", "sanitize_input",
    "ValidationError", "ValidationRule", "DataValidator",
    
    # Crypto utilities
    "generate_secure_token", "generate_uuid", "hash_password", "verify_password", "generate_api_key",
    
    # Text utilities
    "slugify", "truncate_text", "extract_domain", "mask_sensitive_data",
    
    # Data utilities
    "deep_merge", "flatten_dict", "chunk_list", "remove_duplicates",
    
    # Time utilities
    "get_utc_now", "format_datetime", "parse_datetime", "time_ago",
    
    # Security utilities
    "is_safe_path", "sanitize_filename",
    
    # Performance utilities
    "monitored", "rate_limit",
    
    # Conversion utilities
    "bytes_to_human", "human_to_bytes",
    
    # Utility management
    "utility_manager", "UtilityManager", "UTILITY_REGISTRY"
]
