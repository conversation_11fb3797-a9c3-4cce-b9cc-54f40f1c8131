#!/bin/bash

# =============================================================================
# VESLINT Backend Deployment Script for Render
# =============================================================================
# This script deploys the VESLINT FastAPI backend to Render.com
# Supports free tier deployment with optimizations and monitoring

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKEND_DIR="$PROJECT_ROOT/backend"
SERVICE_NAME="veslint-api"
RENDER_REGION="oregon"  # Default to Oregon for free tier
DEPLOY_BRANCH="main"
HEALTH_CHECK_TIMEOUT=300  # 5 minutes

# =============================================================================
# Utility Functions
# =============================================================================

print_header() {
    echo -e "\n${PURPLE}================================================${NC}"
    echo -e "${WHITE}$1${NC}"
    echo -e "${PURPLE}================================================${NC}\n"
}

print_step() {
    echo -e "${CYAN}▶ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

check_command() {
    if command -v "$1" &> /dev/null; then
        return 0
    else
        return 1
    fi
}

wait_for_deployment() {
    local service_url="$1"
    local timeout="$2"
    local check_interval=10
    local elapsed=0
    
    print_step "Waiting for deployment to become healthy..."
    
    while [ $elapsed -lt $timeout ]; do
        if curl -s -f "$service_url/health" >/dev/null 2>&1; then
            print_success "Deployment is healthy!"
            return 0
        fi
        
        echo -n "."
        sleep $check_interval
        elapsed=$((elapsed + check_interval))
    done
    
    print_error "Deployment health check timed out"
    return 1
}

# =============================================================================
# Prerequisites Check
# =============================================================================

check_prerequisites() {
    print_header "Checking Prerequisites"
    
    print_step "Checking required tools"
    
    local missing_tools=()
    
    if ! check_command "git"; then
        missing_tools+=("git")
    fi
    
    if ! check_command "curl"; then
        missing_tools+=("curl")
    fi
    
    if ! check_command "jq"; then
        print_warning "jq not found (optional) - install for better JSON parsing"
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        print_error "Missing required tools: ${missing_tools[*]}"
        print_info "Please install missing tools and try again"
        exit 1
    fi
    
    print_step "Checking Git repository status"
    cd "$PROJECT_ROOT"
    
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        print_error "Not in a Git repository"
        exit 1
    fi
    
    # Check if we're on the correct branch
    current_branch=$(git branch --show-current)
    if [ "$current_branch" != "$DEPLOY_BRANCH" ]; then
        print_warning "Currently on branch '$current_branch', not '$DEPLOY_BRANCH'"
        read -p "Continue with current branch? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "Please switch to '$DEPLOY_BRANCH' branch and try again"
            exit 1
        fi
        DEPLOY_BRANCH="$current_branch"
    fi
    
    # Check for uncommitted changes
    if ! git diff-index --quiet HEAD --; then
        print_warning "You have uncommitted changes"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "Please commit your changes and try again"
            exit 1
        fi
    fi
    
    print_step "Checking backend directory"
    if [ ! -d "$BACKEND_DIR" ]; then
        print_error "Backend directory not found: $BACKEND_DIR"
        exit 1
    fi
    
    print_step "Checking required backend files"
    local required_files=(
        "$BACKEND_DIR/requirements.txt"
        "$BACKEND_DIR/src/main.py"
        "$BACKEND_DIR/Dockerfile"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            print_error "Required file not found: $file"
            exit 1
        fi
    done
    
    print_success "Prerequisites check passed"
}

# =============================================================================
# Environment Configuration
# =============================================================================

setup_environment() {
    print_header "Setting Up Environment Configuration"
    
    print_step "Checking environment variables"
    
    # Required environment variables for deployment
    local required_vars=(
        "SUPABASE_URL"
        "SUPABASE_ANON_KEY"
        "SUPABASE_SERVICE_ROLE_KEY"
    )
    
    # Optional environment variables
    local optional_vars=(
        "FIREBASE_PROJECT_ID"
        "HUGGINGFACE_API_TOKEN"
        "RENDER_API_KEY"
        "SENTRY_DSN"
    )
    
    print_step "Checking required environment variables"
    local missing_required=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_required+=("$var")
        else
            print_success "$var is set"
        fi
    done
    
    if [ ${#missing_required[@]} -ne 0 ]; then
        print_error "Missing required environment variables: ${missing_required[*]}"
        print_info "Please set these variables and try again:"
        for var in "${missing_required[@]}"; do
            echo "  export $var=your_value_here"
        done
        exit 1
    fi
    
    print_step "Checking optional environment variables"
    for var in "${optional_vars[@]}"; do
        if [ -z "${!var}" ]; then
            print_warning "$var is not set (optional)"
        else
            print_success "$var is set"
        fi
    done
    
    # Create deployment environment file
    print_step "Creating deployment environment configuration"
    cat > "$BACKEND_DIR/.env.deploy" << EOF
# VESLINT Backend Deployment Environment
# Generated on $(date)

# Database Configuration
SUPABASE_URL=$SUPABASE_URL
SUPABASE_ANON_KEY=$SUPABASE_ANON_KEY
SUPABASE_SERVICE_ROLE_KEY=$SUPABASE_SERVICE_ROLE_KEY

# Application Configuration
ENVIRONMENT=production
LOG_LEVEL=INFO
API_VERSION=v1
CORS_ORIGINS=https://veslint.com,https://www.veslint.com

# Performance Configuration
MAX_WORKERS=4
WORKER_TIMEOUT=120
KEEP_ALIVE=2
MAX_REQUESTS=1000
MAX_REQUESTS_JITTER=50

# Security Configuration
TRUSTED_HOSTS=veslint-api.onrender.com
SECURE_COOKIES=true
SESSION_TIMEOUT=3600

# ML Model Configuration
MODEL_CACHE_SIZE=100
FEATURE_CACHE_TTL=300
PREDICTION_TIMEOUT=30

# Optional Services
EOF

    # Add optional variables if they exist
    if [ -n "$FIREBASE_PROJECT_ID" ]; then
        echo "FIREBASE_PROJECT_ID=$FIREBASE_PROJECT_ID" >> "$BACKEND_DIR/.env.deploy"
    fi
    
    if [ -n "$HUGGINGFACE_API_TOKEN" ]; then
        echo "HUGGINGFACE_API_TOKEN=$HUGGINGFACE_API_TOKEN" >> "$BACKEND_DIR/.env.deploy"
    fi
    
    if [ -n "$SENTRY_DSN" ]; then
        echo "SENTRY_DSN=$SENTRY_DSN" >> "$BACKEND_DIR/.env.deploy"
    fi
    
    print_success "Environment configuration created"
}

# =============================================================================
# Pre-deployment Tests
# =============================================================================

run_predeploy_tests() {
    print_header "Running Pre-deployment Tests"
    
    cd "$BACKEND_DIR"
    
    print_step "Creating test virtual environment"
    if [ ! -d "test_venv" ]; then
        python3 -m venv test_venv
    fi
    
    source test_venv/bin/activate || source test_venv/Scripts/activate
    
    print_step "Installing dependencies"
    pip install -q --upgrade pip
    pip install -q -r requirements.txt
    
    print_step "Running syntax checks"
    python -m py_compile src/main.py
    print_success "Syntax check passed"
    
    print_step "Running import tests"
    python -c "
import sys
sys.path.insert(0, 'src')
try:
    from main import app
    print('✅ Main application imports successfully')
except Exception as e:
    print(f'❌ Import error: {e}')
    sys.exit(1)
"
    
    print_step "Testing environment configuration"
    python -c "
import os
from src.utils.constants import *
print('✅ Constants module loads successfully')
"
    
    print_step "Running unit tests (if available)"
    if [ -d "tests" ]; then
        python -m pytest tests/ -v --tb=short || {
            print_warning "Some tests failed, but continuing with deployment"
        }
    else
        print_info "No test directory found"
    fi
    
    # Cleanup test environment
    deactivate 2>/dev/null || true
    rm -rf test_venv
    
    print_success "Pre-deployment tests completed"
}

# =============================================================================
# Deployment Configuration
# =============================================================================

create_render_config() {
    print_header "Creating Render Deployment Configuration"
    
    cd "$BACKEND_DIR"
    
    print_step "Generating render.yaml"
    cat > render.yaml << EOF
# Render.com Deployment Configuration for VESLINT Backend
# Auto-generated on $(date)

services:
  - type: web
    name: $SERVICE_NAME
    runtime: docker
    plan: free  # FREE TIER
    region: $RENDER_REGION
    
    # Repository Configuration
    repo: https://github.com/YOUR_USERNAME/VESLINT  # Update this!
    branch: $DEPLOY_BRANCH
    rootDir: backend
    
    # Build Configuration
    dockerfilePath: ./Dockerfile
    dockerContext: .
    
    # Runtime Configuration
    healthCheckPath: /health
    
    # Environment Variables
    envVars:
      - key: ENVIRONMENT
        value: production
      - key: LOG_LEVEL
        value: INFO
      - key: SUPABASE_URL
        sync: false  # Set in Render dashboard
      - key: SUPABASE_ANON_KEY
        sync: false  # Set in Render dashboard  
      - key: SUPABASE_SERVICE_ROLE_KEY
        sync: false  # Set in Render dashboard
      - key: CORS_ORIGINS
        value: https://veslint.com,https://www.veslint.com
      - key: MAX_WORKERS
        value: 4
      - key: WORKER_TIMEOUT
        value: 120
      - key: KEEP_ALIVE
        value: 2
      - key: MAX_REQUESTS
        value: 1000
      - key: MAX_REQUESTS_JITTER
        value: 50
      - key: TRUSTED_HOSTS
        value: $SERVICE_NAME.onrender.com
      - key: SECURE_COOKIES
        value: true
      - key: SESSION_TIMEOUT
        value: 3600
      - key: MODEL_CACHE_SIZE
        value: 100
      - key: FEATURE_CACHE_TTL
        value: 300
      - key: PREDICTION_TIMEOUT
        value: 30

    # Scaling (Free tier limits)
    scaling:
      minInstances: 0  # Scale to zero when idle
      maxInstances: 1  # Free tier limit
    
    # Resource Limits (Free tier)
    disk:
      name: $SERVICE_NAME-disk
      size: 1GB
      
    # Health Check Configuration
    healthCheck:
      path: /health
      intervalSeconds: 30
      timeoutSeconds: 10
      unhealthyThresholdCount: 3
      healthyThresholdCount: 2
EOF

    print_success "Render configuration created"
    
    print_step "Creating deployment script"
    cat > deploy.sh << 'EOF'
#!/bin/bash
# Render deployment script for VESLINT Backend

set -e

echo "🚀 Starting VESLINT Backend deployment..."

# Install dependencies
echo "📦 Installing dependencies..."
pip install --no-cache-dir -r requirements.txt

# Run any necessary setup
echo "⚙️  Running setup..."
python -c "print('✅ Python environment ready')"

# Start the application
echo "🎯 Starting application..."
exec python -m gunicorn src.main:app \
    --worker-class uvicorn.workers.UvicornWorker \
    --workers ${MAX_WORKERS:-4} \
    --timeout ${WORKER_TIMEOUT:-120} \
    --keep-alive ${KEEP_ALIVE:-2} \
    --max-requests ${MAX_REQUESTS:-1000} \
    --max-requests-jitter ${MAX_REQUESTS_JITTER:-50} \
    --bind 0.0.0.0:${PORT:-8000} \
    --access-logfile - \
    --error-logfile - \
    --log-level ${LOG_LEVEL:-info}
EOF
    chmod +x deploy.sh
    
    print_success "Deployment script created"
}

# =============================================================================
# Git Operations
# =============================================================================

prepare_git_deployment() {
    print_header "Preparing Git Repository for Deployment"
    
    cd "$PROJECT_ROOT"
    
    print_step "Adding deployment files to Git"
    git add "$BACKEND_DIR/render.yaml"
    git add "$BACKEND_DIR/deploy.sh"
    
    # Don't add .env.deploy to git for security
    if ! grep -q ".env.deploy" .gitignore 2>/dev/null; then
        echo ".env.deploy" >> .gitignore
        git add .gitignore
        print_success "Updated .gitignore"
    fi
    
    print_step "Committing deployment configuration"
    if ! git diff --cached --quiet; then
        git commit -m "Add Render deployment configuration

- Add render.yaml for Render.com deployment
- Add deploy.sh startup script
- Update .gitignore for deployment files

Generated by deploy_backend.sh on $(date)"
        
        print_success "Deployment configuration committed"
    else
        print_info "No changes to commit"
    fi
    
    print_step "Pushing to remote repository"
    git push origin "$DEPLOY_BRANCH"
    print_success "Changes pushed to remote"
}

# =============================================================================
# Render Deployment
# =============================================================================

deploy_to_render() {
    print_header "Deploying to Render"
    
    print_step "Deployment options:"
    echo "1. Manual deployment (copy render.yaml to Render dashboard)"
    echo "2. Automatic deployment (requires Render CLI - coming soon)"
    echo ""
    
    print_info "For now, please follow these manual steps:"
    echo ""
    echo -e "${WHITE}1. Go to https://dashboard.render.com${NC}"
    echo -e "${WHITE}2. Click 'New +' → 'Web Service'${NC}"
    echo -e "${WHITE}3. Connect your GitHub repository${NC}"
    echo -e "${WHITE}4. Use these settings:${NC}"
    echo "   - Name: $SERVICE_NAME"
    echo "   - Runtime: Docker"
    echo "   - Region: $RENDER_REGION"
    echo "   - Branch: $DEPLOY_BRANCH"
    echo "   - Root Directory: backend"
    echo "   - Dockerfile Path: ./Dockerfile"
    echo ""
    echo -e "${WHITE}5. Set Environment Variables:${NC}"
    echo "   - SUPABASE_URL: $SUPABASE_URL"
    echo "   - SUPABASE_ANON_KEY: [Your Supabase Anon Key]"
    echo "   - SUPABASE_SERVICE_ROLE_KEY: [Your Supabase Service Role Key]"
    
    if [ -n "$FIREBASE_PROJECT_ID" ]; then
        echo "   - FIREBASE_PROJECT_ID: $FIREBASE_PROJECT_ID"
    fi
    
    if [ -n "$HUGGINGFACE_API_TOKEN" ]; then
        echo "   - HUGGINGFACE_API_TOKEN: [Your HuggingFace Token]"
    fi
    
    echo ""
    echo -e "${WHITE}6. Click 'Create Web Service'${NC}"
    echo ""
    
    read -p "Press Enter when deployment is started in Render dashboard..."
    
    print_step "Enter your Render service URL (e.g., https://veslint-api.onrender.com):"
    read -r service_url
    
    if [ -n "$service_url" ]; then
        print_step "Waiting for deployment to complete..."
        if wait_for_deployment "$service_url" $HEALTH_CHECK_TIMEOUT; then
            print_success "Deployment completed successfully!"
            
            print_step "Testing deployment"
            test_deployment "$service_url"
        else
            print_error "Deployment health check failed"
            print_info "Check Render logs at: https://dashboard.render.com"
            exit 1
        fi
    else
        print_warning "Service URL not provided, skipping health check"
    fi
}

# =============================================================================
# Deployment Testing
# =============================================================================

test_deployment() {
    local service_url="$1"
    print_header "Testing Deployment"
    
    print_step "Testing health endpoint"
    if response=$(curl -s -f "$service_url/health"); then
        print_success "Health check passed"
        echo "Response: $response"
    else
        print_error "Health check failed"
        return 1
    fi
    
    print_step "Testing API documentation"
    if curl -s -f "$service_url/docs" >/dev/null; then
        print_success "API documentation accessible"
    else
        print_warning "API documentation not accessible"
    fi
    
    print_step "Testing CORS configuration"
    if curl -s -H "Origin: https://veslint.com" -f "$service_url/health" >/dev/null; then
        print_success "CORS configuration working"
    else
        print_warning "CORS configuration may need adjustment"
    fi
    
    print_success "Deployment testing completed"
    
    print_info "Your VESLINT backend is deployed at: $service_url"
    print_info "API Documentation: $service_url/docs"
    print_info "Health Check: $service_url/health"
}

# =============================================================================
# Post-deployment Setup
# =============================================================================

post_deployment_setup() {
    print_header "Post-deployment Setup"
    
    print_step "Deployment Summary"
    echo -e "${WHITE}✅ Backend deployed to Render${NC}"
    echo -e "${WHITE}✅ Health checks passing${NC}"
    echo -e "${WHITE}✅ API documentation available${NC}"
    echo ""
    
    print_step "Next Steps:"
    echo "1. Update your frontend environment variables:"
    echo "   NEXT_PUBLIC_API_URL=https://your-service.onrender.com"
    echo ""
    echo "2. Configure your custom domain (optional):"
    echo "   - Add CNAME: api.veslint.com → your-service.onrender.com"
    echo "   - Update CORS origins in Render dashboard"
    echo ""
    echo "3. Set up monitoring:"
    echo "   - Enable Render monitoring alerts"
    echo "   - Configure error tracking (Sentry)"
    echo ""
    echo "4. Performance optimization:"
    echo "   - Monitor resource usage"
    echo "   - Adjust worker configuration if needed"
    echo ""
    
    print_success "Backend deployment completed! 🎉"
}

# =============================================================================
# Main Execution
# =============================================================================

main() {
    print_header "VESLINT Backend Deployment to Render"
    
    echo -e "${BLUE}This script will deploy your VESLINT backend to Render.com${NC}"
    echo -e "${BLUE}using the free tier with optimized configuration.${NC}"
    echo ""
    
    # Confirmation
    read -p "Continue with deployment? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Deployment cancelled"
        exit 0
    fi
    
    # Run deployment steps
    check_prerequisites
    setup_environment
    run_predeploy_tests
    create_render_config
    prepare_git_deployment
    deploy_to_render
    post_deployment_setup
    
    print_success "VESLINT backend deployment completed successfully! 🚀"
}

# Handle script arguments
case "${1:-}" in
    "--help"|"-h")
        echo "VESLINT Backend Deployment Script"
        echo "Usage: $0 [--help]"
        echo ""
        echo "This script deploys the VESLINT FastAPI backend to Render.com"
        echo "using the free tier with optimized configuration."
        echo ""
        echo "Required environment variables:"
        echo "  SUPABASE_URL"
        echo "  SUPABASE_ANON_KEY"
        echo "  SUPABASE_SERVICE_ROLE_KEY"
        echo ""
        echo "Optional environment variables:"
        echo "  FIREBASE_PROJECT_ID"
        echo "  HUGGINGFACE_API_TOKEN"
        echo "  RENDER_API_KEY"
        echo "  SENTRY_DSN"
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac