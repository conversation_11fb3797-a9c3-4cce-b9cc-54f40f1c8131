"""
API Routes Initialization for VESLINT Backend.

This module centralizes all API route registration and provides a clean
interface for mounting routes to the FastAPI application.
"""

from fastapi import APIRouter, Depends
from fastapi.responses import JSONResponse
from typing import List

from .jobs import router as jobs_router
from .vessels import router as vessels_router
from .health import router as health_router
from ..middleware import get_current_user, RateLimitMiddleware
from ...models.response import APIResponse, HealthCheckResponse
from ...utils.constants import API_VERSION, SERVICE_NAME
from ...utils.logging import get_logger

logger = get_logger(__name__)

# Create the main API router
api_router = APIRouter()

# API metadata
API_METADATA = {
    "title": "VESLINT Maritime Vessel Classification API",
    "description": "Professional maritime vessel classification using AI/ML",
    "version": API_VERSION,
    "service": SERVICE_NAME,
    "docs_url": "/docs",
    "redoc_url": "/redoc",
    "openapi_url": "/openapi.json"
}

# Route groups configuration
ROUTE_GROUPS = [
    {
        "router": health_router,
        "prefix": "/health",
        "tags": ["Health Check"],
        "include_in_schema": True,
        "dependencies": []  # Health endpoints don't require auth
    },
    {
        "router": jobs_router,
        "prefix": "/jobs",
        "tags": ["Job Management"],
        "include_in_schema": True,
        "dependencies": [Depends(get_current_user)]  # Requires authentication
    },
    {
        "router": vessels_router,
        "prefix": "/vessels",
        "tags": ["Vessel Classification"],
        "include_in_schema": True,
        "dependencies": [Depends(get_current_user)]  # Requires authentication
    }
]

# API root endpoint
@api_router.get(
    "/",
    response_model=APIResponse[dict],
    summary="API Root",
    description="Get API information and available endpoints",
    tags=["Root"],
    include_in_schema=True
)
async def api_root() -> APIResponse[dict]:
    """
    API root endpoint providing service information.
    
    Returns basic information about the VESLINT API including
    version, available endpoints, and service status.
    """
    logger.info("API root endpoint accessed")
    
    available_endpoints = [
        {
            "path": "/",
            "method": "GET",
            "description": "API root information"
        },
        {
            "path": "/health",
            "method": "GET",
            "description": "Health check endpoint"
        },
        {
            "path": "/health/ready",
            "method": "GET",
            "description": "Readiness probe for container orchestration"
        },
        {
            "path": "/health/live",
            "method": "GET",
            "description": "Liveness probe for container orchestration"
        },
        {
            "path": "/jobs",
            "method": "GET",
            "description": "List user's classification jobs"
        },
        {
            "path": "/jobs",
            "method": "POST",
            "description": "Create new classification job"
        },
        {
            "path": "/jobs/{job_id}",
            "method": "GET",
            "description": "Get specific job details"
        },
        {
            "path": "/jobs/{job_id}/results",
            "method": "GET",
            "description": "Get job classification results"
        },
        {
            "path": "/vessels/classify",
            "method": "POST",
            "description": "Classify individual vessel data"
        },
        {
            "path": "/vessels/upload",
            "method": "POST",
            "description": "Upload CSV file for batch classification"
        }
    ]
    
    api_info = {
        "service": API_METADATA["service"],
        "title": API_METADATA["title"],
        "description": API_METADATA["description"],
        "version": API_METADATA["version"],
        "documentation": {
            "swagger_ui": API_METADATA["docs_url"],
            "redoc": API_METADATA["redoc_url"],
            "openapi_spec": API_METADATA["openapi_url"]
        },
        "endpoints": available_endpoints,
        "authentication": {
            "type": "Firebase JWT",
            "header": "Authorization",
            "format": "Bearer <token>"
        },
        "features": [
            "Maritime vessel classification using AI/ML",
            "Batch processing of AIS data",
            "Real-time job monitoring",
            "99+ features engineering",
            "Support for TUG, FISHING, PLEASURE, CARGO vessel types"
        ],
        "limits": {
            "max_file_size_mb": 50,
            "max_vessels_per_job": 10000,
            "rate_limit": "100 requests per minute per user"
        }
    }
    
    return APIResponse(
        success=True,
        message="VESLINT API is running",
        data=api_info
    )


# API status endpoint
@api_router.get(
    "/status",
    response_model=APIResponse[dict],
    summary="API Status",
    description="Get detailed API status and health information",
    tags=["Root"],
    include_in_schema=True
)
async def api_status() -> APIResponse[dict]:
    """
    Get comprehensive API status information.
    
    Provides detailed status information including service health,
    dependencies, and operational metrics.
    """
    logger.info("API status endpoint accessed")
    
    try:
        # Import here to avoid circular imports
        from ...services.ml_service import get_model_info
        from ...database.supabase_client import test_connection
        
        # Test ML service
        try:
            model_info = await get_model_info()
            ml_status = "healthy"
            ml_details = model_info
        except Exception as e:
            logger.warning(f"ML service check failed: {e}")
            ml_status = "degraded"
            ml_details = {"error": str(e)}
        
        # Test database connection
        try:
            db_status = await test_connection()
            db_health = "healthy" if db_status else "unhealthy"
        except Exception as e:
            logger.warning(f"Database check failed: {e}")
            db_health = "unhealthy"
            db_status = {"error": str(e)}
        
        # Overall status determination
        overall_status = "healthy"
        if ml_status == "degraded" and db_health == "unhealthy":
            overall_status = "unhealthy"
        elif ml_status == "degraded" or db_health == "unhealthy":
            overall_status = "degraded"
        
        status_info = {
            "overall_status": overall_status,
            "service": API_METADATA["service"],
            "version": API_METADATA["version"],
            "components": {
                "api": "healthy",
                "database": db_health,
                "ml_service": ml_status,
                "authentication": "healthy"  # Assume healthy if we got this far
            },
            "details": {
                "database": db_status if isinstance(db_status, dict) else {"status": "connected"},
                "ml_service": ml_details
            },
            "uptime_info": {
                "status": "Service uptime tracking not implemented",
                "note": "Consider implementing with process start time tracking"
            }
        }
        
        return APIResponse(
            success=overall_status != "unhealthy",
            message=f"API status: {overall_status}",
            data=status_info
        )
        
    except Exception as e:
        logger.error(f"Status check failed: {e}")
        return APIResponse(
            success=False,
            message="Status check failed",
            data={"error": str(e)}
        )


def register_routes(app) -> None:
    """
    Register all API routes with the FastAPI application.
    
    Args:
        app: FastAPI application instance
    """
    logger.info("Registering API routes...")
    
    # Register route groups
    for route_group in ROUTE_GROUPS:
        try:
            app.include_router(
                route_group["router"],
                prefix=f"/api/v1{route_group['prefix']}",
                tags=route_group["tags"],
                dependencies=route_group.get("dependencies", []),
                include_in_schema=route_group.get("include_in_schema", True)
            )
            logger.info(f"Registered route group: {route_group['prefix']}")
        except Exception as e:
            logger.error(f"Failed to register route group {route_group['prefix']}: {e}")
            raise
    
    # Register the main API router
    app.include_router(
        api_router,
        prefix="/api/v1",
        tags=["Root"]
    )
    
    logger.success("All API routes registered successfully")


def get_route_list() -> List[dict]:
    """
    Get a list of all registered routes.
    
    Returns:
        List of route information dictionaries
    """
    routes = []
    
    # Add routes from each router
    for route_group in ROUTE_GROUPS:
        router = route_group["router"]
        prefix = f"/api/v1{route_group['prefix']}"
        
        for route in router.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                routes.append({
                    "path": f"{prefix}{route.path}",
                    "methods": list(route.methods),
                    "name": getattr(route, 'name', 'Unknown'),
                    "tags": route_group["tags"],
                    "requires_auth": bool(route_group.get("dependencies"))
                })
    
    # Add root routes
    for route in api_router.routes:
        if hasattr(route, 'path') and hasattr(route, 'methods'):
            routes.append({
                "path": f"/api/v1{route.path}",
                "methods": list(route.methods),
                "name": getattr(route, 'name', 'Unknown'),
                "tags": ["Root"],
                "requires_auth": False
            })
    
    return sorted(routes, key=lambda x: x["path"])


def get_protected_routes() -> List[str]:
    """
    Get a list of routes that require authentication.
    
    Returns:
        List of protected route paths
    """
    protected = []
    
    for route_group in ROUTE_GROUPS:
        if route_group.get("dependencies"):
            prefix = f"/api/v1{route_group['prefix']}"
            for route in route_group["router"].routes:
                if hasattr(route, 'path'):
                    protected.append(f"{prefix}{route.path}")
    
    return protected


def validate_route_configuration() -> bool:
    """
    Validate that all routes are properly configured.
    
    Returns:
        True if configuration is valid, False otherwise
    """
    try:
        # Check that all routers are importable
        for route_group in ROUTE_GROUPS:
            router = route_group["router"]
            if not hasattr(router, 'routes'):
                logger.error(f"Invalid router in route group: {route_group['prefix']}")
                return False
        
        # Check for route conflicts
        all_paths = []
        for route_group in ROUTE_GROUPS:
            prefix = route_group["prefix"]
            for route in route_group["router"].routes:
                if hasattr(route, 'path'):
                    full_path = f"{prefix}{route.path}"
                    if full_path in all_paths:
                        logger.error(f"Duplicate route path: {full_path}")
                        return False
                    all_paths.append(full_path)
        
        logger.info("Route configuration validation passed")
        return True
        
    except Exception as e:
        logger.error(f"Route configuration validation failed: {e}")
        return False


# Export key components
__all__ = [
    "api_router",
    "register_routes",
    "get_route_list",
    "get_protected_routes",
    "validate_route_configuration",
    "API_METADATA",
    "ROUTE_GROUPS"
]
