-- =============================================================================
-- VESLINT Row Level Security (RLS) Policies
-- =============================================================================
-- This migration sets up comprehensive Row Level Security policies for the
-- VESLINT maritime vessel classification system. These policies ensure that:
-- 1. Users can only access their own data
-- 2. Authentication is properly validated
-- 3. System operations are secure
-- 4. Performance is optimized through proper indexing
-- =============================================================================

-- Enable Row Level Security on all tables
-- =============================================================================

-- Enable RLS on users table
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Enable RLS on jobs table
ALTER TABLE jobs ENABLE ROW LEVEL SECURITY;

-- Enable RLS on vessels table
ALTER TABLE vessels ENABLE ROW LEVEL SECURITY;

-- Enable RLS on vessel_classifications table
ALTER TABLE vessel_classifications ENABLE ROW LEVEL SECURITY;

-- Enable RLS on job_results table (if exists)
ALTER TABLE job_results ENABLE ROW LEVEL SECURITY;

-- Helper Functions for Authentication
-- =============================================================================

-- Function to get the current user ID from JWT token
-- This assumes Firebase Auth JWT is validated by Supabase
CREATE OR REPLACE FUNCTION auth.uid() RETURNS UUID
LANGUAGE SQL STABLE
AS $$
  SELECT COALESCE(
    nullif(current_setting('request.jwt.claim.sub', true), ''),
    (nullif(current_setting('request.jwt.claim.user_id', true), ''))
  )::UUID;
$$;

-- Function to check if user is authenticated
CREATE OR REPLACE FUNCTION auth.authenticated() RETURNS BOOLEAN
LANGUAGE SQL STABLE
AS $$
  SELECT auth.uid() IS NOT NULL;
$$;

-- Function to check if user has admin role (for future use)
CREATE OR REPLACE FUNCTION auth.has_admin_role() RETURNS BOOLEAN
LANGUAGE SQL STABLE
AS $$
  SELECT COALESCE(
    (current_setting('request.jwt.claim.role', true))::TEXT = 'admin',
    false
  );
$$;

-- Users Table Policies
-- =============================================================================

-- Policy: Users can view their own profile
CREATE POLICY "users_select_own" ON users
  FOR SELECT
  USING (auth.uid() = id);

-- Policy: Users can insert their own profile (registration)
CREATE POLICY "users_insert_own" ON users
  FOR INSERT
  WITH CHECK (auth.uid() = id);

-- Policy: Users can update their own profile
CREATE POLICY "users_update_own" ON users
  FOR UPDATE
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Policy: Prevent users from deleting their profiles (optional)
-- Uncomment if you want to allow profile deletion
-- CREATE POLICY "users_delete_own" ON users
--   FOR DELETE
--   USING (auth.uid() = id);

-- Jobs Table Policies
-- =============================================================================

-- Policy: Users can view their own jobs
CREATE POLICY "jobs_select_own" ON jobs
  FOR SELECT
  USING (auth.uid() = user_id);

-- Policy: Users can create jobs (must set their own user_id)
CREATE POLICY "jobs_insert_own" ON jobs
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Policy: Users can update their own jobs
CREATE POLICY "jobs_update_own" ON jobs
  FOR UPDATE
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Policy: Users can delete their own jobs
CREATE POLICY "jobs_delete_own" ON jobs
  FOR DELETE
  USING (auth.uid() = user_id);

-- Policy: System can update job status (for backend processes)
-- This allows the backend service to update job status without user context
CREATE POLICY "jobs_system_update" ON jobs
  FOR UPDATE
  USING (
    -- Allow updates when called from service role or system context
    current_setting('role') = 'service_role' OR
    current_setting('request.jwt.claim.role', true) = 'system'
  );

-- Vessels Table Policies
-- =============================================================================

-- Policy: Users can view vessels from their own jobs
CREATE POLICY "vessels_select_own" ON vessels
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM jobs 
      WHERE jobs.id = vessels.job_id 
      AND jobs.user_id = auth.uid()
    )
  );

-- Policy: Users can insert vessels for their own jobs
CREATE POLICY "vessels_insert_own" ON vessels
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM jobs 
      WHERE jobs.id = vessels.job_id 
      AND jobs.user_id = auth.uid()
    )
  );

-- Policy: System can insert vessels (for batch processing)
CREATE POLICY "vessels_system_insert" ON vessels
  FOR INSERT
  WITH CHECK (
    current_setting('role') = 'service_role' OR
    current_setting('request.jwt.claim.role', true) = 'system'
  );

-- Policy: Users can update vessels from their own jobs
CREATE POLICY "vessels_update_own" ON vessels
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM jobs 
      WHERE jobs.id = vessels.job_id 
      AND jobs.user_id = auth.uid()
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM jobs 
      WHERE jobs.id = vessels.job_id 
      AND jobs.user_id = auth.uid()
    )
  );

-- Policy: Users can delete vessels from their own jobs
CREATE POLICY "vessels_delete_own" ON vessels
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM jobs 
      WHERE jobs.id = vessels.job_id 
      AND jobs.user_id = auth.uid()
    )
  );

-- Vessel Classifications Table Policies
-- =============================================================================

-- Policy: Users can view classifications for their own vessels
CREATE POLICY "vessel_classifications_select_own" ON vessel_classifications
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM vessels v
      JOIN jobs j ON j.id = v.job_id
      WHERE v.id = vessel_classifications.vessel_id 
      AND j.user_id = auth.uid()
    )
  );

-- Policy: System can insert classifications
CREATE POLICY "vessel_classifications_system_insert" ON vessel_classifications
  FOR INSERT
  WITH CHECK (
    current_setting('role') = 'service_role' OR
    current_setting('request.jwt.claim.role', true) = 'system'
  );

-- Policy: Users can update classifications for their own vessels
CREATE POLICY "vessel_classifications_update_own" ON vessel_classifications
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM vessels v
      JOIN jobs j ON j.id = v.job_id
      WHERE v.id = vessel_classifications.vessel_id 
      AND j.user_id = auth.uid()
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM vessels v
      JOIN jobs j ON j.id = v.job_id
      WHERE v.id = vessel_classifications.vessel_id 
      AND j.user_id = auth.uid()
    )
  );

-- Job Results Table Policies (if exists)
-- =============================================================================

-- Policy: Users can view results for their own jobs
CREATE POLICY "job_results_select_own" ON job_results
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM jobs 
      WHERE jobs.id = job_results.job_id 
      AND jobs.user_id = auth.uid()
    )
  );

-- Policy: System can insert job results
CREATE POLICY "job_results_system_insert" ON job_results
  FOR INSERT
  WITH CHECK (
    current_setting('role') = 'service_role' OR
    current_setting('request.jwt.claim.role', true) = 'system'
  );

-- Performance Optimization Indexes
-- =============================================================================

-- Index for user-specific job queries
CREATE INDEX IF NOT EXISTS idx_jobs_user_id_created_at 
ON jobs(user_id, created_at DESC);

-- Index for job-vessel relationships
CREATE INDEX IF NOT EXISTS idx_vessels_job_id 
ON vessels(job_id);

-- Index for vessel classification lookups
CREATE INDEX IF NOT EXISTS idx_vessel_classifications_vessel_id 
ON vessel_classifications(vessel_id);

-- Index for job results lookups
CREATE INDEX IF NOT EXISTS idx_job_results_job_id 
ON job_results(job_id);

-- Index for user authentication
CREATE INDEX IF NOT EXISTS idx_users_email 
ON users(email);

-- Composite index for vessel queries with job context
CREATE INDEX IF NOT EXISTS idx_vessels_job_user_composite 
ON vessels(job_id, created_at DESC);

-- Storage Bucket Policies (Supabase Storage)
-- =============================================================================

-- Policy: Users can upload files to their own folder
INSERT INTO storage.policies (id, bucket_id, name, definition, check_expression, command) 
VALUES (
  'users_upload_own_files',
  'csv-uploads',
  'Users can upload to own folder',
  'auth.uid()::text = (storage.foldername(name))[1]',
  'auth.uid()::text = (storage.foldername(name))[1]',
  'INSERT'
) ON CONFLICT (id) DO NOTHING;

-- Policy: Users can view their own uploaded files
INSERT INTO storage.policies (id, bucket_id, name, definition, check_expression, command) 
VALUES (
  'users_view_own_files',
  'csv-uploads',
  'Users can view own files',
  'auth.uid()::text = (storage.foldername(name))[1]',
  null,
  'SELECT'
) ON CONFLICT (id) DO NOTHING;

-- Policy: Users can delete their own uploaded files
INSERT INTO storage.policies (id, bucket_id, name, definition, check_expression, command) 
VALUES (
  'users_delete_own_files',
  'csv-uploads',
  'Users can delete own files',
  'auth.uid()::text = (storage.foldername(name))[1]',
  null,
  'DELETE'
) ON CONFLICT (id) DO NOTHING;

-- Service Role Policies for Backend Operations
-- =============================================================================

-- Grant service role ability to bypass RLS for system operations
-- This is needed for background job processing

-- Create a service role policy for jobs
CREATE POLICY "service_role_jobs_all" ON jobs
  FOR ALL
  USING (current_setting('role') = 'service_role')
  WITH CHECK (current_setting('role') = 'service_role');

-- Create a service role policy for vessels
CREATE POLICY "service_role_vessels_all" ON vessels
  FOR ALL
  USING (current_setting('role') = 'service_role')
  WITH CHECK (current_setting('role') = 'service_role');

-- Create a service role policy for vessel classifications
CREATE POLICY "service_role_vessel_classifications_all" ON vessel_classifications
  FOR ALL
  USING (current_setting('role') = 'service_role')
  WITH CHECK (current_setting('role') = 'service_role');

-- Create a service role policy for job results
CREATE POLICY "service_role_job_results_all" ON job_results
  FOR ALL
  USING (current_setting('role') = 'service_role')
  WITH CHECK (current_setting('role') = 'service_role');

-- Security Functions for Additional Validation
-- =============================================================================

-- Function to validate job ownership
CREATE OR REPLACE FUNCTION validate_job_ownership(job_uuid UUID)
RETURNS BOOLEAN
LANGUAGE SQL STABLE SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1 FROM jobs 
    WHERE id = job_uuid 
    AND user_id = auth.uid()
  );
$$;

-- Function to check if user can access vessel
CREATE OR REPLACE FUNCTION can_access_vessel(vessel_uuid UUID)
RETURNS BOOLEAN
LANGUAGE SQL STABLE SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1 FROM vessels v
    JOIN jobs j ON j.id = v.job_id
    WHERE v.id = vessel_uuid 
    AND j.user_id = auth.uid()
  );
$$;

-- Real-time Subscriptions Security
-- =============================================================================

-- Enable real-time for authenticated users on their own data
ALTER TABLE jobs REPLICA IDENTITY FULL;
ALTER TABLE vessels REPLICA IDENTITY FULL;
ALTER TABLE vessel_classifications REPLICA IDENTITY FULL;
ALTER TABLE job_results REPLICA IDENTITY FULL;

-- Create publication for real-time (if not exists)
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_publication WHERE pubname = 'supabase_realtime') THEN
    CREATE PUBLICATION supabase_realtime;
  END IF;
END$$;

-- Add tables to real-time publication
ALTER PUBLICATION supabase_realtime ADD TABLE jobs;
ALTER PUBLICATION supabase_realtime ADD TABLE vessels;
ALTER PUBLICATION supabase_realtime ADD TABLE vessel_classifications;
ALTER PUBLICATION supabase_realtime ADD TABLE job_results;

-- Audit and Logging Setup
-- =============================================================================

-- Create audit log table for security monitoring
CREATE TABLE IF NOT EXISTS audit_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  table_name TEXT NOT NULL,
  operation TEXT NOT NULL,
  old_data JSONB,
  new_data JSONB,
  user_id UUID,
  timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS on audit log
ALTER TABLE audit_log ENABLE ROW LEVEL SECURITY;

-- Audit log policies - only admins can view
CREATE POLICY "audit_admin_only" ON audit_log
  FOR ALL
  USING (auth.has_admin_role())
  WITH CHECK (auth.has_admin_role());

-- Create audit trigger function
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  INSERT INTO audit_log (table_name, operation, old_data, new_data, user_id)
  VALUES (
    TG_TABLE_NAME,
    TG_OP,
    CASE WHEN TG_OP = 'DELETE' THEN to_jsonb(OLD) ELSE NULL END,
    CASE WHEN TG_OP IN ('INSERT', 'UPDATE') THEN to_jsonb(NEW) ELSE NULL END,
    auth.uid()
  );
  
  RETURN CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END;
END;
$$;

-- Create audit triggers for sensitive operations
CREATE TRIGGER jobs_audit_trigger
  AFTER INSERT OR UPDATE OR DELETE ON jobs
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

-- Comments for Documentation
-- =============================================================================

COMMENT ON TABLE users IS 'User profiles and authentication data';
COMMENT ON TABLE jobs IS 'Vessel classification jobs with RLS protection';
COMMENT ON TABLE vessels IS 'Vessel data linked to user jobs';
COMMENT ON TABLE vessel_classifications IS 'ML classification results';
COMMENT ON TABLE job_results IS 'Aggregated job processing results';
COMMENT ON TABLE audit_log IS 'Security audit trail for sensitive operations';

-- Verify RLS is enabled
-- =============================================================================

-- Query to verify RLS is enabled on all tables
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('users', 'jobs', 'vessels', 'vessel_classifications', 'job_results')
ORDER BY tablename;

-- =============================================================================
-- End of RLS Policies Migration
-- =============================================================================

-- Success message
SELECT 'Row Level Security policies successfully applied to VESLINT database' as status;
