# Render.com Deployment Configuration for VESLINT Backend
# Optimized for FREE tier deployment

services:
  - type: web
    name: veslint-api
    runtime: python3
    plan: free  # FREE TIER - 750 hours/month
    
    # Build configuration
    buildCommand: |
      pip install --upgrade pip
      pip install -r requirements.txt
    
    # Start command optimized for Render
    startCommand: |
      gunicorn src.main:app -w 1 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:$PORT --timeout 120 --keep-alive 5 --max-requests 1000 --max-requests-jitter 100
    
    # Health check
    healthCheckPath: /health
    
    # Auto-deploy from main branch
    autoDeploy: true
    branch: main
    
    # Root directory (if backend is in subdirectory)
    rootDir: backend
    
    # Environment variables (set these in Render dashboard)
    envVars:
      # Application settings
      - key: ENVIRONMENT
        value: production
      
      - key: PYTHONPATH
        value: ./src
      
      - key: PORT
        value: 10000  # Render assigns this automatically
      
      - key: LOG_LEVEL
        value: INFO
      
      # Supabase configuration (REQUIRED - set in dashboard)
      - key: SUPABASE_URL
        sync: false  # Set manually in Render dashboard
      
      - key: SUPABASE_SERVICE_KEY
        sync: false  # Set manually in Render dashboard
      
      - key: SUPABASE_ANON_KEY
        sync: false  # Set manually in Render dashboard
      
      # Firebase authentication (REQUIRED - set in dashboard)
      - key: FIREBASE_PROJECT_ID
        sync: false  # Set manually in Render dashboard
      
      - key: FIREBASE_CREDENTIALS_JSON
        sync: false  # Set manually in Render dashboard (base64 encoded)
      
      # Optional: Hugging Face integration
      - key: HUGGINGFACE_API_TOKEN
        sync: false  # Optional - for future HF integration
      
      # Performance optimization for free tier
      - key: PYTHONUNBUFFERED
        value: "1"
      
      - key: PYTHONDONTWRITEBYTECODE
        value: "1"
      
      # Memory optimization
      - key: MALLOC_ARENA_MAX
        value: "2"
      
      - key: MALLOC_MMAP_THRESHOLD_
        value: "131072"
    
    # Resource limits (free tier)
    scaling:
      minInstances: 0  # Can scale to 0 to save resources
      maxInstances: 1  # Free tier limit
    
    # Auto-sleep after 15 minutes of inactivity (free tier)
    # Will auto-wake on first request (cold start ~30 seconds)

# Additional services (if needed later)
# Redis for caching (optional)
# - type: redis
#   name: veslint-redis
#   plan: free  # 25MB Redis instance
#   maxmemoryPolicy: allkeys-lru

# Render deployment notes:
# 1. Free tier includes:
#    - 750 hours/month compute time
#    - 512MB RAM
#    - Shared CPU
#    - Auto-sleep after 15 min inactivity
#    - 15 second cold start time
# 
# 2. To deploy:
#    - Connect GitHub repo to Render
#    - Set environment variables in dashboard
#    - Deploy automatically on git push
#
# 3. Custom domain setup:
#    - Add CNAME record in Cloudflare: api.veslint.com -> your-service.onrender.com
#    - Configure custom domain in Render dashboard