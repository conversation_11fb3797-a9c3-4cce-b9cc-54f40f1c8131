#!/bin/bash

# =============================================================================
# VESLINT Development Environment Setup Script
# =============================================================================
# This script sets up a complete local development environment for VESLINT
# including backend, frontend, database, and all necessary dependencies.

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKEND_DIR="$PROJECT_ROOT/backend"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
ML_MODEL_DIR="$PROJECT_ROOT/ml-model"
DATABASE_DIR="$PROJECT_ROOT/database"

# =============================================================================
# Utility Functions
# =============================================================================

print_header() {
    echo -e "\n${PURPLE}================================================${NC}"
    echo -e "${WHITE}$1${NC}"
    echo -e "${PURPLE}================================================${NC}\n"
}

print_step() {
    echo -e "${CYAN}▶ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

check_command() {
    if command -v "$1" &> /dev/null; then
        print_success "$1 is installed"
        return 0
    else
        print_error "$1 is not installed"
        return 1
    fi
}

install_if_missing() {
    local cmd="$1"
    local install_instructions="$2"
    
    if ! command -v "$cmd" &> /dev/null; then
        print_warning "$cmd is not installed"
        echo -e "${YELLOW}To install $cmd:${NC}"
        echo -e "${WHITE}$install_instructions${NC}"
        echo ""
        read -p "Would you like to continue without $cmd? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_error "Please install $cmd and run this script again"
            exit 1
        fi
    else
        print_success "$cmd is installed"
    fi
}

create_env_file() {
    local template_file="$1"
    local target_file="$2"
    local description="$3"
    
    if [[ -f "$template_file" && ! -f "$target_file" ]]; then
        print_step "Creating $description from template"
        cp "$template_file" "$target_file"
        print_success "Created $target_file"
        print_warning "Please edit $target_file with your actual configuration values"
    elif [[ -f "$target_file" ]]; then
        print_info "$description already exists"
    else
        print_warning "Template file $template_file not found"
    fi
}

wait_for_service() {
    local service_name="$1"
    local host="$2"
    local port="$3"
    local max_attempts="${4:-30}"
    
    print_step "Waiting for $service_name to be ready..."
    
    for i in $(seq 1 $max_attempts); do
        if nc -z "$host" "$port" 2>/dev/null; then
            print_success "$service_name is ready!"
            return 0
        fi
        echo -n "."
        sleep 2
    done
    
    print_error "$service_name failed to start within expected time"
    return 1
}

# =============================================================================
# System Requirements Check
# =============================================================================

check_system_requirements() {
    print_header "Checking System Requirements"
    
    print_step "Checking operating system"
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        print_success "Linux detected"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        print_success "macOS detected"
    elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
        print_success "Windows detected"
    else
        print_warning "Unknown operating system: $OSTYPE"
    fi
    
    print_step "Checking required commands"
    
    # Essential tools
    install_if_missing "git" "Visit https://git-scm.com/downloads"
    install_if_missing "curl" "Install with your package manager (apt, brew, chocolatey)"
    install_if_missing "wget" "Install with your package manager (apt, brew, chocolatey)"
    
    # Docker (optional but recommended)
    install_if_missing "docker" "Visit https://docs.docker.com/get-docker/"
    if command -v docker &> /dev/null; then
        install_if_missing "docker-compose" "Visit https://docs.docker.com/compose/install/"
    fi
    
    # Node.js and npm
    install_if_missing "node" "Visit https://nodejs.org/ or use a version manager like nvm"
    install_if_missing "npm" "Usually comes with Node.js"
    
    # Python
    install_if_missing "python3" "Visit https://python.org/downloads/ or use pyenv"
    install_if_missing "pip3" "Usually comes with Python 3"
    
    # Optional development tools
    if command -v code &> /dev/null; then
        print_success "VS Code is installed"
    else
        print_info "VS Code not found (optional): Visit https://code.visualstudio.com/"
    fi
    
    print_success "System requirements check completed"
}

# =============================================================================
# Python Environment Setup
# =============================================================================

setup_python_environment() {
    print_header "Setting Up Python Environment"
    
    cd "$PROJECT_ROOT"
    
    print_step "Checking Python version"
    python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
    print_info "Python version: $python_version"
    
    # Check if Python version is 3.8+
    if python3 -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)"; then
        print_success "Python version is 3.8 or higher"
    else
        print_error "Python 3.8 or higher is required"
        exit 1
    fi
    
    print_step "Setting up Python virtual environment"
    if [[ ! -d "venv" ]]; then
        python3 -m venv venv
        print_success "Created virtual environment"
    else
        print_info "Virtual environment already exists"
    fi
    
    print_step "Activating virtual environment"
    source venv/bin/activate || source venv/Scripts/activate
    print_success "Virtual environment activated"
    
    print_step "Upgrading pip"
    pip install --upgrade pip
    
    print_step "Installing backend dependencies"
    if [[ -f "$BACKEND_DIR/requirements.txt" ]]; then
        pip install -r "$BACKEND_DIR/requirements.txt"
        print_success "Backend dependencies installed"
    else
        print_warning "Backend requirements.txt not found"
    fi
    
    print_step "Installing development dependencies"
    pip install pytest pytest-asyncio pytest-cov black flake8 mypy
    print_success "Development dependencies installed"
    
    print_step "Installing ML model dependencies"
    if [[ -f "$ML_MODEL_DIR/requirements.txt" ]]; then
        pip install -r "$ML_MODEL_DIR/requirements.txt"
    else
        pip install scikit-learn pandas numpy joblib huggingface_hub
    fi
    print_success "ML dependencies installed"
}

# =============================================================================
# Node.js Environment Setup
# =============================================================================

setup_nodejs_environment() {
    print_header "Setting Up Node.js Environment"
    
    print_step "Checking Node.js version"
    node_version=$(node --version)
    print_info "Node.js version: $node_version"
    
    # Check if Node.js version is 16+
    if node -e "process.exit(parseInt(process.version.slice(1)) >= 16 ? 0 : 1)"; then
        print_success "Node.js version is 16 or higher"
    else
        print_error "Node.js 16 or higher is required"
        exit 1
    fi
    
    print_step "Installing global npm packages"
    npm install -g yarn typescript @vercel/cli
    print_success "Global packages installed"
    
    print_step "Setting up frontend dependencies"
    if [[ -d "$FRONTEND_DIR" ]]; then
        cd "$FRONTEND_DIR"
        
        if [[ -f "package.json" ]]; then
            print_step "Installing frontend dependencies"
            npm install
            print_success "Frontend dependencies installed"
        else
            print_warning "Frontend package.json not found"
        fi
        
        cd "$PROJECT_ROOT"
    else
        print_warning "Frontend directory not found"
    fi
    
    print_step "Setting up workspace dependencies"
    if [[ -f "package.json" ]]; then
        npm install
        print_success "Workspace dependencies installed"
    fi
}

# =============================================================================
# Database Setup
# =============================================================================

setup_database() {
    print_header "Setting Up Database (Supabase Local)"
    
    print_step "Checking for Supabase CLI"
    if command -v supabase &> /dev/null; then
        print_success "Supabase CLI is installed"
    else
        print_step "Installing Supabase CLI"
        if command -v npm &> /dev/null; then
            npm install -g supabase
            print_success "Supabase CLI installed via npm"
        else
            print_error "Please install Supabase CLI manually: https://supabase.com/docs/guides/cli"
            return 1
        fi
    fi
    
    cd "$PROJECT_ROOT"
    
    print_step "Initializing Supabase project"
    if [[ ! -f "supabase/config.toml" ]]; then
        supabase init
        print_success "Supabase project initialized"
    else
        print_info "Supabase project already initialized"
    fi
    
    print_step "Starting Supabase local development"
    supabase start
    print_success "Supabase local development started"
    
    print_step "Running database migrations"
    if [[ -d "$DATABASE_DIR/migrations" ]]; then
        for migration in "$DATABASE_DIR/migrations"/*.sql; do
            if [[ -f "$migration" ]]; then
                print_step "Running migration: $(basename "$migration")"
                supabase db reset --debug
                break  # Reset will run all migrations
            fi
        done
        print_success "Database migrations completed"
    else
        print_warning "No migrations found"
    fi
    
    # Get Supabase connection details
    print_info "Supabase connection details:"
    supabase status
}

# =============================================================================
# Environment Configuration
# =============================================================================

setup_environment_files() {
    print_header "Setting Up Environment Configuration"
    
    cd "$PROJECT_ROOT"
    
    # Backend environment
    create_env_file "$BACKEND_DIR/.env.example" "$BACKEND_DIR/.env" "backend environment file"
    
    # Frontend environment
    create_env_file "$FRONTEND_DIR/.env.example" "$FRONTEND_DIR/.env.local" "frontend environment file"
    
    print_step "Setting up Supabase environment variables"
    if command -v supabase &> /dev/null; then
        # Get Supabase credentials
        supabase_url=$(supabase status | grep "API URL" | awk '{print $3}')
        supabase_anon_key=$(supabase status | grep "anon key" | awk '{print $3}')
        supabase_service_role_key=$(supabase status | grep "service_role key" | awk '{print $3}')
        
        if [[ -n "$supabase_url" && -n "$supabase_anon_key" ]]; then
            print_step "Updating backend .env with Supabase credentials"
            if [[ -f "$BACKEND_DIR/.env" ]]; then
                sed -i.bak "s|SUPABASE_URL=.*|SUPABASE_URL=$supabase_url|" "$BACKEND_DIR/.env"
                sed -i.bak "s|SUPABASE_ANON_KEY=.*|SUPABASE_ANON_KEY=$supabase_anon_key|" "$BACKEND_DIR/.env"
                sed -i.bak "s|SUPABASE_SERVICE_ROLE_KEY=.*|SUPABASE_SERVICE_ROLE_KEY=$supabase_service_role_key|" "$BACKEND_DIR/.env"
                rm -f "$BACKEND_DIR/.env.bak"
                print_success "Backend environment updated with Supabase credentials"
            fi
            
            print_step "Updating frontend .env with Supabase credentials"
            if [[ -f "$FRONTEND_DIR/.env.local" ]]; then
                sed -i.bak "s|NEXT_PUBLIC_SUPABASE_URL=.*|NEXT_PUBLIC_SUPABASE_URL=$supabase_url|" "$FRONTEND_DIR/.env.local"
                sed -i.bak "s|NEXT_PUBLIC_SUPABASE_ANON_KEY=.*|NEXT_PUBLIC_SUPABASE_ANON_KEY=$supabase_anon_key|" "$FRONTEND_DIR/.env.local"
                rm -f "$FRONTEND_DIR/.env.local.bak"
                print_success "Frontend environment updated with Supabase credentials"
            fi
        fi
    fi
    
    print_warning "Please review and update the environment files with your actual configuration:"
    echo -e "${WHITE}Backend: $BACKEND_DIR/.env${NC}"
    echo -e "${WHITE}Frontend: $FRONTEND_DIR/.env.local${NC}"
}

# =============================================================================
# Development Tools Setup
# =============================================================================

setup_development_tools() {
    print_header "Setting Up Development Tools"
    
    cd "$PROJECT_ROOT"
    
    print_step "Setting up Git hooks"
    if [[ -d ".git" ]]; then
        # Create pre-commit hook
        cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# VESLINT pre-commit hook
set -e

echo "Running pre-commit checks..."

# Check Python code formatting
if [[ -d "backend" ]]; then
    echo "Checking Python code formatting..."
    source venv/bin/activate 2>/dev/null || source venv/Scripts/activate 2>/dev/null || true
    black --check backend/src/ || {
        echo "Python code formatting issues found. Run: black backend/src/"
        exit 1
    }
fi

# Check TypeScript code
if [[ -d "frontend" ]]; then
    echo "Checking TypeScript code..."
    cd frontend
    npm run lint || {
        echo "TypeScript linting issues found. Run: npm run lint:fix"
        exit 1
    }
    cd ..
fi

echo "Pre-commit checks passed!"
EOF
        chmod +x .git/hooks/pre-commit
        print_success "Git pre-commit hook installed"
    fi
    
    print_step "Setting up VS Code workspace"
    if command -v code &> /dev/null; then
        # Create VS Code workspace settings
        mkdir -p .vscode
        
        cat > .vscode/settings.json << 'EOF'
{
    "python.pythonPath": "./venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "typescript.preferences.includePackageJsonAutoImports": "auto",
    "eslint.workingDirectories": ["frontend"],
    "files.exclude": {
        "**/__pycache__": true,
        "**/node_modules": true,
        "venv": true
    },
    "search.exclude": {
        "venv": true,
        "**/node_modules": true
    }
}
EOF
        
        cat > .vscode/extensions.json << 'EOF'
{
    "recommendations": [
        "ms-python.python",
        "ms-python.black-formatter",
        "ms-vscode.vscode-typescript-next",
        "bradlc.vscode-tailwindcss",
        "ms-vscode.vscode-json",
        "redhat.vscode-yaml",
        "ms-vscode.vscode-eslint"
    ]
}
EOF
        
        print_success "VS Code workspace configured"
    fi
    
    print_step "Creating development scripts"
    cat > dev.sh << 'EOF'
#!/bin/bash
# VESLINT development helper script

case "$1" in
    "backend")
        echo "Starting backend development server..."
        cd backend
        source ../venv/bin/activate || source ../venv/Scripts/activate
        uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
        ;;
    "frontend")
        echo "Starting frontend development server..."
        cd frontend
        npm run dev
        ;;
    "db")
        echo "Starting database..."
        supabase start
        ;;
    "test")
        echo "Running tests..."
        source venv/bin/activate || source venv/Scripts/activate
        pytest backend/tests/ -v
        cd frontend && npm test
        ;;
    "lint")
        echo "Running linters..."
        source venv/bin/activate || source venv/Scripts/activate
        black backend/src/
        cd frontend && npm run lint:fix
        ;;
    *)
        echo "VESLINT Development Helper"
        echo "Usage: ./dev.sh [command]"
        echo "Commands:"
        echo "  backend  - Start backend development server"
        echo "  frontend - Start frontend development server"
        echo "  db       - Start local database"
        echo "  test     - Run all tests"
        echo "  lint     - Run linters and formatters"
        ;;
esac
EOF
    chmod +x dev.sh
    print_success "Development helper script created"
}

# =============================================================================
# Health Check
# =============================================================================

run_health_check() {
    print_header "Running Health Check"
    
    cd "$PROJECT_ROOT"
    
    print_step "Checking Python environment"
    if source venv/bin/activate 2>/dev/null || source venv/Scripts/activate 2>/dev/null; then
        python -c "import fastapi, pandas, sklearn; print('✅ Python dependencies OK')"
    else
        print_error "Python environment check failed"
    fi
    
    print_step "Checking Node.js environment"
    if [[ -d "$FRONTEND_DIR" ]]; then
        cd "$FRONTEND_DIR"
        if npm run --silent type-check 2>/dev/null; then
            print_success "TypeScript compilation OK"
        else
            print_warning "TypeScript compilation issues detected"
        fi
        cd "$PROJECT_ROOT"
    fi
    
    print_step "Checking database connection"
    if command -v supabase &> /dev/null; then
        if supabase status | grep -q "API URL"; then
            print_success "Database connection OK"
        else
            print_warning "Database connection issues detected"
        fi
    fi
    
    print_step "Checking environment files"
    local env_files=(
        "$BACKEND_DIR/.env"
        "$FRONTEND_DIR/.env.local"
    )
    
    for env_file in "${env_files[@]}"; do
        if [[ -f "$env_file" ]]; then
            print_success "$(basename "$(dirname "$env_file")")/$(basename "$env_file") exists"
        else
            print_warning "$(basename "$(dirname "$env_file")")/$(basename "$env_file") missing"
        fi
    done
}

# =============================================================================
# Main Execution
# =============================================================================

main() {
    print_header "VESLINT Development Environment Setup"
    print_info "This script will set up your complete development environment"
    echo ""
    
    # Confirmation
    read -p "Continue with setup? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Setup cancelled"
        exit 0
    fi
    
    # Run setup steps
    check_system_requirements
    setup_python_environment
    setup_nodejs_environment
    setup_database
    setup_environment_files
    setup_development_tools
    run_health_check
    
    print_header "Setup Complete!"
    
    print_success "VESLINT development environment is ready!"
    echo ""
    print_info "Next steps:"
    echo -e "${WHITE}1. Review and update environment files:${NC}"
    echo -e "   - $BACKEND_DIR/.env"
    echo -e "   - $FRONTEND_DIR/.env.local"
    echo ""
    echo -e "${WHITE}2. Start development servers:${NC}"
    echo -e "   ./dev.sh backend   # Start backend API"
    echo -e "   ./dev.sh frontend  # Start frontend app"
    echo ""
    echo -e "${WHITE}3. Access the application:${NC}"
    echo -e "   Frontend: http://localhost:3000"
    echo -e "   Backend API: http://localhost:8000"
    echo -e "   API Docs: http://localhost:8000/docs"
    echo -e "   Supabase Studio: http://localhost:54323"
    echo ""
    echo -e "${WHITE}4. Run tests:${NC}"
    echo -e "   ./dev.sh test"
    echo ""
    print_info "For help, run: ./dev.sh"
    print_success "Happy coding! 🚀"
}

# Handle script arguments
case "${1:-}" in
    "--help"|"-h")
        echo "VESLINT Development Setup Script"
        echo "Usage: $0 [--help]"
        echo ""
        echo "This script sets up a complete development environment for VESLINT"
        echo "including Python backend, Node.js frontend, and Supabase database."
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac