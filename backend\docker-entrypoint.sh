#!/bin/bash
# VESLINT Backend Docker Entrypoint Script
# Handles container initialization, environment setup, and service startup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Display startup banner
echo "=============================================="
echo "🚢 VESLINT Maritime Vessel Classification API"
echo "=============================================="
echo "Version: 1.0.0"
echo "Environment: ${ENVIRONMENT:-production}"
echo "Port: ${PORT:-8000}"
echo "=============================================="

# Set default values for environment variables
export ENVIRONMENT=${ENVIRONMENT:-production}
export LOG_LEVEL=${LOG_LEVEL:-info}
export PORT=${PORT:-8000}
export PYTHONPATH=${PYTHONPATH:-/app/src}
export ML_MODEL_PATH=${ML_MODEL_PATH:-/app/models/extreme_maritime_classifier.joblib}
export MAX_FILE_SIZE_MB=${MAX_FILE_SIZE_MB:-50}

# Validate required environment variables
log_info "Validating environment configuration..."

required_vars=(
    "SUPABASE_URL"
    "SUPABASE_ANON_KEY"
    "FIREBASE_PROJECT_ID"
)

missing_vars=()
for var in "${required_vars[@]}"; do
    if [[ -z "${!var}" ]]; then
        missing_vars+=("$var")
    fi
done

if [[ ${#missing_vars[@]} -gt 0 ]]; then
    log_error "Missing required environment variables:"
    printf ' - %s\n' "${missing_vars[@]}"
    log_error "Please set these variables in your Render dashboard."
    exit 1
fi

log_success "Environment validation passed"

# Validate Python environment
log_info "Validating Python environment..."
python --version
pip list --format=freeze | head -5

# Check if virtual environment is activated
if [[ -z "${VIRTUAL_ENV}" ]]; then
    log_warning "Virtual environment not detected, but continuing..."
else
    log_success "Virtual environment active: ${VIRTUAL_ENV}"
fi

# Validate application structure
log_info "Validating application structure..."

required_paths=(
    "/app/src"
    "/app/src/main.py"
    "/app/src/models"
    "/app/src/services"
    "/app/src/api"
    "/app/src/utils"
)

for path in "${required_paths[@]}"; do
    if [[ ! -e "$path" ]]; then
        log_error "Missing required path: $path"
        exit 1
    fi
done

log_success "Application structure validated"

# Check ML model availability
log_info "Checking ML model availability..."
if [[ -f "$ML_MODEL_PATH" ]]; then
    model_size=$(du -h "$ML_MODEL_PATH" | cut -f1)
    log_success "ML model found: $ML_MODEL_PATH ($model_size)"
else
    log_warning "ML model not found at: $ML_MODEL_PATH"
    log_warning "The API will use a dummy classifier for development/testing"
fi

# Create necessary directories
log_info "Creating runtime directories..."
mkdir -p /app/logs /app/tmp
log_success "Runtime directories created"

# Test Python imports
log_info "Testing critical Python imports..."
python -c "
import sys
import fastapi
import pandas
import numpy
import sklearn
import supabase
import firebase_admin
print('✓ All critical imports successful')
" || {
    log_error "Critical Python imports failed"
    exit 1
}

log_success "Python imports validated"

# Test database connectivity (with timeout)
log_info "Testing database connectivity..."
timeout 10 python -c "
import os
from supabase import create_client
try:
    supabase_client = create_client(
        os.getenv('SUPABASE_URL'),
        os.getenv('SUPABASE_ANON_KEY')
    )
    # Simple health check
    response = supabase_client.table('jobs').select('count').limit(1).execute()
    print('✓ Supabase connection successful')
except Exception as e:
    print(f'⚠ Supabase connection warning: {e}')
    print('  API will continue but database operations may fail')
" || {
    log_warning "Database connectivity test timed out or failed"
    log_warning "API will start but database operations may fail"
}

# Set up signal handlers for graceful shutdown
log_info "Setting up signal handlers..."

# Function to handle graceful shutdown
graceful_shutdown() {
    log_info "Received shutdown signal, gracefully stopping..."
    
    # If there's a PID file, try to stop the process gracefully
    if [[ -f /app/tmp/app.pid ]]; then
        local pid=$(cat /app/tmp/app.pid)
        if kill -0 "$pid" 2>/dev/null; then
            log_info "Sending SIGTERM to process $pid..."
            kill -TERM "$pid"
            
            # Wait up to 30 seconds for graceful shutdown
            local count=0
            while kill -0 "$pid" 2>/dev/null && [[ $count -lt 30 ]]; do
                sleep 1
                ((count++))
            done
            
            if kill -0 "$pid" 2>/dev/null; then
                log_warning "Graceful shutdown timeout, forcing termination..."
                kill -KILL "$pid"
            fi
        fi
        rm -f /app/tmp/app.pid
    fi
    
    log_info "Cleanup completed, exiting..."
    exit 0
}

# Register signal handlers
trap graceful_shutdown SIGTERM SIGINT

# Environment-specific setup
if [[ "$ENVIRONMENT" == "development" ]]; then
    log_info "Development environment detected"
    export RELOAD=true
    
    # Install additional development tools if needed
    if ! command -v pytest &> /dev/null; then
        log_info "Installing development dependencies..."
        pip install pytest pytest-asyncio httpx --quiet
    fi
    
elif [[ "$ENVIRONMENT" == "production" ]]; then
    log_info "Production environment detected"
    export RELOAD=false
    
    # Set production-optimized settings
    export WORKERS=${WORKERS:-1}
    export WORKER_CLASS=${WORKER_CLASS:-uvicorn.workers.UvicornWorker}
    
fi

# Configure CORS origins
if [[ -n "$CORS_ORIGINS" ]]; then
    log_info "CORS origins configured: $CORS_ORIGINS"
else
    log_warning "CORS_ORIGINS not set, using default configuration"
fi

# Performance tuning for free tier
log_info "Applying performance optimizations for free tier..."
export UVICORN_WORKERS=${UVICORN_WORKERS:-1}
export UVICORN_WORKER_CONNECTIONS=${UVICORN_WORKER_CONNECTIONS:-100}
export UVICORN_BACKLOG=${UVICORN_BACKLOG:-50}

# Memory management
export PYTHONOPTIMIZE=1
export MALLOC_ARENA_MAX=2

log_success "Performance optimizations applied"

# Final health check before starting
log_info "Performing final health check..."
python -c "
import sys
import os
sys.path.insert(0, '/app/src')

try:
    from main import app
    print('✓ FastAPI application import successful')
except Exception as e:
    print(f'✗ FastAPI application import failed: {e}')
    sys.exit(1)
" || {
    log_error "Application health check failed"
    exit 1
}

log_success "Health check passed"

# Display startup summary
echo ""
echo "🚀 Starting VESLINT API Server..."
echo "   Environment: $ENVIRONMENT"
echo "   Port: $PORT"
echo "   Log Level: $LOG_LEVEL"
echo "   Workers: ${UVICORN_WORKERS}"
echo "   Reload: ${RELOAD:-false}"
echo ""

# Store process info for graceful shutdown
echo $$ > /app/tmp/app.pid

# Execute the command passed to the container
log_info "Executing command: $*"
exec "$@"
