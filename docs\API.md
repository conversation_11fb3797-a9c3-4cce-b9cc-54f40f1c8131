# VESLINT API Documentation

This document provides comprehensive documentation for the VESLINT backend API, including authentication, endpoints, request/response formats, and examples.

## 📋 Table of Contents

- [Overview](#overview)
- [Authentication](#authentication)
- [Base URL and Versioning](#base-url-and-versioning)
- [Common Response Format](#common-response-format)
- [Error Handling](#error-handling)
- [Rate Limiting](#rate-limiting)
- [Endpoints](#endpoints)
- [WebSocket Real-time](#websocket-real-time)
- [Examples](#examples)
- [SDKs and Libraries](#sdks-and-libraries)

## 🎯 Overview

The VESLINT API is a RESTful API built with FastAPI that provides maritime vessel classification services. It processes AIS (Automatic Identification System) data and uses machine learning to classify vessel types.

### Key Features

- **🔒 Secure Authentication**: JWT-based authentication with Firebase Auth
- **📊 Real-time Processing**: Asynchronous job processing with live updates
- **🤖 ML Classification**: Advanced vessel type classification
- **📈 Scalable**: Designed for high throughput and concurrent requests
- **📖 Auto-documented**: Interactive API documentation with OpenAPI/Swagger

### API Characteristics

- **Protocol**: HTTPS only in production
- **Format**: JSON for all requests and responses
- **Authentication**: Bearer token (JWT) required for most endpoints
- **Rate Limiting**: 1000 requests per hour per user (configurable)
- **Versioning**: URL path versioning (`/api/v1/`)

## 🔐 Authentication

### Authentication Flow

VESLINT uses Firebase Authentication with JWT tokens:

1. **User Registration/Login**: Via Firebase Auth SDK
2. **Token Retrieval**: Get JWT token from Firebase
3. **API Requests**: Include token in Authorization header
4. **Token Validation**: Backend validates with Firebase

### Authorization Header

```http
Authorization: Bearer <firebase_jwt_token>
```

### Getting a Token

```javascript
// Frontend (Firebase SDK)
import { getAuth, signInWithEmailAndPassword } from 'firebase/auth';

const auth = getAuth();
const userCredential = await signInWithEmailAndPassword(auth, email, password);
const token = await userCredential.user.getIdToken();

// Use token in API requests
fetch('/api/v1/jobs', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});
```

### Token Validation

The backend validates tokens on each request:

- **Token Expiration**: 1 hour (configurable)
- **Token Refresh**: Handled automatically by Firebase SDK
- **User Context**: User ID extracted from token for data isolation

## 🌐 Base URL and Versioning

### Base URLs

| Environment | Base URL |
|-------------|----------|
| **Local Development** | `http://localhost:8000` |
| **Production** | `https://veslint-api.onrender.com` |
| **Custom Domain** | `https://api.veslint.com` |

### API Versioning

Current API version: **v1**

- **Base Path**: `/api/v1/`
- **Full URL**: `{base_url}/api/v1/{endpoint}`
- **Versioning Strategy**: URL path versioning for major versions

### Interactive Documentation

- **Swagger UI**: `{base_url}/docs`
- **ReDoc**: `{base_url}/redoc`
- **OpenAPI Schema**: `{base_url}/openapi.json`

## 📦 Common Response Format

### Standard Response Structure

All API responses follow a consistent format:

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data here
  },
  "metadata": {
    "timestamp": "2024-01-01T12:00:00Z",
    "request_id": "req_123456789",
    "version": "v1"
  }
}
```

### Pagination Response

For paginated endpoints:

```json
{
  "success": true,
  "message": "Data retrieved successfully",
  "data": [
    // Array of items
  ],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total_items": 150,
    "total_pages": 8,
    "has_next": true,
    "has_previous": false
  },
  "metadata": {
    "timestamp": "2024-01-01T12:00:00Z",
    "request_id": "req_123456789"
  }
}
```

## ❌ Error Handling

### Error Response Format

```json
{
  "success": false,
  "message": "Detailed error message",
  "error": {
    "code": "VALIDATION_ERROR",
    "type": "ValidationError",
    "details": {
      "field": "email",
      "reason": "Invalid email format"
    }
  },
  "metadata": {
    "timestamp": "2024-01-01T12:00:00Z",
    "request_id": "req_123456789"
  }
}
```

### HTTP Status Codes

| Status Code | Meaning | Description |
|-------------|---------|-------------|
| **200** | OK | Request successful |
| **201** | Created | Resource created successfully |
| **400** | Bad Request | Invalid request data |
| **401** | Unauthorized | Authentication required |
| **403** | Forbidden | Access denied |
| **404** | Not Found | Resource not found |
| **422** | Unprocessable Entity | Validation error |
| **429** | Too Many Requests | Rate limit exceeded |
| **500** | Internal Server Error | Server error |

### Common Error Codes

| Error Code | Description |
|------------|-------------|
| `VALIDATION_ERROR` | Request validation failed |
| `AUTHENTICATION_ERROR` | Invalid or expired token |
| `AUTHORIZATION_ERROR` | Insufficient permissions |
| `RESOURCE_NOT_FOUND` | Requested resource doesn't exist |
| `RATE_LIMIT_EXCEEDED` | Too many requests |
| `FILE_TOO_LARGE` | Uploaded file exceeds size limit |
| `INVALID_FILE_FORMAT` | Unsupported file format |
| `PROCESSING_ERROR` | Error during data processing |
| `MODEL_ERROR` | ML model inference error |

## 🚦 Rate Limiting

### Rate Limits

| Endpoint Category | Limit | Window |
|------------------|-------|--------|
| **Authentication** | 10 requests | 1 minute |
| **File Upload** | 5 requests | 1 minute |
| **Job Creation** | 20 requests | 1 hour |
| **General API** | 1000 requests | 1 hour |
| **Health Check** | No limit | - |

### Rate Limit Headers

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
X-RateLimit-Window: 3600
```

### Rate Limit Exceeded Response

```json
{
  "success": false,
  "message": "Rate limit exceeded",
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "type": "RateLimitError",
    "details": {
      "limit": 1000,
      "window": 3600,
      "reset_time": "2024-01-01T13:00:00Z"
    }
  }
}
```

## 🔗 Endpoints

### Health and Status

#### Get API Health
```http
GET /health
```

**Description**: Check API health and system status

**Authentication**: None required

**Response**:
```json
{
  "success": true,
  "message": "API is healthy",
  "data": {
    "status": "healthy",
    "version": "2.0.0",
    "uptime": 3600,
    "database": "connected",
    "ml_model": "loaded",
    "timestamp": "2024-01-01T12:00:00Z"
  }
}
```

#### Get API Information
```http
GET /api/v1/info
```

**Description**: Get API version and feature information

**Authentication**: None required

**Response**:
```json
{
  "success": true,
  "data": {
    "name": "VESLINT API",
    "version": "v1",
    "description": "Maritime vessel classification API",
    "features": ["vessel_classification", "real_time_processing", "batch_upload"],
    "supported_formats": ["csv"],
    "max_file_size": "50MB",
    "supported_vessel_types": ["cargo", "tanker", "passenger", "fishing", "pleasure_craft"]
  }
}
```

### Job Management

#### Create Classification Job
```http
POST /api/v1/jobs
```

**Description**: Create a new vessel classification job

**Authentication**: Required

**Request Body**:
```json
{
  "title": "AIS Data Analysis - January 2024",
  "description": "Vessel classification for Port of Hamburg",
  "job_type": "classification",
  "configuration": {
    "confidence_threshold": 0.7,
    "include_features": true,
    "batch_size": 100
  }
}
```

**Response**:
```json
{
  "success": true,
  "message": "Job created successfully",
  "data": {
    "job_id": "job_abc123def456",
    "title": "AIS Data Analysis - January 2024",
    "status": "pending",
    "created_at": "2024-01-01T12:00:00Z",
    "estimated_completion": "2024-01-01T12:05:00Z"
  }
}
```

#### Get Job Status
```http
GET /api/v1/jobs/{job_id}
```

**Description**: Get detailed job information and status

**Authentication**: Required

**Path Parameters**:
- `job_id` (string): Job identifier

**Response**:
```json
{
  "success": true,
  "data": {
    "job_id": "job_abc123def456",
    "title": "AIS Data Analysis - January 2024",
    "status": "processing",
    "progress": 45,
    "vessel_count": 150,
    "processed_vessels": 68,
    "created_at": "2024-01-01T12:00:00Z",
    "started_at": "2024-01-01T12:01:00Z",
    "estimated_completion": "2024-01-01T12:05:00Z",
    "configuration": {
      "confidence_threshold": 0.7,
      "include_features": true
    }
  }
}
```

#### List User Jobs
```http
GET /api/v1/jobs
```

**Description**: Get list of user's jobs with pagination

**Authentication**: Required

**Query Parameters**:
- `page` (integer, optional): Page number (default: 1)
- `page_size` (integer, optional): Items per page (default: 20, max: 100)
- `status` (string, optional): Filter by status (pending, processing, completed, failed)
- `sort` (string, optional): Sort field (created_at, title, status)
- `order` (string, optional): Sort order (asc, desc)

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "job_id": "job_abc123def456",
      "title": "AIS Data Analysis - January 2024",
      "status": "completed",
      "vessel_count": 150,
      "created_at": "2024-01-01T12:00:00Z",
      "completed_at": "2024-01-01T12:05:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total_items": 5,
    "total_pages": 1,
    "has_next": false,
    "has_previous": false
  }
}
```

#### Get Job Results
```http
GET /api/v1/jobs/{job_id}/results
```

**Description**: Get classification results for a completed job

**Authentication**: Required

**Query Parameters**:
- `format` (string, optional): Response format (json, csv) (default: json)
- `include_features` (boolean, optional): Include feature data (default: false)
- `confidence_min` (float, optional): Minimum confidence threshold (0.0-1.0)

**Response**:
```json
{
  "success": true,
  "data": {
    "job_id": "job_abc123def456",
    "summary": {
      "total_vessels": 150,
      "classified_vessels": 148,
      "average_confidence": 0.87,
      "processing_time": 125.5,
      "classification_distribution": {
        "cargo": 45,
        "tanker": 23,
        "fishing": 31,
        "passenger": 18,
        "pleasure_craft": 31
      }
    },
    "vessels": [
      {
        "mmsi": 123456789,
        "vessel_name": "OCEAN EXPLORER",
        "predicted_type": "cargo",
        "confidence": 0.92,
        "class_probabilities": {
          "cargo": 0.92,
          "tanker": 0.05,
          "fishing": 0.02,
          "passenger": 0.01
        },
        "data_points": 45,
        "time_span_hours": 12.5,
        "first_seen": "2024-01-01T00:00:00Z",
        "last_seen": "2024-01-01T12:30:00Z"
      }
    ]
  }
}
```

#### Cancel Job
```http
DELETE /api/v1/jobs/{job_id}
```

**Description**: Cancel a pending or processing job

**Authentication**: Required

**Response**:
```json
{
  "success": true,
  "message": "Job cancelled successfully",
  "data": {
    "job_id": "job_abc123def456",
    "status": "cancelled",
    "cancelled_at": "2024-01-01T12:03:00Z"
  }
}
```

### File Upload

#### Upload CSV File
```http
POST /api/v1/jobs/{job_id}/upload
```

**Description**: Upload AIS data CSV file for processing

**Authentication**: Required

**Content-Type**: `multipart/form-data`

**Request Body**:
```
file: [CSV file] (required)
```

**CSV Format Requirements**:
- **Required columns**: `mmsi`, `timestamp`, `lat`, `lon`, `sog`, `cog`
- **Optional columns**: `heading`, `vessel_name`, `call_sign`, `imo`
- **Max file size**: 50MB
- **Max vessels**: 10,000 per file

**Response**:
```json
{
  "success": true,
  "message": "File uploaded successfully",
  "data": {
    "job_id": "job_abc123def456",
    "file_name": "ais_data_january.csv",
    "file_size": 1024000,
    "vessel_count": 150,
    "data_points": 4500,
    "time_span": "2024-01-01T00:00:00Z to 2024-01-31T23:59:59Z",
    "validation_status": "passed"
  }
}
```

### Direct Vessel Classification

#### Classify Single Vessel
```http
POST /api/v1/vessels/classify
```

**Description**: Classify a single vessel directly (synchronous)

**Authentication**: Required

**Request Body**:
```json
{
  "mmsi": 123456789,
  "vessel_name": "OCEAN EXPLORER",
  "ais_data": [
    {
      "timestamp": "2024-01-01T12:00:00Z",
      "lat": 40.7128,
      "lon": -74.0060,
      "sog": 12.5,
      "cog": 90.0,
      "heading": 85.0
    },
    {
      "timestamp": "2024-01-01T12:15:00Z",
      "lat": 40.7135,
      "lon": -74.0040,
      "sog": 13.2,
      "cog": 92.0,
      "heading": 88.0
    }
  ]
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "mmsi": 123456789,
    "vessel_name": "OCEAN EXPLORER",
    "predicted_type": "cargo",
    "confidence": 0.92,
    "class_probabilities": {
      "cargo": 0.92,
      "tanker": 0.05,
      "fishing": 0.02,
      "passenger": 0.01
    },
    "features": {
      "data_points": 2,
      "time_span_hours": 0.25,
      "avg_speed": 12.85,
      "total_distance": 3.2
    },
    "processing_time_ms": 45
  }
}
```

#### Get Model Information
```http
GET /api/v1/vessels/model-info
```

**Description**: Get information about the ML model

**Authentication**: Optional

**Response**:
```json
{
  "success": true,
  "data": {
    "model_name": "VESLINT Maritime Classifier",
    "version": "1.0.0",
    "algorithm": "XGBoost",
    "features_count": 99,
    "classes": ["cargo", "tanker", "passenger", "fishing", "pleasure_craft", "high_speed_craft", "tug_tow", "military", "pilot", "search_rescue", "other", "unknown"],
    "accuracy": 0.92,
    "training_date": "2024-01-01",
    "last_updated": "2024-01-01T00:00:00Z"
  }
}
```

### User Management

#### Get User Profile
```http
GET /api/v1/users/profile
```

**Description**: Get current user's profile information

**Authentication**: Required

**Response**:
```json
{
  "success": true,
  "data": {
    "user_id": "user_123456",
    "email": "<EMAIL>",
    "display_name": "John Doe",
    "role": "user",
    "organization": "Maritime Analytics Inc",
    "created_at": "2024-01-01T00:00:00Z",
    "last_login": "2024-01-01T12:00:00Z",
    "usage_stats": {
      "total_jobs": 15,
      "total_vessels_processed": 2500,
      "current_month_usage": 150
    },
    "preferences": {
      "default_confidence_threshold": 0.7,
      "email_notifications": true,
      "data_retention_days": 30
    }
  }
}
```

#### Update User Profile
```http
PUT /api/v1/users/profile
```

**Description**: Update user profile information

**Authentication**: Required

**Request Body**:
```json
{
  "display_name": "John Doe",
  "organization": "Maritime Analytics Inc",
  "preferences": {
    "default_confidence_threshold": 0.8,
    "email_notifications": false,
    "data_retention_days": 60
  }
}
```

**Response**:
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "user_id": "user_123456",
    "display_name": "John Doe",
    "organization": "Maritime Analytics Inc",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

## 🔄 WebSocket Real-time

VESLINT provides real-time updates for job processing via WebSocket connections.

### Connection

```javascript
const ws = new WebSocket('wss://api.veslint.com/ws/jobs/{job_id}?token={jwt_token}');

ws.onopen = function(event) {
  console.log('Connected to job updates');
};

ws.onmessage = function(event) {
  const update = JSON.parse(event.data);
  console.log('Job update:', update);
};
```

### Real-time Message Format

```json
{
  "type": "job_update",
  "job_id": "job_abc123def456",
  "status": "processing",
  "progress": 75,
  "processed_vessels": 112,
  "current_vessel": {
    "mmsi": 987654321,
    "status": "classified",
    "type": "cargo",
    "confidence": 0.89
  },
  "timestamp": "2024-01-01T12:03:45Z"
}
```

### Message Types

| Type | Description |
|------|-------------|
| `job_update` | Job progress update |
| `job_completed` | Job completed successfully |
| `job_failed` | Job failed with error |
| `vessel_classified` | Individual vessel classified |
| `error` | WebSocket error |

## 📚 Examples

### Complete Job Workflow

```javascript
// 1. Create a job
const createJob = async () => {
  const response = await fetch('/api/v1/jobs', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      title: 'Port Analysis Q1 2024',
      description: 'Vessel classification for quarterly report',
      job_type: 'classification'
    })
  });
  
  const result = await response.json();
  return result.data.job_id;
};

// 2. Upload CSV file
const uploadFile = async (jobId, file) => {
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await fetch(`/api/v1/jobs/${jobId}/upload`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });
  
  return await response.json();
};

// 3. Monitor job progress
const monitorJob = (jobId) => {
  const ws = new WebSocket(`wss://api.veslint.com/ws/jobs/${jobId}?token=${token}`);
  
  ws.onmessage = (event) => {
    const update = JSON.parse(event.data);
    
    if (update.type === 'job_completed') {
      console.log('Job completed!');
      getResults(jobId);
    } else if (update.type === 'job_update') {
      console.log(`Progress: ${update.progress}%`);
    }
  };
};

// 4. Get results
const getResults = async (jobId) => {
  const response = await fetch(`/api/v1/jobs/${jobId}/results`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  const results = await response.json();
  console.log('Classification results:', results.data);
};
```

### Error Handling

```javascript
const handleApiCall = async (url, options) => {
  try {
    const response = await fetch(url, options);
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.message || 'API request failed');
    }
    
    return data;
  } catch (error) {
    console.error('API Error:', error);
    
    if (error.message.includes('401')) {
      // Handle authentication error
      redirectToLogin();
    } else if (error.message.includes('429')) {
      // Handle rate limiting
      showRateLimitMessage();
    } else {
      // Handle other errors
      showErrorMessage(error.message);
    }
    
    throw error;
  }
};
```

## 🛠️ SDKs and Libraries

### JavaScript/TypeScript SDK

```typescript
import { VeslintAPI } from '@veslint/sdk';

const client = new VeslintAPI({
  baseUrl: 'https://api.veslint.com',
  apiKey: 'your-api-key'
});

// Create and process job
const job = await client.jobs.create({
  title: 'Analysis Job',
  description: 'Vessel classification'
});

await client.jobs.uploadFile(job.job_id, csvFile);
const results = await client.jobs.waitForCompletion(job.job_id);
```

### Python SDK

```python
from veslint import VeslintClient

client = VeslintClient(
    base_url="https://api.veslint.com",
    api_key="your-api-key"
)

# Create and process job
job = client.jobs.create(
    title="Analysis Job",
    description="Vessel classification"
)

client.jobs.upload_file(job.job_id, "ais_data.csv")
results = client.jobs.wait_for_completion(job.job_id)
```

### cURL Examples

```bash
# Create job
curl -X POST "https://api.veslint.com/api/v1/jobs" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test Job",
    "description": "API testing",
    "job_type": "classification"
  }'

# Upload file
curl -X POST "https://api.veslint.com/api/v1/jobs/JOB_ID/upload" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@ais_data.csv"

# Get results
curl -X GET "https://api.veslint.com/api/v1/jobs/JOB_ID/results" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🔧 Development and Testing

### Local API Testing

```bash
# Start local development server
npm run dev:backend

# Test endpoints
curl http://localhost:8000/health
curl http://localhost:8000/docs  # Interactive documentation
```

### API Testing Tools

- **Swagger UI**: Built-in interactive documentation
- **Postman**: Import OpenAPI schema from `/openapi.json`
- **Insomnia**: REST client with OpenAPI support
- **HTTPie**: Command-line HTTP client

### Mock Data

Sample CSV format for testing:

```csv
mmsi,timestamp,lat,lon,sog,cog,heading,vessel_name,call_sign
123456789,2024-01-01T00:00:00Z,40.7128,-74.0060,12.5,90.0,85.0,TEST_VESSEL,TEST1
123456789,2024-01-01T00:15:00Z,40.7130,-74.0040,13.2,92.0,88.0,TEST_VESSEL,TEST1
987654321,2024-01-01T00:00:00Z,51.5074,-0.1278,8.5,180.0,175.0,CARGO_SHIP,CALL2
```

## 📞 Support

### Getting Help

- **Documentation**: This guide and inline API docs
- **Interactive Testing**: Use `/docs` endpoint for live testing
- **GitHub Issues**: Report bugs and feature requests
- **Email Support**: <EMAIL>

### API Status

- **Status Page**: https://status.veslint.com
- **Health Endpoint**: `/health` for real-time status
- **Maintenance Windows**: Announced via email and status page

---

This completes the VESLINT API documentation. For additional information, please refer to the interactive documentation at `/docs` or contact our support team.