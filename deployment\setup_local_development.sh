#!/bin/bash

# VESLINT Local Development Setup Script
# Sets up complete local development environment for testing

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# ASCII Art Banner
print_banner() {
    echo -e "${BLUE}"
    cat << "EOF"
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    ██╗   ██╗███████╗███████╗██╗     ██╗███╗   ██╗████████╗   ║
║    ██║   ██║██╔════╝██╔════╝██║     ██║████╗  ██║╚══██╔══╝   ║
║    ██║   ██║█████╗  ███████╗██║     ██║██╔██╗ ██║   ██║      ║
║    ██║   ██║██╔══╝  ╚════██║██║     ██║██║╚██╗██║   ██║      ║
║    ╚██████╔╝███████╗███████║███████╗██║██║ ╚████║   ██║      ║
║     ╚═════╝ ╚══════╝╚══════╝╚══════╝╚═╝╚═╝  ╚═══╝   ╚═╝      ║
║                                                              ║
║            Maritime Intelligence Platform                    ║
║              Local Development Setup                         ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# Check if running from correct directory
check_directory() {
    if [[ ! -f "backend/src/main.py" || ! -f "backend/requirements.txt" ]]; then
        log_error "Please run this script from the VESLINT project root directory"
        log_info "Expected structure:"
        echo "  VESLINT/"
        echo "  ├── backend/"
        echo "  │   ├── src/main.py"
        echo "  │   └── requirements.txt"
        echo "  └── deployment/"
        echo "      └── setup_local_development.sh"
        exit 1
    fi
}

# Check system requirements
check_requirements() {
    log_step "Checking system requirements..."
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is required but not installed"
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
    if [[ $(echo "$python_version >= 3.11" | bc -l 2>/dev/null || echo "0") == "0" ]]; then
        log_warning "Python 3.11+ recommended (found: Python $python_version)"
    else
        log_success "Python $python_version detected"
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js is required but not installed"
        log_info "Install from: https://nodejs.org/"
        exit 1
    fi
    
    node_version=$(node --version | sed 's/v//')
    if [[ $(echo "$node_version >= 18.0" | bc -l 2>/dev/null || echo "0") == "0" ]]; then
        log_warning "Node.js 18+ recommended (found: v$node_version)"
    else
        log_success "Node.js v$node_version detected"
    fi
    
    # Check Git
    if ! command -v git &> /dev/null; then
        log_error "Git is required but not installed"
        exit 1
    fi
    
    log_success "All system requirements satisfied"
}

# Setup backend environment
setup_backend() {
    log_step "Setting up backend environment..."
    
    cd backend
    
    # Create virtual environment
    if [[ ! -d "venv" ]]; then
        log_info "Creating Python virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install dependencies
    log_info "Installing Python dependencies..."
    pip install -r requirements.txt
    
    # Check if model file exists
    if [[ ! -f "src/assets/extreme_maritime_classifier.joblib" ]]; then
        log_warning "Trained model not found at src/assets/extreme_maritime_classifier.joblib"
        log_info "The system will use a dummy classifier for development"
        
        # Create assets directory
        mkdir -p src/assets
        
        # Create placeholder file
        touch src/assets/extreme_maritime_classifier.joblib.placeholder
        echo "# Place your trained model file here" > src/assets/README.md
    else
        log_success "Trained model found"
    fi
    
    # Create development environment file
    if [[ ! -f ".env" ]]; then
        log_info "Creating development environment file..."
        cat > .env << 'EOF'
# VESLINT Backend Development Environment
ENVIRONMENT=development
PYTHONPATH=./src
LOG_LEVEL=DEBUG

# Database (will be set up in next steps)
SUPABASE_URL=
SUPABASE_SERVICE_KEY=
SUPABASE_ANON_KEY=

# Authentication (will be set up in next steps)
FIREBASE_PROJECT_ID=
FIREBASE_CREDENTIALS_JSON=

# Development settings
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
ALLOWED_HOSTS=localhost,127.0.0.1

# Performance settings for local development
MALLOC_ARENA_MAX=2
PYTHONUNBUFFERED=1
EOF
        log_success "Development environment file created"
    fi
    
    cd ..
    log_success "Backend environment setup complete"
}

# Test backend functionality
test_backend() {
    log_step "Testing backend functionality..."
    
    cd backend
    source venv/bin/activate
    
    # Start backend in background
    log_info "Starting backend server..."
    python src/main.py &
    BACKEND_PID=$!
    
    # Wait for server to start
    sleep 5
    
    # Test health endpoint
    if curl -s http://localhost:8000/health | grep -q "healthy"; then
        log_success "Backend health check passed"
    else
        log_error "Backend health check failed"
        kill $BACKEND_PID 2>/dev/null
        cd ..
        return 1
    fi
    
    # Test model info endpoint
    if curl -s http://localhost:8000/api/v1/vessels/model-info | grep -q "model"; then
        log_success "ML model endpoint accessible"
    else
        log_warning "ML model endpoint not fully functional (expected for dummy model)"
    fi
    
    # Stop backend
    kill $BACKEND_PID 2>/dev/null
    wait $BACKEND_PID 2>/dev/null
    
    cd ..
    log_success "Backend functionality test complete"
}

# Setup frontend (if migrated)
setup_frontend() {
    log_step "Checking for frontend setup..."
    
    if [[ -d "veslint-frontend" ]]; then
        log_info "Found Next.js frontend, setting up..."
        
        cd veslint-frontend
        
        # Install dependencies
        if [[ ! -d "node_modules" ]]; then
            log_info "Installing frontend dependencies..."
            npm install
        fi
        
        # Create development environment
        if [[ ! -f ".env.local" ]]; then
            log_info "Creating frontend environment file..."
            cat > .env.local << 'EOF'
# VESLINT Frontend Development Environment
NEXT_PUBLIC_API_URL=http://localhost:8000

# Database (fill these in after Supabase setup)
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=

# Authentication (fill these in after Firebase setup)
NEXT_PUBLIC_FIREBASE_API_KEY=
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=
NEXT_PUBLIC_FIREBASE_PROJECT_ID=
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=
NEXT_PUBLIC_FIREBASE_APP_ID=

# Development settings
NODE_ENV=development
EOF
        fi
        
        cd ..
        log_success "Frontend setup complete"
    else
        log_info "Frontend not found - run migration first or use existing React frontend"
    fi
}

# Create sample test data
create_test_data() {
    log_step "Creating sample test data..."
    
    mkdir -p test-data
    
    # Create sample AIS CSV file
    cat > test-data/sample_ais_data.csv << 'EOF'
mmsi,timestamp,lat,lon,sog,cog,heading
123456789,2024-01-01T00:00:00Z,40.7128,-74.0060,12.5,180,175
123456789,2024-01-01T00:05:00Z,40.7130,-74.0058,12.8,182,178
123456789,2024-01-01T00:10:00Z,40.7132,-74.0056,13.1,184,180
123456789,2024-01-01T00:15:00Z,40.7134,-74.0054,13.0,186,182
987654321,2024-01-01T00:00:00Z,51.5074,-0.1278,8.2,90,95
987654321,2024-01-01T00:05:00Z,51.5080,-0.1270,8.5,92,98
987654321,2024-01-01T00:10:00Z,51.5086,-0.1262,8.8,94,100
987654321,2024-01-01T00:15:00Z,51.5092,-0.1254,9.0,96,102
555666777,2024-01-01T00:00:00Z,35.6762,139.6503,15.2,45,50
555666777,2024-01-01T00:05:00Z,35.6770,139.6510,15.8,47,52
555666777,2024-01-01T00:10:00Z,35.6778,139.6517,16.1,49,54
555666777,2024-01-01T00:15:00Z,35.6786,139.6524,16.0,51,56
EOF
    
    # Create larger test file
    cat > test-data/large_sample.csv << 'EOF'
mmsi,timestamp,lat,lon,sog,cog,heading
111111111,2024-01-01T00:00:00Z,33.7490,-84.3880,5.2,120,125
111111111,2024-01-01T00:05:00Z,33.7485,-84.3875,5.5,122,127
111111111,2024-01-01T00:10:00Z,33.7480,-84.3870,5.8,124,129
222222222,2024-01-01T00:00:00Z,41.8781,-87.6298,22.1,200,205
222222222,2024-01-01T00:05:00Z,41.8785,-87.6302,22.5,202,207
222222222,2024-01-01T00:10:00Z,41.8789,-87.6306,23.0,204,209
333333333,2024-01-01T00:00:00Z,37.7749,-122.4194,3.8,350,355
333333333,2024-01-01T00:05:00Z,37.7752,-122.4191,4.1,352,357
333333333,2024-01-01T00:10:00Z,37.7755,-122.4188,4.4,354,359
444444444,2024-01-01T00:00:00Z,25.7617,-80.1918,18.7,75,80
444444444,2024-01-01T00:05:00Z,25.7620,-80.1915,19.2,77,82
444444444,2024-01-01T00:10:00Z,25.7623,-80.1912,19.8,79,84
EOF
    
    log_success "Sample test data created in test-data/ directory"
}

# Create helper scripts
create_helper_scripts() {
    log_step "Creating helper scripts..."
    
    mkdir -p scripts
    
    # Backend start script
    cat > scripts/start_backend.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting VESLINT Backend..."
cd backend
source venv/bin/activate
python src/main.py
EOF
    chmod +x scripts/start_backend.sh
    
    # Frontend start script (if exists)
    if [[ -d "veslint-frontend" ]]; then
        cat > scripts/start_frontend.sh << 'EOF'
#!/bin/bash
echo "🎨 Starting VESLINT Frontend..."
cd veslint-frontend
npm run dev
EOF
        chmod +x scripts/start_frontend.sh
    fi
    
    # Test API script
    cat > scripts/test_api.sh << 'EOF'
#!/bin/bash
echo "🧪 Testing VESLINT API..."

BASE_URL=${1:-http://localhost:8000}

echo "Testing health endpoint..."
curl -s "$BASE_URL/health" | jq .

echo -e "\nTesting model info..."
curl -s "$BASE_URL/api/v1/vessels/model-info" | jq .

echo -e "\nTesting CSV validation..."
curl -s -X POST "$BASE_URL/api/v1/vessels/validate-csv" \
  -F "file=@test-data/sample_ais_data.csv" | jq .

echo -e "\nAPI documentation available at: $BASE_URL/docs"
EOF
    chmod +x scripts/test_api.sh
    
    # Full system test script
    cat > scripts/test_system.sh << 'EOF'
#!/bin/bash
echo "🔍 Testing Complete VESLINT System..."

# Test backend
echo "1. Testing Backend..."
./scripts/test_api.sh

# Test file classification (if backend is running)
echo -e "\n2. Testing Direct Classification..."
curl -s -X POST "http://localhost:8000/api/v1/vessels/classify" \
  -F "file=@test-data/sample_ais_data.csv" | jq .

# Frontend test (if available)
if [[ -d "veslint-frontend" ]]; then
    echo -e "\n3. Frontend available at: http://localhost:3000"
    echo "   Start with: ./scripts/start_frontend.sh"
fi

echo -e "\n✅ System test complete!"
EOF
    chmod +x scripts/test_system.sh
    
    # Development startup script
    cat > scripts/start_dev.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting VESLINT Development Environment..."

# Start backend in background
echo "Starting backend..."
./scripts/start_backend.sh &
BACKEND_PID=$!

# Wait for backend to start
echo "Waiting for backend to initialize..."
sleep 5

# Start frontend if available
if [[ -d "veslint-frontend" ]]; then
    echo "Starting frontend..."
    ./scripts/start_frontend.sh &
    FRONTEND_PID=$!
fi

echo "✅ Development environment started!"
echo "📊 Backend: http://localhost:8000"
echo "📚 API Docs: http://localhost:8000/docs"

if [[ -d "veslint-frontend" ]]; then
    echo "🎨 Frontend: http://localhost:3000"
fi

echo "🛑 Press Ctrl+C to stop all services"

# Wait for user interrupt
trap 'echo "Stopping services..."; kill $BACKEND_PID 2>/dev/null; kill $FRONTEND_PID 2>/dev/null; exit' INT
wait
EOF
    chmod +x scripts/start_dev.sh
    
    log_success "Helper scripts created in scripts/ directory"
}

# Create development documentation
create_dev_docs() {
    log_step "Creating development documentation..."
    
    cat > DEVELOPMENT_GUIDE.md << 'EOF'
# VESLINT Local Development Guide

## 🚀 Quick Start

```bash
# Start complete development environment
./scripts/start_dev.sh

# Or start components individually:
./scripts/start_backend.sh     # Backend only
./scripts/start_frontend.sh    # Frontend only (if migrated)
```

## 🧪 Testing

```bash
# Test API endpoints
./scripts/test_api.sh

# Test complete system
./scripts/test_system.sh

# Test with sample data
curl -X POST http://localhost:8000/api/v1/vessels/classify \
  -F "file=@test-data/sample_ais_data.csv"
```

## 📁 Project Structure

```
VESLINT/
├── backend/                 # FastAPI backend
│   ├── src/                 # Source code
│   │   ├── main.py          # Application entry point
│   │   ├── api/             # API routes
│   │   ├── services/        # Business logic
│   │   ├── models/          # Data models
│   │   └── utils/           # Utilities
│   ├── requirements.txt     # Python dependencies
│   └── .env                 # Environment variables
├── veslint-frontend/        # Next.js frontend (if migrated)
├── test-data/               # Sample CSV files
├── scripts/                 # Development scripts
└── docs/                    # Documentation
```

## 🔧 Configuration

### Backend Environment (.env)
```bash
ENVIRONMENT=development
PYTHONPATH=./src
LOG_LEVEL=DEBUG
SUPABASE_URL=your_supabase_url
FIREBASE_PROJECT_ID=your_firebase_project
```

### Frontend Environment (.env.local)
```bash
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_firebase_project
```

## 📊 Available Endpoints

- **Health Check**: http://localhost:8000/health
- **API Documentation**: http://localhost:8000/docs
- **Model Info**: http://localhost:8000/api/v1/vessels/model-info
- **Direct Classification**: POST /api/v1/vessels/classify
- **CSV Validation**: POST /api/v1/vessels/validate-csv

## 🗃️ Database Setup

1. Create Supabase project
2. Run migrations from database/ directory
3. Update environment variables
4. Test connection with /health/detailed

## 🔐 Authentication Setup

1. Create Firebase project
2. Enable Authentication
3. Download service account key
4. Update environment variables

## 🚨 Troubleshooting

**Backend won't start**: Check Python version and dependencies
**Model errors**: Ensure trained model is in backend/src/assets/
**Port conflicts**: Change ports in configuration files
**CORS errors**: Update CORS_ORIGINS in backend environment

## 📈 Production Deployment

Ready for production? Follow these guides:
- Backend: deployment/RENDER_DEPLOYMENT.md
- Frontend: docs/FRONTEND_MIGRATION_GUIDE.md
- Domain: docs/CLOUDFLARE_DOMAIN_SETUP.md
EOF
    
    log_success "Development documentation created"
}

# Main setup function
main() {
    print_banner
    
    log_info "Setting up VESLINT local development environment..."
    echo ""
    
    check_directory
    check_requirements
    setup_backend
    test_backend
    setup_frontend
    create_test_data
    create_helper_scripts
    create_dev_docs
    
    echo ""
    log_success "🎉 Local development environment setup complete!"
    echo ""
    echo -e "${GREEN}Next steps:${NC}"
    echo "1. 📝 Configure your environment variables:"
    echo "   • Edit backend/.env with your Supabase and Firebase credentials"
    echo "   • Edit veslint-frontend/.env.local (if using Next.js frontend)"
    echo ""
    echo "2. 🚀 Start development:"
    echo "   • ./scripts/start_dev.sh         # Start everything"
    echo "   • ./scripts/start_backend.sh     # Backend only"
    echo "   • ./scripts/start_frontend.sh    # Frontend only"
    echo ""
    echo "3. 🧪 Test your setup:"
    echo "   • ./scripts/test_api.sh          # Test API endpoints"
    echo "   • ./scripts/test_system.sh       # Full system test"
    echo ""
    echo "4. 📚 Documentation:"
    echo "   • DEVELOPMENT_GUIDE.md           # Development guide"
    echo "   • http://localhost:8000/docs     # API documentation"
    echo ""
    echo -e "${BLUE}Your VESLINT development environment is ready! 🚢⚓${NC}"
}

# Run main function
main "$@"