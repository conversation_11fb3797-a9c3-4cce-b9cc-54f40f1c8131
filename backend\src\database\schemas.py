"""
Database Schemas for VESLINT Backend.

This module defines the database table schemas, validation functions, and
data models for the Supabase database used in the VESLINT system.
"""

import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Union
from enum import Enum
from pydantic import BaseModel, Field, validator
from uuid import UUID, uuid4

# Module-level logger
logger = logging.getLogger(__name__)

# =============================================================================
# Enums for Database Values
# =============================================================================

class JobStatus(str, Enum):
    """Job processing status."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class VesselType(str, Enum):
    """Vessel classification types."""
    CARGO = "cargo"
    TANKER = "tanker"
    PASSENGER = "passenger"
    FISHING = "fishing"
    PLEASURE_CRAFT = "pleasure_craft"
    HIGH_SPEED_CRAFT = "high_speed_craft"
    TUG_TOW = "tug_tow"
    MILITARY = "military"
    PILOT = "pilot"
    SEARCH_RESCUE = "search_rescue"
    OTHER = "other"
    UNKNOWN = "unknown"

class JobType(str, Enum):
    """Job processing types."""
    CLASSIFICATION = "classification"
    ANALYSIS = "analysis"
    PREDICTION = "prediction"
    BATCH_PROCESSING = "batch_processing"

class UserRole(str, Enum):
    """User roles in the system."""
    USER = "user"
    ADMIN = "admin"
    RESEARCHER = "researcher"
    ANALYST = "analyst"

# =============================================================================
# Database Schema Definitions
# =============================================================================

class DatabaseSchemas:
    """Container for all database schemas."""
    
    # =============================================================================
    # Jobs Table Schema
    # =============================================================================
    
    jobs_schema = {
        "table_name": "jobs",
        "columns": {
            "id": {
                "type": "uuid",
                "primary_key": True,
                "default": "uuid_generate_v4()",
                "nullable": False
            },
            "user_id": {
                "type": "text",
                "nullable": False,
                "description": "Firebase Auth user ID"
            },
            "job_type": {
                "type": "text",
                "nullable": False,
                "check": f"job_type IN ({', '.join([f\"'{t.value}'\" for t in JobType])})"
            },
            "status": {
                "type": "text",
                "nullable": False,
                "default": f"'{JobStatus.PENDING.value}'",
                "check": f"status IN ({', '.join([f\"'{s.value}'\" for s in JobStatus])})"
            },
            "title": {
                "type": "text",
                "nullable": False,
                "description": "Human-readable job title"
            },
            "description": {
                "type": "text",
                "nullable": True,
                "description": "Optional job description"
            },
            "file_name": {
                "type": "text",
                "nullable": True,
                "description": "Original uploaded file name"
            },
            "file_path": {
                "type": "text",
                "nullable": True,
                "description": "Storage path for uploaded file"
            },
            "file_size": {
                "type": "bigint",
                "nullable": True,
                "description": "File size in bytes"
            },
            "vessel_count": {
                "type": "integer",
                "nullable": True,
                "description": "Number of vessels in the dataset"
            },
            "progress": {
                "type": "integer",
                "default": 0,
                "check": "progress >= 0 AND progress <= 100",
                "description": "Processing progress percentage"
            },
            "results": {
                "type": "jsonb",
                "nullable": True,
                "description": "Job results in JSON format"
            },
            "error_message": {
                "type": "text",
                "nullable": True,
                "description": "Error message if job failed"
            },
            "configuration": {
                "type": "jsonb",
                "nullable": True,
                "description": "Job configuration parameters"
            },
            "created_at": {
                "type": "timestamptz",
                "default": "now()",
                "nullable": False
            },
            "updated_at": {
                "type": "timestamptz",
                "default": "now()",
                "nullable": False
            },
            "started_at": {
                "type": "timestamptz",
                "nullable": True,
                "description": "When processing started"
            },
            "completed_at": {
                "type": "timestamptz",
                "nullable": True,
                "description": "When processing completed"
            }
        },
        "indexes": [
            {"columns": ["user_id"], "type": "btree"},
            {"columns": ["status"], "type": "btree"},
            {"columns": ["created_at"], "type": "btree"},
            {"columns": ["user_id", "status"], "type": "btree"},
            {"columns": ["user_id", "created_at"], "type": "btree"}
        ],
        "constraints": [
            "CONSTRAINT valid_progress CHECK (progress >= 0 AND progress <= 100)",
            "CONSTRAINT valid_dates CHECK (created_at <= updated_at)",
            "CONSTRAINT completed_has_end_date CHECK (status != 'completed' OR completed_at IS NOT NULL)"
        ],
        "triggers": [
            "update_updated_at_trigger"
        ]
    }
    
    # =============================================================================
    # Vessels Table Schema
    # =============================================================================
    
    vessels_schema = {
        "table_name": "vessels",
        "columns": {
            "id": {
                "type": "uuid",
                "primary_key": True,
                "default": "uuid_generate_v4()",
                "nullable": False
            },
            "job_id": {
                "type": "uuid",
                "nullable": False,
                "foreign_key": "jobs(id)",
                "on_delete": "CASCADE"
            },
            "mmsi": {
                "type": "bigint",
                "nullable": True,
                "description": "Maritime Mobile Service Identity"
            },
            "imo": {
                "type": "bigint",
                "nullable": True,
                "description": "International Maritime Organization number"
            },
            "vessel_name": {
                "type": "text",
                "nullable": True,
                "description": "Vessel name from AIS data"
            },
            "call_sign": {
                "type": "text",
                "nullable": True,
                "description": "Vessel call sign"
            },
            "predicted_type": {
                "type": "text",
                "nullable": True,
                "check": f"predicted_type IN ({', '.join([f\"'{t.value}'\" for t in VesselType])})"
            },
            "confidence": {
                "type": "real",
                "nullable": True,
                "check": "confidence >= 0 AND confidence <= 1",
                "description": "Prediction confidence score"
            },
            "class_probabilities": {
                "type": "jsonb",
                "nullable": True,
                "description": "Probability scores for all classes"
            },
            "features": {
                "type": "jsonb",
                "nullable": True,
                "description": "Extracted features used for classification"
            },
            "raw_data": {
                "type": "jsonb",
                "nullable": True,
                "description": "Original AIS data points"
            },
            "data_points": {
                "type": "integer",
                "nullable": True,
                "description": "Number of AIS data points for this vessel"
            },
            "time_span_hours": {
                "type": "real",
                "nullable": True,
                "description": "Time span of data in hours"
            },
            "first_seen": {
                "type": "timestamptz",
                "nullable": True,
                "description": "First AIS timestamp"
            },
            "last_seen": {
                "type": "timestamptz",
                "nullable": True,
                "description": "Last AIS timestamp"
            },
            "created_at": {
                "type": "timestamptz",
                "default": "now()",
                "nullable": False
            }
        },
        "indexes": [
            {"columns": ["job_id"], "type": "btree"},
            {"columns": ["mmsi"], "type": "btree"},
            {"columns": ["imo"], "type": "btree"},
            {"columns": ["predicted_type"], "type": "btree"},
            {"columns": ["confidence"], "type": "btree"},
            {"columns": ["job_id", "mmsi"], "type": "btree"},
            {"columns": ["job_id", "predicted_type"], "type": "btree"}
        ],
        "constraints": [
            "CONSTRAINT valid_confidence CHECK (confidence IS NULL OR (confidence >= 0 AND confidence <= 1))",
            "CONSTRAINT valid_timestamps CHECK (first_seen IS NULL OR last_seen IS NULL OR first_seen <= last_seen)",
            "CONSTRAINT positive_data_points CHECK (data_points IS NULL OR data_points > 0)"
        ]
    }
    
    # =============================================================================
    # User Profiles Table Schema
    # =============================================================================
    
    user_profiles_schema = {
        "table_name": "user_profiles",
        "columns": {
            "id": {
                "type": "uuid",
                "primary_key": True,
                "default": "uuid_generate_v4()",
                "nullable": False
            },
            "user_id": {
                "type": "text",
                "nullable": False,
                "unique": True,
                "description": "Firebase Auth user ID"
            },
            "email": {
                "type": "text",
                "nullable": False,
                "unique": True
            },
            "display_name": {
                "type": "text",
                "nullable": True
            },
            "role": {
                "type": "text",
                "nullable": False,
                "default": f"'{UserRole.USER.value}'",
                "check": f"role IN ({', '.join([f\"'{r.value}'\" for r in UserRole])})"
            },
            "organization": {
                "type": "text",
                "nullable": True
            },
            "preferences": {
                "type": "jsonb",
                "nullable": True,
                "description": "User preferences and settings"
            },
            "usage_stats": {
                "type": "jsonb",
                "nullable": True,
                "description": "Usage statistics and quotas"
            },
            "last_login": {
                "type": "timestamptz",
                "nullable": True
            },
            "created_at": {
                "type": "timestamptz",
                "default": "now()",
                "nullable": False
            },
            "updated_at": {
                "type": "timestamptz",
                "default": "now()",
                "nullable": False
            }
        },
        "indexes": [
            {"columns": ["user_id"], "type": "btree", "unique": True},
            {"columns": ["email"], "type": "btree", "unique": True},
            {"columns": ["role"], "type": "btree"},
            {"columns": ["created_at"], "type": "btree"}
        ],
        "triggers": [
            "update_updated_at_trigger"
        ]
    }
    
    # =============================================================================
    # Job Analytics Table Schema
    # =============================================================================
    
    job_analytics_schema = {
        "table_name": "job_analytics",
        "columns": {
            "id": {
                "type": "uuid",
                "primary_key": True,
                "default": "uuid_generate_v4()",
                "nullable": False
            },
            "job_id": {
                "type": "uuid",
                "nullable": False,
                "foreign_key": "jobs(id)",
                "on_delete": "CASCADE"
            },
            "processing_time_seconds": {
                "type": "real",
                "nullable": True,
                "description": "Total processing time"
            },
            "memory_usage_mb": {
                "type": "real",
                "nullable": True,
                "description": "Peak memory usage"
            },
            "cpu_usage_percent": {
                "type": "real",
                "nullable": True,
                "description": "Average CPU usage"
            },
            "features_extracted": {
                "type": "integer",
                "nullable": True,
                "description": "Number of features extracted"
            },
            "model_version": {
                "type": "text",
                "nullable": True,
                "description": "ML model version used"
            },
            "accuracy_metrics": {
                "type": "jsonb",
                "nullable": True,
                "description": "Accuracy and performance metrics"
            },
            "error_details": {
                "type": "jsonb",
                "nullable": True,
                "description": "Detailed error information"
            },
            "created_at": {
                "type": "timestamptz",
                "default": "now()",
                "nullable": False
            }
        },
        "indexes": [
            {"columns": ["job_id"], "type": "btree"},
            {"columns": ["processing_time_seconds"], "type": "btree"},
            {"columns": ["created_at"], "type": "btree"}
        ]
    }

# =============================================================================
# Pydantic Models for Validation
# =============================================================================

class JobCreate(BaseModel):
    """Model for creating a new job."""
    
    user_id: str
    job_type: JobType
    title: str
    description: Optional[str] = None
    file_name: Optional[str] = None
    configuration: Optional[Dict[str, Any]] = None
    
    @validator('title')
    def title_must_not_be_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('Title cannot be empty')
        return v.strip()

class JobUpdate(BaseModel):
    """Model for updating job status."""
    
    status: Optional[JobStatus] = None
    progress: Optional[int] = Field(None, ge=0, le=100)
    results: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    vessel_count: Optional[int] = Field(None, ge=0)
    
    @validator('progress')
    def validate_progress(cls, v):
        if v is not None and (v < 0 or v > 100):
            raise ValueError('Progress must be between 0 and 100')
        return v

class VesselCreate(BaseModel):
    """Model for creating vessel classification result."""
    
    job_id: UUID
    mmsi: Optional[int] = None
    imo: Optional[int] = None
    vessel_name: Optional[str] = None
    call_sign: Optional[str] = None
    predicted_type: Optional[VesselType] = None
    confidence: Optional[float] = Field(None, ge=0, le=1)
    class_probabilities: Optional[Dict[str, float]] = None
    features: Optional[Dict[str, Any]] = None
    raw_data: Optional[Dict[str, Any]] = None
    data_points: Optional[int] = Field(None, ge=1)
    time_span_hours: Optional[float] = Field(None, ge=0)
    first_seen: Optional[datetime] = None
    last_seen: Optional[datetime] = None

class UserProfileCreate(BaseModel):
    """Model for creating user profile."""
    
    user_id: str
    email: str
    display_name: Optional[str] = None
    role: UserRole = UserRole.USER
    organization: Optional[str] = None
    preferences: Optional[Dict[str, Any]] = None

# =============================================================================
# Schema Validation Functions
# =============================================================================

def validate_schema(schema_name: str, data: Dict[str, Any]) -> bool:
    """Validate data against a schema."""
    try:
        schemas = DatabaseSchemas()
        schema = getattr(schemas, f"{schema_name}_schema", None)
        
        if not schema:
            logger.error(f"Schema '{schema_name}' not found")
            return False
        
        # Basic validation - check required fields
        columns = schema.get("columns", {})
        
        for column_name, column_def in columns.items():
            if not column_def.get("nullable", True) and column_name not in data:
                if "default" not in column_def:
                    logger.error(f"Required field '{column_name}' missing")
                    return False
        
        # Type validation could be added here
        logger.info(f"Schema validation passed for '{schema_name}'")
        return True
        
    except Exception as e:
        logger.error(f"Schema validation failed: {e}")
        return False

def get_create_table_sql(schema_name: str) -> str:
    """Generate CREATE TABLE SQL for a schema."""
    try:
        schemas = DatabaseSchemas()
        schema = getattr(schemas, f"{schema_name}_schema", None)
        
        if not schema:
            raise ValueError(f"Schema '{schema_name}' not found")
        
        table_name = schema["table_name"]
        columns = schema["columns"]
        
        # Build column definitions
        column_defs = []
        for column_name, column_def in columns.items():
            parts = [column_name, column_def["type"]]
            
            if column_def.get("primary_key"):
                parts.append("PRIMARY KEY")
            
            if not column_def.get("nullable", True):
                parts.append("NOT NULL")
            
            if "default" in column_def:
                parts.append(f"DEFAULT {column_def['default']}")
            
            if "check" in column_def:
                parts.append(f"CHECK ({column_def['check']})")
            
            column_defs.append(" ".join(parts))
        
        # Add constraints
        constraints = schema.get("constraints", [])
        all_definitions = column_defs + constraints
        
        sql = f"""
        CREATE TABLE IF NOT EXISTS {table_name} (
            {',\\n    '.join(all_definitions)}
        );
        """
        
        return sql
        
    except Exception as e:
        logger.error(f"Failed to generate CREATE TABLE SQL: {e}")
        raise

def get_create_indexes_sql(schema_name: str) -> List[str]:
    """Generate CREATE INDEX SQL statements for a schema."""
    try:
        schemas = DatabaseSchemas()
        schema = getattr(schemas, f"{schema_name}_schema", None)
        
        if not schema:
            raise ValueError(f"Schema '{schema_name}' not found")
        
        table_name = schema["table_name"]
        indexes = schema.get("indexes", [])
        
        sql_statements = []
        
        for i, index in enumerate(indexes):
            columns = index["columns"]
            index_type = index.get("type", "btree")
            unique = "UNIQUE " if index.get("unique") else ""
            
            index_name = f"idx_{table_name}_{'_'.join(columns)}_{i}"
            columns_str = ", ".join(columns)
            
            sql = f"CREATE {unique}INDEX IF NOT EXISTS {index_name} ON {table_name} USING {index_type} ({columns_str});"
            sql_statements.append(sql)
        
        return sql_statements
        
    except Exception as e:
        logger.error(f"Failed to generate CREATE INDEX SQL: {e}")
        raise

# =============================================================================
# Database Migration Utilities
# =============================================================================

def get_migration_sql() -> str:
    """Get complete migration SQL for all schemas."""
    try:
        schemas = DatabaseSchemas()
        schema_names = [
            'user_profiles',
            'jobs', 
            'vessels',
            'job_analytics'
        ]
        
        sql_parts = [
            "-- VESLINT Database Schema Migration",
            "-- Generated automatically",
            "",
            "-- Enable required extensions",
            "CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";",
            "CREATE EXTENSION IF NOT EXISTS \"pgcrypto\";",
            ""
        ]
        
        # Create tables
        for schema_name in schema_names:
            sql_parts.append(f"-- Create {schema_name} table")
            sql_parts.append(get_create_table_sql(schema_name))
            sql_parts.append("")
            
            # Create indexes
            index_sqls = get_create_indexes_sql(schema_name)
            if index_sqls:
                sql_parts.append(f"-- Create indexes for {schema_name}")
                sql_parts.extend(index_sqls)
                sql_parts.append("")
        
        # Add triggers
        sql_parts.extend([
            "-- Create update timestamp trigger function",
            """
            CREATE OR REPLACE FUNCTION update_updated_at_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = now();
                RETURN NEW;
            END;
            $$ language 'plpgsql';
            """,
            "",
            "-- Apply triggers to tables with updated_at columns",
            """
            CREATE TRIGGER update_jobs_updated_at 
                BEFORE UPDATE ON jobs 
                FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
            """,
            """
            CREATE TRIGGER update_user_profiles_updated_at 
                BEFORE UPDATE ON user_profiles 
                FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
            """
        ])
        
        return "\n".join(sql_parts)
        
    except Exception as e:
        logger.error(f"Failed to generate migration SQL: {e}")
        raise

# =============================================================================
# Exports
# =============================================================================

__all__ = [
    # Enums
    'JobStatus',
    'VesselType', 
    'JobType',
    'UserRole',
    
    # Schema container
    'DatabaseSchemas',
    
    # Pydantic models
    'JobCreate',
    'JobUpdate', 
    'VesselCreate',
    'UserProfileCreate',
    
    # Validation functions
    'validate_schema',
    'get_create_table_sql',
    'get_create_indexes_sql',
    'get_migration_sql'
]