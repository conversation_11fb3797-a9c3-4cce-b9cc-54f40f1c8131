-- Enable Real-time subscriptions for VESLINT
-- This enables live job progress monitoring in the frontend

-- Enable real-time for jobs table
ALTER PUBLICATION supabase_realtime ADD TABLE jobs;

-- Enable real-time for vessel classifications table
ALTER PUBLICATION supabase_realtime ADD TABLE vessel_classifications;

-- Create a real-time optimized view for job monitoring
CREATE OR REPLACE VIEW job_progress_realtime AS
SELECT 
    j.id,
    j.user_id,
    j.filename,
    j.status,
    j.progress,
    j.total_vessels,
    j.processed_vessels,
    j.error_message,
    j.created_at,
    j.updated_at,
    j.started_at,
    j.completed_at,
    
    -- Real-time metrics
    j.tug_count,
    j.fishing_count,
    j.pleasure_count,
    j.cargo_count,
    
    -- Calculated fields for real-time updates
    CASE 
        WHEN j.total_vessels > 0 THEN 
            ROUND((j.processed_vessels::DECIMAL / j.total_vessels) * 100, 1)
        ELSE 0 
    END as completion_percentage,
    
    -- Estimated time remaining (simple heuristic)
    CASE 
        WHEN j.status = 'processing' AND j.processed_vessels > 0 AND j.total_vessels > j.processed_vessels THEN
            ROUND(
                (EXTRACT(EPOCH FROM (NOW() - COALESCE(j.started_at, j.created_at))) / j.processed_vessels) 
                * (j.total_vessels - j.processed_vessels)
            )
        ELSE NULL
    END as estimated_seconds_remaining,
    
    -- Processing speed (vessels per minute)
    CASE 
        WHEN j.status IN ('processing', 'completed') AND j.started_at IS NOT NULL AND j.processed_vessels > 0 THEN
            ROUND(
                (j.processed_vessels::DECIMAL / GREATEST(1, EXTRACT(EPOCH FROM (COALESCE(j.completed_at, NOW()) - j.started_at)) / 60.0)), 
                2
            )
        ELSE NULL
    END as vessels_per_minute
    
FROM jobs j;

-- Enable real-time for the progress view
ALTER PUBLICATION supabase_realtime ADD TABLE job_progress_realtime;

-- Create a function to notify on job status changes
CREATE OR REPLACE FUNCTION notify_job_status_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Send notification for significant status changes
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        -- This will trigger real-time subscriptions
        PERFORM pg_notify(
            'job_status_changed',
            json_build_object(
                'job_id', NEW.id,
                'user_id', NEW.user_id,
                'old_status', OLD.status,
                'new_status', NEW.status,
                'progress', NEW.progress,
                'timestamp', NOW()
            )::text
        );
    END IF;
    
    -- Also notify on significant progress updates (every 10%)
    IF OLD.progress IS DISTINCT FROM NEW.progress AND 
       (NEW.progress % 10 = 0 OR NEW.progress = 100) THEN
        PERFORM pg_notify(
            'job_progress_updated',
            json_build_object(
                'job_id', NEW.id,
                'user_id', NEW.user_id,
                'progress', NEW.progress,
                'processed_vessels', NEW.processed_vessels,
                'total_vessels', NEW.total_vessels,
                'timestamp', NOW()
            )::text
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for job status notifications
CREATE TRIGGER job_status_change_trigger
    AFTER UPDATE ON jobs
    FOR EACH ROW
    EXECUTE FUNCTION notify_job_status_change();

-- Row Level Security (RLS) policies for real-time subscriptions
-- Users can only see their own jobs
ALTER TABLE jobs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own jobs" ON jobs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own jobs" ON jobs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own jobs" ON jobs
    FOR UPDATE USING (auth.uid() = user_id);

-- RLS for vessel classifications
ALTER TABLE vessel_classifications ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own vessel classifications" ON vessel_classifications
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM jobs 
            WHERE jobs.id = vessel_classifications.job_id 
            AND jobs.user_id = auth.uid()
        )
    );

CREATE POLICY "System can insert vessel classifications" ON vessel_classifications
    FOR INSERT WITH CHECK (true); -- Backend service will insert these

-- RLS for views
ALTER VIEW job_progress_realtime ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own job progress" ON job_progress_realtime
    FOR SELECT USING (auth.uid() = user_id);

-- Function to clean up old completed jobs (optional background task)
CREATE OR REPLACE FUNCTION cleanup_old_jobs()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete jobs older than 30 days that are completed or failed
    DELETE FROM jobs 
    WHERE (status IN ('completed', 'failed')) 
    AND created_at < NOW() - INTERVAL '30 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log the cleanup
    INSERT INTO job_cleanup_log (deleted_jobs_count, cleanup_date)
    VALUES (deleted_count, NOW());
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create cleanup log table
CREATE TABLE IF NOT EXISTS job_cleanup_log (
    id SERIAL PRIMARY KEY,
    deleted_jobs_count INTEGER NOT NULL,
    cleanup_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create an index for real-time queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_jobs_realtime_user_status 
ON jobs(user_id, status, updated_at DESC) 
WHERE status IN ('created', 'uploading', 'processing', 'extracting_features', 'classifying');

-- Success message
SELECT 'Real-time subscriptions enabled for VESLINT!' as message;

-- Usage instructions for frontend:
/*
Frontend JavaScript example for real-time subscriptions:

import { supabase } from './supabase-client'

// Subscribe to job progress updates
const subscription = supabase
  .channel('job-progress')
  .on('postgres_changes', 
    { 
      event: 'UPDATE', 
      schema: 'public', 
      table: 'jobs',
      filter: `user_id=eq.${userId}`
    },
    (payload) => {
      console.log('Job updated:', payload.new)
      // Update your React state here
    }
  )
  .subscribe()

// Subscribe to vessel classification results
const vesselSubscription = supabase
  .channel('vessel-results')
  .on('postgres_changes',
    {
      event: 'INSERT',
      schema: 'public', 
      table: 'vessel_classifications'
    },
    (payload) => {
      console.log('New vessel classified:', payload.new)
      // Update results in real-time
    }
  )
  .subscribe()
*/