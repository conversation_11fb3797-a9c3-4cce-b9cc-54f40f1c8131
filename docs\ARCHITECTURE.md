# VESLINT Architecture Overview

This document provides a high-level overview of the VESLINT system architecture, explaining the main components, data flow, and technology choices.

## System Architecture

VESLINT follows a modern, modular architecture with clear separation of concerns:

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Next.js        │     │  FastAPI        │     │  Hugging Face   │
│  Frontend       │◄───►│  Backend        │◄───►│  ML Model       │
│  (Vercel)       │     │  (Render)       │     │                 │
│                 │     │                 │     │                 │
└────────┬────────┘     └────────┬────────┘     └─────────────────┘
         │                       │
         │                       │
         ▼                       ▼
┌─────────────────────────────────────────────┐
│                                             │
│  Supabase                                   │
│  (PostgreSQL + Storage + Realtime)          │
│                                             │
└─────────────────────────────────────────────┘
```

### Key Components

1. **Frontend (Next.js on Vercel)**
   - User interface for uploading AIS data, managing jobs, and viewing results
   - Built with Next.js 13+ using the App Router
   - Typescript for type safety
   - Real-time updates via Supabase subscriptions
   - Authentication via Firebase Auth

2. **Backend (FastAPI on Render)**
   - RESTful API for data processing and ML inference
   - Job management and background processing
   - CSV validation and feature engineering
   - Integration with Hugging Face for ML inference
   - Connection to Supabase for data persistence

3. **Database (Supabase)**
   - PostgreSQL database for structured data
   - Storage for CSV files and results
   - Real-time subscriptions for live updates
   - Row Level Security for data protection

4. **ML Model (Hugging Face)**
   - Hosted vessel classification model
   - Inference API for predictions
   - Version control for model updates

## Data Flow

1. **User Authentication**
   - User authenticates via Firebase Auth
   - Frontend obtains authentication token
   - Backend validates token for protected endpoints

2. **Data Upload and Processing**
   ```
   User → Upload CSV → Backend Validation → Storage → Background Processing → ML Inference → Results Storage → Real-time Update to UI
   ```

3. **Results Retrieval**
   - Real-time job status updates via Supabase subscriptions
   - Results fetched from database when job completes
   - Visualizations rendered in the frontend

## Technology Stack Details

### Frontend

- **Framework**: Next.js 13+ with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **State Management**: React Hooks + Context
- **Authentication**: Firebase Auth
- **Real-time**: Supabase Realtime
- **Hosting**: Vercel

### Backend

- **Framework**: FastAPI
- **Language**: Python 3.9+
- **Async Processing**: Background tasks with asyncio
- **Data Processing**: Pandas, NumPy
- **ML Integration**: Hugging Face Inference API
- **Hosting**: Render

### Database

- **Engine**: PostgreSQL (via Supabase)
- **Storage**: Supabase Storage
- **Real-time**: Supabase Realtime
- **Security**: Row Level Security policies

### ML Pipeline

- **Hosting**: Hugging Face Model Hub
- **Model Type**: Random Forest Classifier
- **Feature Engineering**: Custom preprocessing pipeline
- **Inference**: REST API

## Security Considerations

- **Authentication**: Firebase Auth with JWT validation
- **Authorization**: Row Level Security in Supabase
- **Data Protection**: Secure storage with access controls
- **API Security**: CORS, rate limiting, and input validation

## Scalability

The architecture is designed to scale horizontally:

- **Frontend**: Vercel's edge network provides global distribution
- **Backend**: Render allows scaling based on demand
- **Database**: Supabase can scale to handle increased load
- **ML Inference**: Hugging Face provides scalable inference endpoints

## Development Workflow

1. Local development with Docker Compose
2. CI/CD with GitHub Actions
3. Staging and production environments
4. Automated testing and deployment

## Future Considerations

- Potential migration to containerized deployment on Kubernetes
- Enhanced ML pipeline with automated retraining
- Integration with additional maritime data sources
- Advanced analytics and reporting features 