#!/bin/bash

# =============================================================================
# VESLINT Quick Setup Script
# =============================================================================
# This script quickly sets up the VESLINT project for local testing
# by installing missing dependencies and starting services.

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

print_header() {
    echo -e "\n${PURPLE}================================================${NC}"
    echo -e "${WHITE}$1${NC}"
    echo -e "${PURPLE}================================================${NC}\n"
}

print_step() {
    echo -e "${CYAN}▶ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

check_command() {
    if command -v "$1" &> /dev/null; then
        print_success "$1 is installed"
        return 0
    else
        print_error "$1 is not installed"
        return 1
    fi
}

main() {
    print_header "VESLINT Quick Setup"
    
    # Check prerequisites
    print_step "Checking prerequisites..."
    
    local missing_tools=()
    
    if ! check_command "node"; then
        missing_tools+=("node")
    fi
    
    if ! check_command "npm"; then
        missing_tools+=("npm")
    fi
    
    if ! check_command "python3"; then
        missing_tools+=("python3")
    fi
    
    if ! check_command "docker"; then
        missing_tools+=("docker")
    fi
    
    if ! check_command "docker-compose"; then
        missing_tools+=("docker-compose")
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        print_error "Missing required tools: ${missing_tools[*]}"
        print_info "Please install missing tools and run this script again"
        exit 1
    fi
    
    print_success "All prerequisites are installed"
    
    # Install frontend dependencies
    print_step "Installing frontend dependencies..."
    cd frontend
    
    if [ -f "package.json" ]; then
        npm install
        print_success "Frontend dependencies installed"
    else
        print_error "Frontend package.json not found"
        exit 1
    fi
    
    cd ..
    
    # Install backend dependencies
    print_step "Setting up Python environment..."
    
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        print_success "Python virtual environment created"
    else
        print_info "Python virtual environment already exists"
    fi
    
    # Activate virtual environment
    source venv/bin/activate || source venv/Scripts/activate
    
    print_step "Installing backend dependencies..."
    pip install --upgrade pip
    pip install -r backend/requirements.txt
    print_success "Backend dependencies installed"
    
    # Install workspace dependencies
    print_step "Installing workspace dependencies..."
    if [ -f "package.json" ]; then
        npm install
        print_success "Workspace dependencies installed"
    fi
    
    print_header "Setup Complete!"
    
    print_success "VESLINT is ready for local testing!"
    echo ""
    print_info "Choose your testing approach:"
    echo ""
    echo -e "${WHITE}Option 1: Docker (Recommended)${NC}"
    echo -e "  docker-compose up -d"
    echo -e "  # Wait 2-3 minutes for services to start"
    echo -e "  # Access: http://localhost:3000"
    echo ""
    echo -e "${WHITE}Option 2: Manual Development${NC}"
    echo -e "  # Terminal 1 - Backend:"
    echo -e "  cd backend && source ../venv/bin/activate"
    echo -e "  uvicorn src.main:app --reload --host 0.0.0.0 --port 8000"
    echo ""
    echo -e "  # Terminal 2 - Frontend:"
    echo -e "  cd frontend && npm run dev"
    echo ""
    echo -e "${WHITE}Option 3: Run Tests${NC}"
    echo -e "  npm run test"
    echo ""
    print_info "See LOCAL_TESTING_GUIDE.md for detailed instructions"
    print_success "Happy coding! 🚀"
}

# Handle script arguments
case "${1:-}" in
    "--help"|"-h")
        echo "VESLINT Quick Setup Script"
        echo "Usage: $0 [--help]"
        echo ""
        echo "This script quickly sets up VESLINT for local testing"
        echo "by installing all necessary dependencies."
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
