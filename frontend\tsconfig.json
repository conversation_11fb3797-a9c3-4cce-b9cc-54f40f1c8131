{
  "compilerOptions": {
    /* Language and Environment */
    "target": "es2022",
    "lib": ["dom", "dom.iterable", "es6", "es2022"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    
    /* Modules */
    "module": "esnext",
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": false,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    
    /* Type Checking */
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitReturns": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": false,
    "noUncheckedIndexedAccess": false,
    "exactOptionalPropertyTypes": false,
    
    /* Interop Constraints */
    "verbatimModuleSyntax": false,
    
    /* JavaScript Support */
    "checkJs": false,
    
    /* JSX */
    "jsx": "preserve",
    "jsxImportSource": "react",
    
    /* Emit */
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    
    /* Path Mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/pages/*": ["./src/app/*"],
      "@/lib/*": ["./src/lib/*"],
      "@/utils/*": ["./src/utils/*"],
      "@/hooks/*": ["./src/hooks/*"],
      "@/types/*": ["./src/types/*"],
      "@/styles/*": ["./src/styles/*"],
      "@/public/*": ["./public/*"]
    }
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts",
    "src/**/*",
    "pages/**/*",
    "components/**/*",
    "lib/**/*",
    "utils/**/*",
    "hooks/**/*",
    "types/**/*"
  ],
  "exclude": [
    "node_modules",
    ".next",
    "out",
    "dist",
    "build",
    "coverage",
    "**/*.test.ts",
    "**/*.test.tsx",
    "**/*.spec.ts",
    "**/*.spec.tsx"
  ],
  "ts-node": {
    "compilerOptions": {
      "module": "commonjs"
    }
  }
}