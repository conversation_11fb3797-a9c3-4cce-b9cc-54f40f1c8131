#!/bin/bash

# VESLINT Backend Deployment Script for Render
# Deploys your trained model and FastAPI backend to Render for FREE

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check requirements
check_requirements() {
    log_info "Checking deployment requirements..."
    
    # Check if we're in the right directory
    if [ ! -f "backend/src/main.py" ]; then
        log_error "backend/src/main.py not found. Please run this script from the project root directory."
        exit 1
    fi
    
    # Check if model file exists
    if [ ! -f "backend/src/assets/extreme_maritime_classifier.joblib" ]; then
        log_error "Trained model file not found at backend/src/assets/extreme_maritime_classifier.joblib"
        log_info "Please copy your trained model file to backend/src/assets/"
        exit 1
    fi
    
    # Check required tools
    if ! command -v git &> /dev/null; then
        log_error "Git is required but not installed"
        exit 1
    fi
    
    log_success "Requirements check passed"
}

# Setup environment variables template
setup_env_template() {
    log_info "Creating environment variables template..."
    
    cat > backend/.env.render << 'EOF'
# VESLINT Backend Environment Variables for Render
# Copy these to your Render service environment variables

# Application Settings
ENVIRONMENT=production
PYTHONPATH=./src
LOG_LEVEL=INFO

# Supabase Configuration (REQUIRED)
SUPABASE_URL=your_supabase_url_here
SUPABASE_SERVICE_KEY=your_supabase_service_key_here
SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Firebase Authentication (REQUIRED)
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_CREDENTIALS_JSON=your_base64_encoded_credentials_json

# Optional: Performance Optimization
MALLOC_ARENA_MAX=2
MALLOC_MMAP_THRESHOLD_=131072
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1

# Optional: Monitoring
SENTRY_DSN=your_sentry_dsn_if_you_want_error_tracking
EOF

    log_success "Environment template created at backend/.env.render"
    log_warning "You'll need to set these variables in the Render dashboard"
}

# Validate model file
validate_model() {
    log_info "Validating trained model file..."
    
    cd backend
    
    # Create a simple validation script
    cat > validate_model.py << 'EOF'
import sys
import os
sys.path.append('./src')

try:
    import joblib
    import numpy as np
    from utils.constants import EXPECTED_FEATURES, CLASS_NAMES
    
    # Load the model
    model_path = './src/assets/extreme_maritime_classifier.joblib'
    print(f"Loading model from: {model_path}")
    
    model_data = joblib.load(model_path)
    
    if isinstance(model_data, dict):
        model = model_data.get('model')
        features = model_data.get('feature_columns', EXPECTED_FEATURES)
    else:
        model = model_data
        features = EXPECTED_FEATURES
    
    print(f"✅ Model loaded successfully")
    print(f"✅ Model type: {type(model).__name__}")
    print(f"✅ Expected features: {len(features)}")
    
    # Test prediction
    if hasattr(model, 'predict'):
        test_input = np.random.rand(1, len(features))
        pred = model.predict(test_input)
        print(f"✅ Test prediction successful: {pred}")
        
        if hasattr(model, 'predict_proba'):
            proba = model.predict_proba(test_input)
            print(f"✅ Probability prediction available: {proba.shape}")
    
    print("✅ Model validation passed!")
    
except Exception as e:
    print(f"❌ Model validation failed: {e}")
    sys.exit(1)
EOF

    # Run validation
    if python3 validate_model.py; then
        log_success "Model validation passed"
        rm validate_model.py
    else
        log_error "Model validation failed"
        rm validate_model.py
        exit 1
    fi
    
    cd ..
}

# Setup Git repository for Render deployment
setup_git_repo() {
    log_info "Setting up Git repository for Render deployment..."
    
    # Initialize git if not already done
    if [ ! -d ".git" ]; then
        git init
        log_info "Git repository initialized"
    fi
    
    # Create .gitignore if not exists
    if [ ! -f ".gitignore" ]; then
        cat > .gitignore << 'EOF'
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.env
.env.*
.venv/

# IDEs
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log

# Local environment files
backend/.env.render
backend/.env.local

# Large files that shouldn't be in git
*.zip
*.tar.gz

# Frontend (if you want to deploy separately)
frontend/node_modules/
frontend/dist/
frontend/.next/
EOF
        log_info "Created .gitignore"
    fi
    
    # Add all files
    git add .
    
    # Check if there are changes to commit
    if git diff --staged --quiet; then
        log_info "No changes to commit"
    else
        git commit -m "Prepare VESLINT backend for Render deployment"
        log_success "Changes committed to Git"
    fi
}

# Create Render deployment guide
create_render_guide() {
    log_info "Creating Render deployment guide..."
    
    cat > deployment/RENDER_DEPLOYMENT.md << 'EOF'
# VESLINT Backend Deployment on Render (FREE)

## Step-by-Step Deployment Guide

### 1. Create Render Account
- Go to [render.com](https://render.com)
- Sign up with your GitHub account (recommended)

### 2. Connect Your Repository
- Push your code to GitHub
- In Render dashboard, click "New" → "Web Service"
- Connect your GitHub repository
- Select the VESLINT repository

### 3. Configure Service Settings
```
Name: veslint-api
Runtime: Python 3
Build Command: pip install -r requirements.txt
Start Command: gunicorn src.main:app -w 1 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:$PORT --timeout 120
```

### 4. Set Environment Variables
Go to Environment tab and add:

```bash
# Application
ENVIRONMENT=production
PYTHONPATH=./src
LOG_LEVEL=INFO

# Supabase (get from your Supabase dashboard)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your_service_key_here
SUPABASE_ANON_KEY=your_anon_key_here

# Firebase (get from Firebase console)
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_CREDENTIALS_JSON=your_base64_encoded_json

# Performance optimization
MALLOC_ARENA_MAX=2
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1
```

### 5. Deploy
- Click "Create Web Service"
- Render will automatically deploy your service
- First deployment takes ~3-5 minutes
- Your API will be available at: `https://veslint-api.onrender.com`

### 6. Test Deployment
```bash
# Health check
curl https://veslint-api.onrender.com/health

# API documentation
# Visit: https://veslint-api.onrender.com/docs
```

### 7. Custom Domain (Optional)
- In Render dashboard, go to Settings → Custom Domains
- Add: api.veslint.com
- Update your Cloudflare DNS with the CNAME record

## Free Tier Limits
- 750 hours/month compute time
- 512MB RAM
- Shared CPU
- Auto-sleep after 15 minutes of inactivity
- 15-30 second cold start time

## Monitoring
- View logs in Render dashboard
- Monitor uptime and performance
- Set up alerts for service health

## Troubleshooting
- Check logs in Render dashboard
- Verify environment variables are set correctly
- Ensure model file is included in deployment
- Test endpoints using the /docs interface
EOF

    log_success "Render deployment guide created at deployment/RENDER_DEPLOYMENT.md"
}

# Create health check script
create_health_check() {
    log_info "Creating health check script..."
    
    cat > deployment/check_backend_health.sh << 'EOF'
#!/bin/bash

# Health check script for deployed backend
BACKEND_URL="${1:-https://veslint-api.onrender.com}"

echo "Checking backend health at: $BACKEND_URL"

# Basic health check
echo "1. Basic health check..."
if curl -s "$BACKEND_URL/health" | grep -q "healthy"; then
    echo "✅ Basic health check passed"
else
    echo "❌ Basic health check failed"
    exit 1
fi

# Detailed health check
echo "2. Detailed health check..."
if curl -s "$BACKEND_URL/health/detailed" | grep -q "healthy"; then
    echo "✅ Detailed health check passed"
else
    echo "⚠️ Detailed health check shows issues"
fi

# API documentation
echo "3. API documentation check..."
if curl -s "$BACKEND_URL/docs" | grep -q "swagger"; then
    echo "✅ API documentation accessible"
else
    echo "⚠️ API documentation may not be working"
fi

# Model info
echo "4. ML model check..."
if curl -s "$BACKEND_URL/api/v1/vessels/model-info" | grep -q "model"; then
    echo "✅ ML model loaded and accessible"
else
    echo "❌ ML model not loaded properly"
fi

echo ""
echo "Health check completed!"
echo "Visit $BACKEND_URL/docs for full API documentation"
EOF

    chmod +x deployment/check_backend_health.sh
    log_success "Health check script created"
}

# Main deployment process
main() {
    log_info "Starting VESLINT backend deployment preparation..."
    
    # Create deployment directory if it doesn't exist
    mkdir -p deployment
    
    # Run all steps
    check_requirements
    validate_model
    setup_env_template
    setup_git_repo
    create_render_guide
    create_health_check
    
    log_success "Backend deployment preparation completed!"
    echo ""
    log_info "Next steps:"
    echo "1. Push your code to GitHub: git push origin main"
    echo "2. Follow the guide in deployment/RENDER_DEPLOYMENT.md"
    echo "3. Set up your Supabase database using the database/ migrations"
    echo "4. Configure environment variables in Render dashboard"
    echo "5. Test your deployed API using deployment/check_backend_health.sh"
    echo ""
    log_info "Your trained model will be deployed and ready for FREE inference!"
}

# Run the deployment preparation
main "$@"