# Cloudflare Domain Setup for VESLINT.COM

## 🎯 Goal: Professional Domain Setup (100% Free)

Configure your **veslint.com** domain on Cloudflare to work with your VESLINT deployment:
- **Main site**: `https://veslint.com` → Vercel frontend
- **API**: `https://api.veslint.com` → Render backend
- **Free SSL certificates** automatically managed
- **CDN and performance optimization** included

## 🚀 Step 1: Cloudflare DNS Configuration

### Login to Cloudflare Dashboard

1. Go to [dash.cloudflare.com](https://dash.cloudflare.com)
2. Select your **veslint.com** domain
3. Go to **DNS** → **Records**

### Add DNS Records

Add these DNS records exactly as shown:

#### **Record 1: Main Website (Frontend)**
```
Type: CNAME
Name: @
Target: your-vercel-project.vercel.app
Proxy Status: 🟠 Proxied (Orange Cloud ON)
TTL: Auto
```

#### **Record 2: WWW Redirect**
```
Type: CNAME
Name: www
Target: your-vercel-project.vercel.app
Proxy Status: 🟠 Proxied (Orange Cloud ON)
TTL: Auto
```

#### **Record 3: API Subdomain (Backend)**
```
Type: CNAME
Name: api
Target: veslint-api.onrender.com
Proxy Status: 🟠 Proxied (Orange Cloud ON)
TTL: Auto
```

#### **Record 4: Optional - Docs/Admin**
```
Type: CNAME
Name: docs
Target: your-vercel-project.vercel.app
Proxy Status: 🟠 Proxied (Orange Cloud ON)
TTL: Auto
```

### Example DNS Table
| Type | Name | Target | Proxy | TTL |
|------|------|--------|-------|-----|
| CNAME | @ | veslint-frontend.vercel.app | 🟠 | Auto |
| CNAME | www | veslint-frontend.vercel.app | 🟠 | Auto |
| CNAME | api | veslint-api.onrender.com | 🟠 | Auto |
| CNAME | docs | veslint-frontend.vercel.app | 🟠 | Auto |

## 🔒 Step 2: SSL/TLS Configuration

### Enable Full SSL Encryption

1. Go to **SSL/TLS** → **Overview**
2. Set encryption mode to: **Full (strict)**
3. This ensures end-to-end encryption

### Configure SSL Settings

1. **SSL/TLS** → **Edge Certificates**
2. Enable these settings:
   - ✅ **Always Use HTTPS**: ON
   - ✅ **HTTP Strict Transport Security (HSTS)**: Enable
   - ✅ **Minimum TLS Version**: 1.2
   - ✅ **Automatic HTTPS Rewrites**: ON

### HSTS Configuration
```
Max Age Header: 12 months
Include Subdomains: ON
Preload: ON
No-Sniff Header: ON
```

## ⚡ Step 3: Performance Optimization

### Speed Settings

Go to **Speed** → **Optimization**:

1. **Auto Minify**:
   - ✅ JavaScript: ON
   - ✅ CSS: ON
   - ✅ HTML: ON

2. **Brotli Compression**: ON

3. **Rocket Loader**: OFF (can break React apps)

### Caching Configuration

Go to **Caching** → **Configuration**:

1. **Caching Level**: Standard
2. **Browser Cache TTL**: 4 hours
3. **Always Online**: ON

### Cache Rules

Create custom cache rules:

**Rule 1: API Caching**
```
Field: Hostname
Operator: equals
Value: api.veslint.com

Then:
- Cache Level: Bypass
- Edge Cache TTL: Do not cache
```

**Rule 2: Static Assets Caching**
```
Field: File Extension
Operator: equals
Value: css js png jpg jpeg gif svg ico woff woff2

Then:
- Cache Level: Cache Everything
- Edge Cache TTL: 30 days
- Browser Cache TTL: 7 days
```

## 🛡️ Step 4: Security Configuration

### Firewall Rules

Go to **Security** → **WAF**:

1. **Security Level**: Medium
2. **Challenge Passage**: 30 minutes
3. **Browser Integrity Check**: ON

### Custom Firewall Rules

Create these rules for enhanced security:

**Rule 1: Block Suspicious API Requests**
```
Field: Hostname
Operator: equals
Value: api.veslint.com

AND

Field: Request Method
Operator: equals
Value: GET

AND

Field: URI Path
Operator: contains
Value: /api/v1/

Then: Block
```

**Rule 2: Rate Limiting for API**
```
Field: Hostname
Operator: equals
Value: api.veslint.com

Then: Rate Limit
- Requests: 100 per minute
- Action: Block for 1 hour
```

### Bot Fight Mode

1. **Security** → **Bots**
2. Enable **Bot Fight Mode**: ON
3. **Super Bot Fight Mode**: Consider upgrading for advanced protection

## 🔧 Step 5: Page Rules (Legacy - Free Plan)

If you have page rules available, create these:

**Rule 1: API Bypass Cache**
```
URL Pattern: api.veslint.com/*
Settings:
- Cache Level: Bypass
- Disable Apps
- Disable Performance
```

**Rule 2: Always Use HTTPS**
```
URL Pattern: http://veslint.com/*
Settings:
- Always Use HTTPS
```

**Rule 3: Cache Everything for Static Assets**
```
URL Pattern: veslint.com/*.{js,css,png,jpg,gif,svg,ico}
Settings:
- Cache Level: Cache Everything
- Edge Cache TTL: 30 days
```

## 🎯 Step 6: Connect to Deployment Platforms

### Configure Vercel Custom Domain

1. **Vercel Dashboard** → Your Project → **Settings** → **Domains**
2. Add domain: `veslint.com`
3. Add domain: `www.veslint.com`
4. Vercel will provide verification requirements
5. Add any required DNS records in Cloudflare

### Configure Render Custom Domain

1. **Render Dashboard** → Your Service → **Settings** → **Custom Domains**
2. Add domain: `api.veslint.com`
3. Render will verify the CNAME record automatically

## 🧪 Step 7: Test Your Configuration

### DNS Propagation Check

```bash
# Check DNS propagation
dig veslint.com
dig www.veslint.com
dig api.veslint.com

# Check SSL certificates
curl -I https://veslint.com
curl -I https://api.veslint.com
```

### Online Tools

Use these tools to verify your setup:
- [DNS Checker](https://dnschecker.org/)
- [SSL Labs Test](https://www.ssllabs.com/ssltest/)
- [GTmetrix](https://gtmetrix.com/) for performance
- [Cloudflare Diagnostic Center](https://support.cloudflare.com/hc/en-us/articles/200169566)

### Manual Testing

1. **Frontend Test**:
   ```bash
   curl -I https://veslint.com
   # Should return 200 OK
   ```

2. **API Test**:
   ```bash
   curl https://api.veslint.com/health
   # Should return {"status":"healthy"}
   ```

3. **SSL Test**:
   ```bash
   curl -I https://veslint.com | grep -i ssl
   # Should show SSL certificate info
   ```

4. **Redirect Test**:
   ```bash
   curl -I http://veslint.com
   # Should redirect to https://veslint.com
   ```

## 📊 Step 8: Analytics & Monitoring

### Cloudflare Analytics

1. **Analytics & Logs** → **Web Analytics**
2. Enable analytics for your domain
3. Add analytics to your frontend:

```html
<!-- Add to your Next.js layout -->
<script defer src='https://static.cloudflareinsights.com/beacon.min.js' data-cf-beacon='{"token": "your-token-here"}'></script>
```

### Custom Analytics Dashboard

Create custom rules to track:
- API response times
- Geographic distribution of users
- Security threats blocked
- Cache hit ratios

## ⚙️ Step 9: Advanced Configuration (Optional)

### Load Balancing (Pro Feature)

If you upgrade to Pro, configure load balancing:
- Primary: Render backend
- Fallback: Static maintenance page

### Workers (Optional Enhancement)

Create a Cloudflare Worker for advanced functionality:

```javascript
// Example: API rate limiting worker
addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

async function handleRequest(request) {
  const url = new URL(request.url)
  
  // Add custom logic here
  if (url.hostname === 'api.veslint.com') {
    // Custom API handling
    return fetch(request)
  }
  
  return fetch(request)
}
```

## 🚨 Troubleshooting

### Common Issues

**Issue: "Too many redirects"**
- Check SSL/TLS mode is set to "Full (strict)"
- Verify CNAME targets are correct

**Issue: "DNS_PROBE_FINISHED_NXDOMAIN"**
- Wait for DNS propagation (up to 24 hours)
- Check CNAME records are correct

**Issue: "SSL certificate errors"**
- Wait for Cloudflare to issue certificates (up to 24 hours)
- Check that proxy (orange cloud) is enabled

**Issue: "API not accessible"**
- Verify Render service is running
- Check CNAME record for api subdomain
- Ensure no conflicting page rules

### Debug Commands

```bash
# Check current DNS resolution
nslookup veslint.com
nslookup api.veslint.com

# Check SSL certificate
openssl s_client -connect veslint.com:443 -servername veslint.com

# Test API endpoint
curl -v https://api.veslint.com/health

# Check headers
curl -I https://veslint.com
```

## ✅ Configuration Complete!

Your domain is now configured with:

- ✅ **Professional domain**: veslint.com
- ✅ **SSL certificates**: Automatically managed
- ✅ **CDN optimization**: Global performance
- ✅ **Security protection**: DDoS protection, WAF
- ✅ **API subdomain**: api.veslint.com
- ✅ **Caching optimization**: Fast loading times
- ✅ **Analytics tracking**: User insights

### Final URLs:
- **Frontend**: https://veslint.com
- **API**: https://api.veslint.com
- **API Docs**: https://api.veslint.com/docs
- **Health Check**: https://api.veslint.com/health

## 🎉 Success!

Your VESLINT platform is now live on your professional domain with enterprise-grade performance, security, and reliability - all for **FREE**! 🚢⚓

## 📈 Next Steps

1. **Monitor performance** in Cloudflare analytics
2. **Set up uptime monitoring** for critical endpoints
3. **Configure alerting** for service issues
4. **Optimize caching** based on usage patterns
5. **Consider Cloudflare Pro** for advanced features as you scale