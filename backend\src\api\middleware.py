"""
API Middleware Configuration
CORS, authentication, logging, and request handling
"""

import logging
import time
import uuid
from typing import Callable
from fastapi import Fast<PERSON><PERSON>, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import J<PERSON>NResponse
import os

logger = logging.getLogger(__name__)

def setup_middleware(app: FastAPI):
    """Configure all middleware for the FastAPI application"""
    
    # Trusted Host Middleware (security)
    allowed_hosts = os.getenv("ALLOWED_HOSTS", "*").split(",")
    if allowed_hosts != ["*"]:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=allowed_hosts
        )
    
    # CORS Middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=_get_cors_origins(),
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=[
            "Accept",
            "Accept-Language",
            "Content-Language",
            "Content-Type",
            "Authorization",
            "X-Requested-With",
            "X-Request-ID"
        ],
        expose_headers=["X-Request-ID", "X-Processing-Time"]
    )
    
    # Request ID and timing middleware
    app.add_middleware(RequestTimingMiddleware)
    
    logger.info("Middleware configured successfully")

def _get_cors_origins() -> list:
    """Get CORS origins from environment"""
    environment = os.getenv("ENVIRONMENT", "production")
    
    if environment == "development":
        return [
            "http://localhost:3000",
            "http://localhost:5173",
            "http://localhost:8080",
            "http://127.0.0.1:3000",
            "http://127.0.0.1:5173"
        ]
    else:
        # Production origins
        cors_origins = os.getenv("CORS_ORIGINS", "").split(",")
        origins = [origin.strip() for origin in cors_origins if origin.strip()]
        
        # Add default production domains
        default_origins = [
            "https://veslint.com",
            "https://www.veslint.com",
            "https://*.vercel.app",
            "https://*.netlify.app"
        ]
        
        return origins + default_origins

class RequestTimingMiddleware:
    """Middleware to add request ID and timing"""
    
    def __init__(self, app: FastAPI):
        self.app = app
    
    async def __call__(self, request: Request, call_next: Callable) -> Response:
        # Generate request ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # Add to headers for logging
        start_time = time.time()
        
        # Log request start
        logger.info(
            f"Request started: {request.method} {request.url}",
            extra={
                "request_id": request_id,
                "method": request.method,
                "url": str(request.url),
                "client_ip": request.client.host if request.client else "unknown"
            }
        )
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate processing time
            processing_time = time.time() - start_time
            
            # Add headers to response
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Processing-Time"] = f"{processing_time:.3f}s"
            
            # Log request completion
            logger.info(
                f"Request completed: {request.method} {request.url} - {response.status_code}",
                extra={
                    "request_id": request_id,
                    "method": request.method,
                    "url": str(request.url),
                    "status_code": response.status_code,
                    "processing_time": processing_time
                }
            )
            
            return response
            
        except Exception as e:
            # Calculate processing time for errors too
            processing_time = time.time() - start_time
            
            # Log error
            logger.error(
                f"Request failed: {request.method} {request.url} - {str(e)}",
                extra={
                    "request_id": request_id,
                    "method": request.method,
                    "url": str(request.url),
                    "error": str(e),
                    "processing_time": processing_time
                },
                exc_info=True
            )
            
            # Return error response
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Internal server error",
                    "message": "An unexpected error occurred",
                    "request_id": request_id
                },
                headers={
                    "X-Request-ID": request_id,
                    "X-Processing-Time": f"{processing_time:.3f}s"
                }
            )

class RequestLoggingMiddleware:
    """Enhanced request logging middleware"""
    
    def __init__(self, app: FastAPI):
        self.app = app
    
    async def __call__(self, request: Request, call_next: Callable) -> Response:
        # Skip logging for health checks to reduce noise
        if request.url.path in ["/health", "/health/live", "/health/ready"]:
            return await call_next(request)
        
        request_id = getattr(request.state, "request_id", "unknown")
        
        # Log request details
        request_details = {
            "request_id": request_id,
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "query_params": dict(request.query_params),
            "user_agent": request.headers.get("user-agent", "unknown"),
            "content_type": request.headers.get("content-type"),
            "content_length": request.headers.get("content-length"),
            "client_ip": request.client.host if request.client else "unknown"
        }
        
        # Add authentication info if available
        auth_header = request.headers.get("authorization")
        if auth_header:
            request_details["has_auth"] = True
            # Don't log the actual token for security
        
        logger.info("Processing request", extra=request_details)
        
        # Process request
        response = await call_next(request)
        
        # Log response details
        response_details = {
            "request_id": request_id,
            "status_code": response.status_code,
            "response_size": response.headers.get("content-length", "unknown")
        }
        
        if response.status_code >= 400:
            logger.warning("Request completed with error", extra=response_details)
        else:
            logger.info("Request completed successfully", extra=response_details)
        
        return response

class RateLimitingMiddleware:
    """Simple rate limiting middleware for free tier protection"""
    
    def __init__(self, app: FastAPI, requests_per_minute: int = 60):
        self.app = app
        self.requests_per_minute = requests_per_minute
        self.request_counts = {}  # In production, use Redis
        self.window_start = {}
    
    async def __call__(self, request: Request, call_next: Callable) -> Response:
        # Skip rate limiting for health checks
        if request.url.path.startswith("/health"):
            return await call_next(request)
        
        # Get client identifier
        client_ip = request.client.host if request.client else "unknown"
        auth_header = request.headers.get("authorization")
        
        # Use auth token if available, otherwise IP
        client_id = auth_header[:20] if auth_header else client_ip
        
        current_time = time.time()
        
        # Initialize tracking for new clients
        if client_id not in self.request_counts:
            self.request_counts[client_id] = 0
            self.window_start[client_id] = current_time
        
        # Reset window if needed (1 minute windows)
        if current_time - self.window_start[client_id] >= 60:
            self.request_counts[client_id] = 0
            self.window_start[client_id] = current_time
        
        # Check rate limit
        if self.request_counts[client_id] >= self.requests_per_minute:
            logger.warning(
                f"Rate limit exceeded for client {client_id}",
                extra={"client_id": client_id, "requests": self.request_counts[client_id]}
            )
            
            return JSONResponse(
                status_code=429,
                content={
                    "error": "Rate limit exceeded",
                    "message": f"Maximum {self.requests_per_minute} requests per minute allowed",
                    "retry_after": 60 - int(current_time - self.window_start[client_id])
                },
                headers={"Retry-After": "60"}
            )
        
        # Increment counter
        self.request_counts[client_id] += 1
        
        return await call_next(request)

class SecurityHeadersMiddleware:
    """Add security headers to responses"""
    
    def __init__(self, app: FastAPI):
        self.app = app
    
    async def __call__(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        # Only add HSTS in production with HTTPS
        if os.getenv("ENVIRONMENT") == "production":
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        return response