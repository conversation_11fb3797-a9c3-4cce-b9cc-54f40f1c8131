#!/bin/bash

# =============================================================================
# VESLINT Deployment Testing Script
# =============================================================================
# Comprehensive testing script that validates the entire VESLINT deployment
# including frontend, backend, database connections, and all integrations.
# This ensures the system works end-to-end after deployment.
# =============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test() {
    echo -e "${PURPLE}[TEST]${NC} $1"
}

log_result() {
    echo -e "${CYAN}[RESULT]${NC} $1"
}

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &> /dev/null && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Test configuration
ENVIRONMENT="${1:-production}"
TIMEOUT="${2:-30}"
VERBOSE="${3:-false}"

# URLs to test (can be overridden by environment variables)
FRONTEND_URL="${FRONTEND_URL:-https://veslint.com}"
BACKEND_URL="${BACKEND_URL:-https://veslint-api.onrender.com}"
API_BASE_URL="${API_BASE_URL:-$BACKEND_URL/api/v1}"

# Test results
declare -A test_results
total_tests=0
passed_tests=0
failed_tests=0
warnings=0

# =============================================================================
# Helper Functions
# =============================================================================

run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="${3:-0}"
    
    ((total_tests++))
    log_test "Running: $test_name"
    
    if [ "$VERBOSE" = "true" ]; then
        log_info "Command: $test_command"
    fi
    
    # Run the test command
    if eval "$test_command" > /dev/null 2>&1; then
        local result=$?
        if [ $result -eq $expected_result ]; then
            log_success "✓ $test_name"
            test_results["$test_name"]="PASS"
            ((passed_tests++))
            return 0
        else
            log_error "✗ $test_name (unexpected result: $result)"
            test_results["$test_name"]="FAIL"
            ((failed_tests++))
            return 1
        fi
    else
        log_error "✗ $test_name (command failed)"
        test_results["$test_name"]="FAIL"
        ((failed_tests++))
        return 1
    fi
}

run_test_with_output() {
    local test_name="$1"
    local test_command="$2"
    local expected_pattern="${3:-.*}"
    
    ((total_tests++))
    log_test "Running: $test_name"
    
    if [ "$VERBOSE" = "true" ]; then
        log_info "Command: $test_command"
    fi
    
    # Run the test command and capture output
    local output
    if output=$(eval "$test_command" 2>&1); then
        if echo "$output" | grep -qE "$expected_pattern"; then
            log_success "✓ $test_name"
            test_results["$test_name"]="PASS"
            ((passed_tests++))
            
            if [ "$VERBOSE" = "true" ]; then
                log_info "Output: $output"
            fi
            return 0
        else
            log_error "✗ $test_name (output doesn't match pattern: $expected_pattern)"
            test_results["$test_name"]="FAIL"
            ((failed_tests++))
            log_info "Actual output: $output"
            return 1
        fi
    else
        log_error "✗ $test_name (command failed)"
        test_results["$test_name"]="FAIL"
        ((failed_tests++))
        log_info "Error output: $output"
        return 1
    fi
}

test_url_accessibility() {
    local name="$1"
    local url="$2"
    local expected_status="${3:-200}"
    
    run_test_with_output \
        "$name URL Accessibility" \
        "curl -s -o /dev/null -w '%{http_code}' --max-time $TIMEOUT '$url'" \
        "^$expected_status$"
}

test_api_endpoint() {
    local endpoint="$1"
    local expected_status="${2:-200}"
    local method="${3:-GET}"
    
    local url="$API_BASE_URL$endpoint"
    
    run_test_with_output \
        "API $method $endpoint" \
        "curl -s -X $method -o /dev/null -w '%{http_code}' --max-time $TIMEOUT '$url'" \
        "^$expected_status$"
}

test_api_response_content() {
    local endpoint="$1"
    local expected_pattern="$2"
    local method="${3:-GET}"
    
    local url="$API_BASE_URL$endpoint"
    
    run_test_with_output \
        "API $method $endpoint Response Content" \
        "curl -s -X $method --max-time $TIMEOUT '$url'" \
        "$expected_pattern"
}

# =============================================================================
# Infrastructure Tests
# =============================================================================

test_dns_resolution() {
    log_info "Testing DNS Resolution..."
    
    # Extract hostname from URLs
    local frontend_host=$(echo "$FRONTEND_URL" | sed 's|https\?://||' | sed 's|/.*||')
    local backend_host=$(echo "$BACKEND_URL" | sed 's|https\?://||' | sed 's|/.*||')
    
    run_test \
        "Frontend DNS Resolution ($frontend_host)" \
        "nslookup $frontend_host"
    
    run_test \
        "Backend DNS Resolution ($backend_host)" \
        "nslookup $backend_host"
}

test_ssl_certificates() {
    log_info "Testing SSL Certificates..."
    
    # Extract hostname from URLs
    local frontend_host=$(echo "$FRONTEND_URL" | sed 's|https\?://||' | sed 's|/.*||')
    local backend_host=$(echo "$BACKEND_URL" | sed 's|https\?://||' | sed 's|/.*||')
    
    run_test \
        "Frontend SSL Certificate" \
        "echo | openssl s_client -servername $frontend_host -connect $frontend_host:443 2>/dev/null | openssl x509 -noout -dates"
    
    run_test \
        "Backend SSL Certificate" \
        "echo | openssl s_client -servername $backend_host -connect $backend_host:443 2>/dev/null | openssl x509 -noout -dates"
}

# =============================================================================
# Frontend Tests
# =============================================================================

test_frontend() {
    log_info "Testing Frontend Application..."
    
    # Test main frontend URL
    test_url_accessibility "Frontend Main Page" "$FRONTEND_URL"
    
    # Test frontend application structure
    run_test_with_output \
        "Frontend App Structure" \
        "curl -s --max-time $TIMEOUT '$FRONTEND_URL'" \
        "<!DOCTYPE html>.*<title>.*VESLINT.*</title>"
    
    # Test React app loading
    run_test_with_output \
        "React App Assets" \
        "curl -s --max-time $TIMEOUT '$FRONTEND_URL'" \
        "_next/static"
    
    # Test key frontend routes
    local routes=("/dashboard" "/new-analysis" "/login" "/register" "/about")
    for route in "${routes[@]}"; do
        test_url_accessibility "Frontend Route $route" "$FRONTEND_URL$route"
    done
    
    # Test static assets
    run_test_with_output \
        "Frontend Favicon" \
        "curl -s -o /dev/null -w '%{http_code}' --max-time $TIMEOUT '$FRONTEND_URL/favicon.ico'" \
        "200"
}

test_frontend_security_headers() {
    log_info "Testing Frontend Security Headers..."
    
    run_test_with_output \
        "X-Frame-Options Header" \
        "curl -s -I --max-time $TIMEOUT '$FRONTEND_URL'" \
        "X-Frame-Options:.*DENY"
    
    run_test_with_output \
        "X-Content-Type-Options Header" \
        "curl -s -I --max-time $TIMEOUT '$FRONTEND_URL'" \
        "X-Content-Type-Options:.*nosniff"
    
    run_test_with_output \
        "Referrer-Policy Header" \
        "curl -s -I --max-time $TIMEOUT '$FRONTEND_URL'" \
        "Referrer-Policy:"
}

# =============================================================================
# Backend API Tests
# =============================================================================

test_backend_health() {
    log_info "Testing Backend Health..."
    
    # Test health endpoints
    test_api_endpoint "/health" 200
    test_api_endpoint "/health/ready" 200
    test_api_endpoint "/health/live" 200
    
    # Test health response content
    test_api_response_content "/health" "status.*healthy"
}

test_backend_api_structure() {
    log_info "Testing Backend API Structure..."
    
    # Test API root
    test_api_endpoint "/" 200
    test_api_response_content "/" "VESLINT.*API"
    
    # Test API status
    test_api_endpoint "/status" 200
    test_api_response_content "/status" "overall_status"
    
    # Test API documentation (if available)
    test_api_endpoint "/docs" 200
    test_api_endpoint "/redoc" 200
    test_api_endpoint "/openapi.json" 200
}

test_backend_cors() {
    log_info "Testing Backend CORS Configuration..."
    
    # Test CORS preflight
    run_test_with_output \
        "CORS Preflight Request" \
        "curl -s -X OPTIONS -H 'Origin: $FRONTEND_URL' -H 'Access-Control-Request-Method: GET' -I --max-time $TIMEOUT '$API_BASE_URL/health'" \
        "Access-Control-Allow-Origin"
}

test_backend_authentication() {
    log_info "Testing Backend Authentication..."
    
    # Test protected endpoints return 401 without auth
    test_api_endpoint "/jobs" 401
    test_api_endpoint "/vessels" 401
    
    # Test auth endpoint exists
    test_api_endpoint "/auth" 404  # Should return 404 if not implemented
}

# =============================================================================
# Database Tests
# =============================================================================

test_database_connectivity() {
    log_info "Testing Database Connectivity..."
    
    # Test database status through API
    test_api_response_content "/status" "database.*healthy"
    
    # Test if API can connect to Supabase
    test_api_response_content "/status" "supabase"
}

# =============================================================================
# Integration Tests
# =============================================================================

test_frontend_backend_integration() {
    log_info "Testing Frontend-Backend Integration..."
    
    # Test if frontend can reach backend API
    run_test_with_output \
        "Frontend API Proxy" \
        "curl -s --max-time $TIMEOUT '$FRONTEND_URL/api/health'" \
        "status"
    
    # Test CORS between frontend and backend
    run_test_with_output \
        "Frontend-Backend CORS" \
        "curl -s -H 'Origin: $FRONTEND_URL' --max-time $TIMEOUT '$API_BASE_URL/health'" \
        "status"
}

test_file_upload_endpoint() {
    log_info "Testing File Upload Functionality..."
    
    # Create a small test CSV file
    local test_csv="/tmp/test_vessel_data.csv"
    cat > "$test_csv" << EOF
mmsi,timestamp,lat,lon,sog,cog,heading
123456789,2024-01-01T00:00:00Z,40.7128,-74.0060,10.5,45.0,90.0
987654321,2024-01-01T00:01:00Z,40.7138,-74.0070,12.3,50.0,95.0
EOF
    
    # Test upload endpoint (if it exists and doesn't require auth)
    run_test_with_output \
        "File Upload Endpoint Response" \
        "curl -s -X POST --max-time $TIMEOUT '$API_BASE_URL/vessels/upload'" \
        "401\\|400\\|405"  # Should return error without proper auth/data
    
    # Cleanup
    rm -f "$test_csv"
}

test_ml_model_availability() {
    log_info "Testing ML Model Availability..."
    
    # Test if ML service is available through status endpoint
    test_api_response_content "/status" "ml_service"
    
    # Test vessel classification endpoint
    test_api_endpoint "/vessels/classify" 401  # Should require auth
}

# =============================================================================
# Performance Tests
# =============================================================================

test_response_times() {
    log_info "Testing Response Times..."
    
    # Test frontend response time
    run_test_with_output \
        "Frontend Response Time < 3s" \
        "curl -s -o /dev/null -w '%{time_total}' --max-time 3 '$FRONTEND_URL'" \
        "^[0-2]\\."
    
    # Test backend API response time
    run_test_with_output \
        "Backend API Response Time < 2s" \
        "curl -s -o /dev/null -w '%{time_total}' --max-time 2 '$API_BASE_URL/health'" \
        "^[0-1]\\."
}

test_concurrent_requests() {
    log_info "Testing Concurrent Request Handling..."
    
    # Test multiple concurrent requests to health endpoint
    run_test \
        "Concurrent Health Checks" \
        "for i in {1..5}; do curl -s '$API_BASE_URL/health' & done; wait"
}

# =============================================================================
# Security Tests
# =============================================================================

test_security_headers() {
    log_info "Testing Security Headers..."
    
    # Test backend security headers
    run_test_with_output \
        "Backend Security Headers" \
        "curl -s -I --max-time $TIMEOUT '$API_BASE_URL/health'" \
        "X-Content-Type-Options\\|X-Frame-Options"
}

test_information_disclosure() {
    log_info "Testing Information Disclosure..."
    
    # Test that sensitive information is not exposed
    run_test_with_output \
        "No Debug Information Exposed" \
        "curl -s --max-time $TIMEOUT '$API_BASE_URL/health'" \
        "^((?!debug|traceback|error|exception).)*$"
    
    # Test for common sensitive paths
    test_api_endpoint "/.env" 404
    test_api_endpoint "/config" 404
    test_api_endpoint "/admin" 404
}

# =============================================================================
# Monitoring and Observability Tests
# =============================================================================

test_monitoring_endpoints() {
    log_info "Testing Monitoring Endpoints..."
    
    # Test metrics endpoint (if available)
    run_test_with_output \
        "Metrics Endpoint" \
        "curl -s -o /dev/null -w '%{http_code}' --max-time $TIMEOUT '$BACKEND_URL/metrics'" \
        "200\\|404"  # 404 is acceptable if metrics not enabled
}

# =============================================================================
# Deployment Verification
# =============================================================================

test_deployment_artifacts() {
    log_info "Testing Deployment Artifacts..."
    
    # Test build information
    test_api_response_content "/" "version"
    
    # Test deployment timestamp
    run_test_with_output \
        "Frontend Build Info" \
        "curl -s --max-time $TIMEOUT '$FRONTEND_URL'" \
        "build.*time\\|version"
}

# =============================================================================
# Test Suite Execution
# =============================================================================

run_all_tests() {
    log_info "Starting comprehensive deployment testing..."
    log_info "Environment: $ENVIRONMENT"
    log_info "Frontend URL: $FRONTEND_URL"
    log_info "Backend URL: $BACKEND_URL"
    log_info "API Base URL: $API_BASE_URL"
    log_info "Timeout: ${TIMEOUT}s"
    echo ""
    
    # Infrastructure Tests
    test_dns_resolution
    test_ssl_certificates
    
    # Frontend Tests
    test_frontend
    test_frontend_security_headers
    
    # Backend Tests
    test_backend_health
    test_backend_api_structure
    test_backend_cors
    test_backend_authentication
    
    # Database Tests
    test_database_connectivity
    
    # Integration Tests
    test_frontend_backend_integration
    test_file_upload_endpoint
    test_ml_model_availability
    
    # Performance Tests
    test_response_times
    test_concurrent_requests
    
    # Security Tests
    test_security_headers
    test_information_disclosure
    
    # Monitoring Tests
    test_monitoring_endpoints
    
    # Deployment Tests
    test_deployment_artifacts
}

generate_test_report() {
    log_info "Generating test report..."
    
    echo ""
    echo "=============================================="
    echo "🧪 VESLINT Deployment Test Report"
    echo "=============================================="
    echo "Environment: $ENVIRONMENT"
    echo "Test Date: $(date)"
    echo "Frontend URL: $FRONTEND_URL"
    echo "Backend URL: $BACKEND_URL"
    echo ""
    echo "Test Summary:"
    echo "  Total Tests: $total_tests"
    echo "  Passed: $passed_tests"
    echo "  Failed: $failed_tests"
    echo "  Warnings: $warnings"
    echo ""
    
    if [ $failed_tests -eq 0 ]; then
        echo "🎉 All tests passed! Deployment is healthy."
        echo ""
        echo "✅ Frontend is accessible and working"
        echo "✅ Backend API is responding correctly"
        echo "✅ Database connectivity is established"
        echo "✅ Security headers are configured"
        echo "✅ Integration between services is working"
    else
        echo "⚠️  Some tests failed. Please review:"
        echo ""
        for test_name in "${!test_results[@]}"; do
            if [ "${test_results[$test_name]}" = "FAIL" ]; then
                echo "❌ $test_name"
            fi
        done
    fi
    
    echo ""
    echo "Detailed Results:"
    for test_name in "${!test_results[@]}"; do
        local status="${test_results[$test_name]}"
        if [ "$status" = "PASS" ]; then
            echo "✅ $test_name"
        else
            echo "❌ $test_name"
        fi
    done
    
    echo ""
    echo "Next Steps:"
    if [ $failed_tests -eq 0 ]; then
        echo "1. Monitor application performance and logs"
        echo "2. Set up automated monitoring and alerting"
        echo "3. Perform user acceptance testing"
        echo "4. Update documentation with deployment details"
    else
        echo "1. Review failed tests and fix issues"
        echo "2. Check application logs for errors"
        echo "3. Verify environment configuration"
        echo "4. Re-run tests after fixes"
    fi
    
    echo "=============================================="
}

# =============================================================================
# Main Execution
# =============================================================================

main() {
    case "$1" in
        "help"|"-h"|"--help")
            echo "VESLINT Deployment Testing Script"
            echo ""
            echo "Usage: $0 [environment] [timeout] [verbose]"
            echo ""
            echo "Arguments:"
            echo "  environment - Test environment (development|staging|production) [default: production]"
            echo "  timeout     - Request timeout in seconds [default: 30]"
            echo "  verbose     - Enable verbose output (true|false) [default: false]"
            echo ""
            echo "Environment Variables:"
            echo "  FRONTEND_URL - Frontend application URL [default: https://veslint.com]"
            echo "  BACKEND_URL  - Backend API URL [default: https://veslint-api.onrender.com]"
            echo "  API_BASE_URL - API base URL [default: \$BACKEND_URL/api/v1]"
            echo ""
            echo "Examples:"
            echo "  $0                    # Test production deployment"
            echo "  $0 staging           # Test staging deployment"
            echo "  $0 production 60     # Test with 60s timeout"
            echo "  $0 staging 30 true   # Test staging with verbose output"
            exit 0
            ;;
        *)
            # Check prerequisites
            if ! command -v curl &> /dev/null; then
                log_error "curl is required but not installed"
                exit 1
            fi
            
            if ! command -v nslookup &> /dev/null; then
                log_warning "nslookup not available, skipping DNS tests"
            fi
            
            if ! command -v openssl &> /dev/null; then
                log_warning "openssl not available, skipping SSL tests"
            fi
            
            # Run all tests
            run_all_tests
            
            # Generate report
            generate_test_report
            
            # Exit with appropriate code
            if [ $failed_tests -eq 0 ]; then
                exit 0
            else
                exit 1
            fi
            ;;
    esac
}

# Execute main function
main "$@"
